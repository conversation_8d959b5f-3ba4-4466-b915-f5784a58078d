"use client";
import React, { useState } from "react";
import CreateTrackSheet from "./createTrackSheet";
import { But<PERSON> } from "@/components/ui/button";
import { TrackSheetContext } from "./TrackSheetContext";
import Sidebar from "@/components/sidebar/Sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import ClientSelectPage from "./ClientSelectPage";

const ManageWorkSheet = ({
  permissions,
  client,
  carrier,
  associate,
  userData,
  actions,
}: any) => {
  const [filterdata, setFilterData] = useState([]);
  const [deleteData, setDeletedData] = useState(false);
  const [activeView, setActiveView] = useState("view"); 
  return (
    <TrackSheetContext.Provider
      value={{
        filterdata,
        setFilterData,
        deleteData,
        setDeletedData,
      }}
    >
      <SidebarProvider>
        <div className="flex w-full min-h-screen">
        <Sidebar {...{ permissions, profile: userData }} />
          <main className="flex-1 w-full pl-3 overflow-auto">
            <div className="mb-6">
              <BreadCrumbs
                breadcrumblist={[
                  { link: "/user/trackSheets", name: "TrackSheet" },
                ]}
              />
            </div>

            <div className="flex gap-4 pl-3 mb-6">
              <Button
                variant="default"
                onClick={() => setActiveView("view")}
                className={`w-40 shadow-md rounded-xl text-base transition-all duration-200 ${
                  activeView === "view"
                    ? "bg-neutral-800 hover:bg-neutral-900 text-white"
                    : "bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100"
                }`}
              >
                View TrackSheet
              </Button>
              <Button
                variant="default"
                onClick={() => setActiveView("create")}
                className={`w-40 shadow-md rounded-xl text-base transition-all duration-200 ${
                  activeView === "create"
                    ? "bg-neutral-800 hover:bg-neutral-900 text-white"
                    : "bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100"
                }`}
              >
                Create TrackSheet
              </Button>
            </div>

            <div className="w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-1">
              {activeView === "create" ? (
                <CreateTrackSheet client={client} carrier={carrier} associate={associate} userData={userData} />
              ) : (
                <ClientSelectPage
                  permissions={actions}
                  client={client}
                />
              )}
            </div>
          </main>
        </div>
      </SidebarProvider>
    </TrackSheetContext.Provider>
  );
};

export default ManageWorkSheet;