"use client";
import React, { useState } from "react";
import CreateTrackSheet from "./createTrackSheet";
import { TrackSheetContext } from "./TrackSheetContext";
import Sidebar from "@/components/sidebar/Sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import BreadCrumbs from "@/app/_component/BreadCrumbs";

const ManageWorkSheet = ({
  permissions,
  client,
  carrier,
  associate,
  userData,
  actions,
}: any) => {
  const [filterdata, setFilterData] = useState([]);
  const [deleteData, setDeletedData] = useState(false);
  const [activeView, setActiveView] = useState("view"); 
  return (
    <TrackSheetContext.Provider
      value={{
        filterdata,
        setFilterData,
        deleteData,
        setDeletedData,
      }}
    >
      <SidebarProvider>
        <div className="flex w-full min-h-screen">
        <Sidebar {...{ permissions, profile: userData }} />
          <main className="flex-1 w-full pl-3 overflow-auto">
            <div className="mb-6">
              <BreadCrumbs
                breadcrumblist={[
                  { link: "/user/trackSheets", name: "TrackSheet" },
                ]}
              />
            </div>

            <CreateTrackSheet
              client={client}
              carrier={carrier}
              associate={associate}
              userData={userData}
              activeView={activeView}
              setActiveView={setActiveView}
              permissions={actions}
            />
          </main>
        </div>
      </SidebarProvider>
    </TrackSheetContext.Provider>
  );
};

export default ManageWorkSheet;