"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSuperAdmin = void 0;
const operation_1 = require("../../utils/operation");
const createSuperAdmin = async (req, res) => {
    const fields = {
        username: req.body.username,
        email: req.body.email,
        password: req.body.password
    };
    await (0, operation_1.createItem)({
        model: "superAdmin",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "Super Admin has been created"
    });
};
exports.createSuperAdmin = createSuperAdmin;
//# sourceMappingURL=create.js.map