import React from 'react';

interface ComboboxDemoProps {
  data: Array<{ notes_id: string; note: string }>;
  value: string;
  setValue: (value: string) => void;
  placeholder?: string;
  label?: string;
  isEntryPage?: boolean;
}

const ComboboxDemo: React.FC<ComboboxDemoProps> = ({
  data,
  value,
  setValue,
  placeholder,
  label,
  isEntryPage,
}) => {
  return (
    <div>
      {label && <label>{label}</label>}
      <select
        value={value}
        onChange={(e) => setValue(e.target.value)}

      >
        <option value="">{placeholder}</option>
        {data.map((item) => (
          <option key={item.notes_id} value={item.notes_id}>
            {item.note}
          </option>
        ))}
      </select>
    </div>
  );
};

export default ComboboxDemo;
