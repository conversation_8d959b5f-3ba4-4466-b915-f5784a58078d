"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.exportCustomizeReport = void 0;
const xlsx_1 = __importDefault(require("xlsx"));
const helpers_1 = require("../../../utils/helpers");
const library_1 = require("@prisma/client/runtime/library");
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const canvas_1 = require("canvas");
const visibiltyHelper_1 = require("../visibilty/visibiltyHelper");
const exportCustomizeReport = async (req, res) => {
    const formatFilename = () => {
        const now = new Date();
        const dd = String(now.getDate()).padStart(2, "0");
        const mm = String(now.getMonth() + 1).padStart(2, "0");
        const yyyy = now.getFullYear();
        const hours = String(now.getHours()).padStart(2, "0");
        const minutes = String(now.getMinutes()).padStart(2, "0");
        return `Work-Report_${dd}${mm}${yyyy}-${hours}${minutes}.xlsx`;
    };
    try {
        const userId = req.user_id;
        const { Username, fDate, tDate, Category, Client, screenshot, isCorporation, } = req.query;
        const from = fDate ? new Date(fDate) : null;
        const to = tDate ? new Date(tDate) : null;
        if (to)
            to.setHours(23, 59, 59, 999);
        const user_ids = await (0, visibiltyHelper_1.getVisibility)(userId, "DailyPlanning");
        if (!Array.isArray(user_ids)) {
            return res.status(400).json({ message: "Invalid user_ids format" });
        }
        const userIdArray = [...new Set(user_ids.map((u) => u.user_id))];
        const whereClause = {
            AND: [],
            // , user_id: { in: userIdArray }
        };
        if (from || to) {
            const dateCondition = { date: {} };
            if (from)
                dateCondition.date.gte = from;
            if (to)
                dateCondition.date.lte = to;
            whereClause.AND.push(dateCondition);
        }
        const searchConditions = [];
        if (Username) {
            const users = Username.split(",").map((x) => x.trim());
            // If only one username, do exact match
            if (users.length === 1) {
                searchConditions.push({
                    user: { username: { equals: users[0], mode: "insensitive" } },
                });
            }
            else {
                // Multiple usernames — use contains
                searchConditions.push({
                    OR: users.map((u) => ({
                        user: { username: { contains: u, mode: "insensitive" } },
                    })),
                });
            }
        }
        if (Category) {
            const categoryList = Category.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: categoryList.map((category) => ({
                    category: {
                        category_name: { contains: category, mode: "insensitive" },
                    },
                })),
            });
        }
        if (Client) {
            const clientList = Client.split(",").map((item) => item.trim());
            searchConditions.push({
                OR: clientList.map((client) => ({
                    client: {
                        client_name: { contains: client, mode: "insensitive" },
                    },
                })),
            });
        }
        if (searchConditions.length > 0) {
            whereClause.AND.push({ AND: searchConditions });
        }
        const data = await prismaClient_1.default.workReport.findMany({
            where: whereClause,
            include: { user: true, category: true, client: true },
            orderBy: { id: "desc" },
        });
        const formatDate = (dateString) => {
            if (!dateString)
                return "N/A";
            const date = new Date(dateString);
            return `${String(date.getDate()).padStart(2, "0")}-${String(date.getMonth() + 1).padStart(2, "0")}-${date.getFullYear()}`;
        };
        const getFormattedDateRange = (fromDate, toDate) => {
            if (!fromDate || !toDate)
                return "";
            return `${formatDate(fromDate)} to ${formatDate(toDate)}`;
        };
        const formatDuration = (timeSpentInMinutes) => {
            if (timeSpentInMinutes == null || isNaN(timeSpentInMinutes))
                return "00:00:00";
            const hours = Math.floor(timeSpentInMinutes / 60);
            const minutes = Math.floor(timeSpentInMinutes % 60);
            const seconds = Math.round((timeSpentInMinutes % 1) * 60);
            return `${String(hours).padStart(2, "0")}:${String(minutes).padStart(2, "0")}:${String(seconds).padStart(2, "0")}`;
        };
        let formattedData = [];
        if (from && to) {
            const groupedData = data.reduce((acc, item) => {
                const dateRange = `${formatDate(from)} to ${formatDate(to)}`;
                const username = item.user?.username || "N/A";
                const clientName = item.client?.client_name || "N/A";
                const categoryName = item.category?.category_name || "N/A";
                const key = Client
                    ? `${username}_${clientName}_${categoryName}`
                    : `${username}_${categoryName}`;
                if (!acc[key]) {
                    acc[key] = {
                        date: dateRange,
                        username: username,
                        client_name: Client ? clientName : "",
                        category_name: categoryName,
                        count: 1,
                        time_spent: item.time_spent instanceof library_1.Decimal
                            ? item.time_spent.toNumber()
                            : item.time_spent || 0,
                        actual_number: item.actual_number || 0,
                    };
                }
                else {
                    acc[key].count += 1;
                    acc[key].time_spent +=
                        item.time_spent instanceof library_1.Decimal
                            ? item.time_spent.toNumber()
                            : item.time_spent || 0;
                    acc[key].actual_number += item.actual_number || 0;
                }
                return acc;
            }, {});
            formattedData = Object.values(groupedData).map((group) => ({
                date: group.date,
                username: group.username,
                ...(Client ? { client_name: `${group.client_name}` } : {}),
                category_name: `${group.category_name}`,
                actual_number: group.actual_number,
                time_spent: formatDuration(group.time_spent),
            }));
        }
        else {
            const groupedData = data.reduce((acc, item) => {
                const date = formatDate(item.date);
                const username = item.user?.username || "N/A";
                const clientName = item.client?.client_name || "N/A";
                const categoryName = item.category?.category_name || "N/A";
                // Create unique key based on whether client is selected
                const key = Client
                    ? `${date}_${username}_${clientName}_${categoryName}`
                    : `${date}_${username}_${categoryName}`;
                if (!acc[key]) {
                    acc[key] = {
                        date: date,
                        username: username,
                        client_name: Client ? clientName : "",
                        category_name: categoryName,
                        count: 1,
                        time_spent: item.time_spent instanceof library_1.Decimal
                            ? item.time_spent.toNumber()
                            : item.time_spent || 0,
                        actual_number: item.actual_number || 0,
                    };
                }
                else {
                    acc[key].count += 1;
                    acc[key].time_spent +=
                        item.time_spent instanceof library_1.Decimal
                            ? item.time_spent.toNumber()
                            : item.time_spent || 0;
                    acc[key].actual_number += item.actual_number || 0;
                }
                return acc;
            }, {});
            // Convert grouped data to rows and sort by date
            formattedData = Object.values(groupedData)
                .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
                .map((group) => ({
                date: group.date,
                username: group.username,
                ...(Client ? { client_name: `${group.client_name}` } : {}),
                category_name: `${group.category_name}`,
                actual_number: group.actual_number,
                time_spent: formatDuration(group.time_spent),
            }));
        }
        // Calculate totals
        let totalTimeSpentMinutes = 0;
        let totalActualNumber = 0;
        formattedData.forEach((item) => {
            const timeSpent = typeof item.time_spent === "string"
                ? convertTimeToMinutes(item.time_spent)
                : item.time_spent;
            totalTimeSpentMinutes += timeSpent;
            totalActualNumber += item.actual_number;
        });
        let totalTimeFormatted = formatDuration(totalTimeSpentMinutes);
        // Calculate the data length before adding the total row
        const dataLength = formattedData.length;
        // Check if this is a data length check request
        if (req.query.checkDataLength === "true") {
            return res.status(200).json({
                success: true,
                dataLength: dataLength,
            });
        }
        if (data.length === 0) {
            const noDataMessage = "No data available for the selected filters.";
            if (screenshot === "true") {
                const canvas = (0, canvas_1.createCanvas)(800, 200);
                const ctx = canvas.getContext("2d");
                ctx.fillStyle = "#fff";
                ctx.fillRect(0, 0, 800, 200);
                ctx.fillStyle = "#000";
                ctx.font = "20px sans-serif"; // Default font styling
                ctx.fillText(noDataMessage, 50, 100);
                const buffer = canvas.toBuffer("image/png");
                res.setHeader("Content-Type", "image/png");
                res.setHeader("Content-Disposition", 'attachment; filename="Work_Report_Summary.png"');
                return res.send(buffer);
            }
            else {
                const ws = xlsx_1.default.utils.aoa_to_sheet([[noDataMessage]]);
                const wb = xlsx_1.default.utils.book_new();
                xlsx_1.default.utils.book_append_sheet(wb, ws, "Work Report");
                const excelBuffer = xlsx_1.default.write(wb, {
                    bookType: "xlsx",
                    type: "buffer",
                });
                res.setHeader("Content-Disposition", 'attachment; filename="Work_Report_Summary.xlsx"');
                res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                return res.send(excelBuffer);
            }
        }
        // Add total row
        formattedData.push({
            date: "Total:",
            username: "",
            ...(Client ? { client_name: "" } : {}),
            category_name: "",
            actual_number: totalActualNumber,
            time_spent: totalTimeFormatted,
        });
        // Helper function to convert HH:MM:SS to minutes
        function convertTimeToMinutes(timeString) {
            const [hours, minutes, seconds] = timeString.split(":").map(Number);
            return hours * 60 + minutes + seconds / 60;
        }
        // Determine if date column should be hidden (when from and to dates are provided)
        const hideDateColumn = from && to;
        const ws = xlsx_1.default.utils.json_to_sheet(formattedData.map((item) => {
            let processedItem = { ...item };
            if (hideDateColumn) {
                const { date, ...withoutDate } = processedItem;
                processedItem = withoutDate;
            }
            if (!Client) {
                const { client_name, ...withoutClient } = processedItem;
                processedItem = withoutClient;
            }
            return processedItem;
        }));
        let headers = [];
        if (Client) {
            headers = hideDateColumn
                ? [["Username", "Client", "Category", "Actual Number", "Time Spent"]]
                : [
                    [
                        "Date",
                        "Username",
                        "Client",
                        "Category",
                        "Actual Number",
                        "Time Spent",
                    ],
                ];
        }
        else {
            headers = hideDateColumn
                ? [["Username", "Category", "Actual Number", "Time Spent"]]
                : [["Date", "Username", "Category", "Actual Number", "Time Spent"]];
        }
        xlsx_1.default.utils.sheet_add_aoa(ws, headers, { origin: "A1" });
        const range = xlsx_1.default.utils.decode_range(ws["!ref"]);
        for (let col = range.s.c; col <= range.e.c; col++) {
            const cellRef = xlsx_1.default.utils.encode_cell({ r: 0, c: col });
            ws[cellRef].s = {
                font: { bold: true },
                alignment: { horizontal: "center" },
            };
        }
        if (hideDateColumn || !Client) {
            for (let row = 1; row <= range.e.r; row++) {
                for (let col = range.s.c; col <= range.e.c; col++) {
                    const cellRef = xlsx_1.default.utils.encode_cell({
                        r: row,
                        c: col,
                    });
                    if (ws[cellRef]) {
                        ws[cellRef].s = {
                            alignment: { horizontal: "center" },
                        };
                    }
                }
            }
        }
        else {
            // Only center align actual number column if date column is shown and client is selected
            let actualNumberColumnIndex;
            if (Client) {
                actualNumberColumnIndex = 4; // With client, with date
            }
            else {
                actualNumberColumnIndex = 3; // Without client, with date
            }
            for (let row = 1; row <= range.e.r; row++) {
                const actualNumberCellRef = xlsx_1.default.utils.encode_cell({
                    r: row,
                    c: actualNumberColumnIndex,
                });
                if (ws[actualNumberCellRef]) {
                    ws[actualNumberCellRef].s = {
                        alignment: { horizontal: "center" },
                    };
                }
            }
        }
        // Adjust column widths based on whether client is included and whether date column is hidden
        if (Client) {
            if (hideDateColumn) {
                ws["!cols"] = [
                    { wch: 15 }, // Username
                    { wch: 15 }, // Client
                    { wch: 30 }, // Category - increased width from 20 to 30
                    { wch: 15 }, // Actual Number
                    { wch: 12 }, // Time Spent
                ];
            }
            else {
                ws["!cols"] = [
                    { wch: 22 }, // Date
                    { wch: 15 }, // Username
                    { wch: 15 }, // Client
                    { wch: 30 }, // Category - increased width from 20 to 30
                    { wch: 15 }, // Actual Number
                    { wch: 12 }, // Time Spent
                ];
            }
        }
        else {
            if (hideDateColumn) {
                ws["!cols"] = [
                    { wch: 15 }, // Username
                    { wch: 30 }, // Category - increased width from 20 to 30
                    { wch: 15 }, // Actual Number
                    { wch: 12 }, // Time Spent
                ];
            }
            else {
                // Client not included, date shown
                ws["!cols"] = [
                    { wch: 22 }, // Date
                    { wch: 15 }, // Username
                    { wch: 30 }, // Category - increased width from 20 to 30
                    { wch: 15 }, // Actual Number
                    { wch: 12 }, // Time Spent
                ];
            }
        }
        const isSingleUsername = Username && !Username.includes(",");
        if (screenshot === "true") {
            const actualDataLength = formattedData.length - 1;
            if (req.query.checkDataLength === "true") {
                return res.status(200).json({
                    success: true,
                    dataLength: actualDataLength,
                });
            }
            // For screenshot generation, limit to 100 entries in the table
            if (actualDataLength > 100) {
                const totalRow = formattedData.pop();
                // Limit formattedData to 100 entries
                formattedData = formattedData.slice(0, 100);
                // Recalculate totals based on the limited data
                let totalTimeSpentMinutes = 0;
                let totalActualNumber = 0;
                formattedData.forEach((item) => {
                    const timeSpent = typeof item.time_spent === "string"
                        ? convertTimeToMinutes(item.time_spent)
                        : item.time_spent;
                    totalTimeSpentMinutes += timeSpent;
                    totalActualNumber += item.actual_number;
                });
                totalRow.actual_number = totalActualNumber;
                totalRow.time_spent = formatDuration(totalTimeSpentMinutes);
                formattedData.push(totalRow);
            }
            const uniqueUsers = [
                ...new Set(formattedData.slice(0, -1).map((item) => item.username)),
            ].length;
            const uniqueClients = [
                ...new Set(formattedData.slice(0, -1).map((item) => item.client_name)),
            ].length;
            const uniqueCategories = [
                ...new Set(formattedData.slice(0, -1).map((item) => item.category_name)),
            ].length;
            const totalEntries = formattedData.length - 1;
            const rowHeight = 30;
            const headerHeight = 210;
            const statsCardsHeight = 100;
            const tableHeaderHeight = 30;
            const totalRowHeight = 30;
            const padding = 50;
            const dataRowsHeight = (formattedData.length - 1) * rowHeight;
            const canvasHeight = headerHeight +
                statsCardsHeight +
                tableHeaderHeight +
                dataRowsHeight +
                totalRowHeight +
                padding;
            const canvasWidth = 1050;
            const canvas = (0, canvas_1.createCanvas)(canvasWidth, canvasHeight);
            const ctx = canvas.getContext("2d");
            const gradient = ctx.createLinearGradient(0, 0, 0, canvasHeight);
            gradient.addColorStop(0, "#f8f9fa");
            gradient.addColorStop(1, "#f1f3f5");
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvasWidth, canvasHeight);
            ctx.strokeStyle = "#e9ecef";
            ctx.lineWidth = 2;
            ctx.strokeRect(10, 10, canvasWidth - 20, canvasHeight - 20);
            ctx.shadowColor = "rgba(0, 0, 0, 0.15)";
            ctx.shadowBlur = 12;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 5;
            ctx.fillStyle = "#ffffff";
            ctx.fillRect(50, 50, canvasWidth - 100, 150);
            ctx.shadowColor = "transparent";
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            ctx.strokeStyle = "#4834b5";
            ctx.lineWidth = 2;
            ctx.strokeRect(60, 65, 25, 30);
            ctx.beginPath();
            ctx.moveTo(65, 75);
            ctx.lineTo(80, 75);
            ctx.moveTo(65, 80);
            ctx.lineTo(80, 80);
            ctx.moveTo(65, 85);
            ctx.lineTo(75, 85);
            ctx.stroke();
            const part1 = "Oi";
            const part2 = "360";
            const part3 = " Custom Reports";
            ctx.font = "bold 30px Arial,sans-serif";
            const part1Width = ctx.measureText(part1).width;
            const part2Width = ctx.measureText(part2).width;
            const part3Width = ctx.measureText(part3).width;
            const totalWidth = part1Width + part2Width + part3Width;
            const headerCenterX = (canvasWidth - 100) / 2 + 50;
            const startX = headerCenterX - totalWidth / 2;
            ctx.fillStyle = "#0099FF";
            ctx.fillText(part1, startX, 80);
            ctx.fillStyle = "#000000";
            ctx.fillText(part2, startX + part1Width, 80);
            ctx.fillStyle = "#000000";
            ctx.fillText(part3, startX + part1Width + part2Width, 80);
            ctx.shadowColor = "rgba(0, 0, 0, 0.08)";
            ctx.shadowBlur = 3;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 2;
            ctx.shadowColor = "transparent";
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            ctx.font = "12px Arial,sans-serif";
            ctx.fillStyle = "#000";
            const currentDate = new Date();
            const formattedDate = `${currentDate
                .getDate()
                .toString()
                .padStart(2, "0")}-${(currentDate.getMonth() + 1)
                .toString()
                .padStart(2, "0")}-${currentDate.getFullYear()}`;
            const generatedFor = isCorporation
                ? "ADMIN"
                : Username && Username.trim()
                    ? Username
                    : "ADMIN";
            const centerX = canvasWidth / 2;
            const headerY = 130;
            const headerFont = "Arial,sans-serif";
            const headerBoldFont = `bold 12px ${headerFont}`;
            const headerRegularFont = `12px ${headerFont}`;
            ctx.font = headerBoldFont;
            ctx.textAlign = "left";
            ctx.fillStyle = "#000000";
            ctx.font = headerBoldFont;
            const generatedByText = "Generated By: ";
            ctx.fillText(generatedByText, 70, headerY);
            const genByWidth = ctx.measureText(generatedByText).width;
            const adminX = 70 + genByWidth;
            ctx.fillStyle = "#000000";
            ctx.font = headerRegularFont;
            ctx.fillText(generatedFor, adminX, headerY);
            const boldPart = "This is a Custom Report Generated For: ";
            let userPart = "All Users";
            if (Username && Username.trim()) {
                const usernames = Username.split(",").map((name) => name.trim());
                if (usernames.length <= 3) {
                    userPart = Username;
                }
                else {
                    userPart = `${usernames.slice(0, 3).join(", ")}...`;
                }
            }
            ctx.font = headerBoldFont;
            const boldPartWidth = ctx.measureText(boldPart).width;
            ctx.font = headerRegularFont;
            const userPartWidth = ctx.measureText(userPart).width;
            const reportTotalWidth = boldPartWidth + userPartWidth;
            const reportX = centerX - reportTotalWidth / 2;
            ctx.fillStyle = "#000000";
            ctx.font = headerBoldFont;
            ctx.fillText(boldPart, reportX, headerY);
            ctx.fillStyle = "#000000";
            ctx.font = headerRegularFont;
            ctx.fillText(userPart, reportX + boldPartWidth, headerY);
            ctx.textAlign = "right";
            ctx.fillStyle = "#000000";
            ctx.font = headerBoldFont;
            const dateLabel = "Generated Date: ";
            ctx.font = headerRegularFont;
            const dateWidth = ctx.measureText(formattedDate).width;
            ctx.font = headerBoldFont;
            ctx.fillText(dateLabel, canvasWidth - 70 - dateWidth, headerY);
            ctx.fillStyle = "#000000";
            ctx.font = headerRegularFont;
            ctx.fillText(formattedDate, canvasWidth - 70, headerY);
            ctx.textAlign = "left";
            const leftMarginAlignment = 70;
            let yPosition = headerY + 20;
            let dateRangeShown = false;
            if (from || to) {
                dateRangeShown = true;
                ctx.fillStyle = "#FFFFFF";
                const dateRangeLabel = "Date Range: ";
                const dateRangeValue = `${from ? formatDate(from) : "All"} to ${to ? formatDate(to) : "All"}`;
                ctx.fillStyle = "#000000";
                ctx.font = "bold 12px Arial,sans-serif";
                ctx.fillText(dateRangeLabel, leftMarginAlignment, yPosition);
                const labelWidth = ctx.measureText(dateRangeLabel).width;
                ctx.fillStyle = "#000000";
                ctx.font = "12px Arial,sans-serif";
                ctx.fillText(dateRangeValue, leftMarginAlignment + labelWidth, yPosition);
            }
            const filterYPosition = dateRangeShown ? yPosition + 25 : yPosition;
            let hasClientFilter = false;
            let clientLabel = "Clients: ";
            let clientValue = "All Clients ";
            if (Client) {
                hasClientFilter = true;
                const clientList = Client.split(",").map((c) => c.trim());
                if (clientList.length === 1 && clientList[0].toLowerCase() === "all") {
                    clientValue = "All Clients ";
                }
                else if (clientList.length <= 10) {
                    clientValue = clientList.join(", ");
                }
                else {
                    clientValue = `All Clients `;
                }
            }
            let categoryFilter = "";
            if (!Category || Category.toLowerCase() === "all" || Category === "") {
                categoryFilter = "Categories: All Categories";
            }
            else {
                const catList = Category.split(",").map((c) => c.trim());
                if (catList.length > 3) {
                    categoryFilter = `Categories: ${catList.slice(0, 3).join(", ")}...`;
                }
                else {
                    categoryFilter = `Categories: ${catList.join(", ")}`;
                }
            }
            let userLabel = "Users: ";
            let userValue = "All Users ";
            if (Username && Username.trim() && Username !== "all") {
                const userList = Username.split(",").map((u) => u.trim());
                if (userList.length > 3) {
                    userValue = `${userList.slice(0, 3).join(", ")}...`;
                }
                else {
                    userValue = userList.join(", ");
                }
            }
            const filtersText = "Filters Applied:";
            ctx.fillStyle = "#000000";
            ctx.font = "bold 12px Arial,sans-serif";
            ctx.fillText(filtersText, leftMarginAlignment, filterYPosition);
            const labelWidth = ctx.measureText("Filters Applied:").width;
            let currentX = leftMarginAlignment + labelWidth + 5;
            ctx.font = "bold 12px Arial,sans-serif";
            ctx.fillStyle = "#e74c3c";
            ctx.fillText(userLabel, currentX, filterYPosition);
            let userLabelWidth = ctx.measureText(userLabel).width;
            ctx.font = "12px Arial,sans-serif";
            ctx.fillStyle = "#000000";
            ctx.fillText(userValue, currentX + userLabelWidth, filterYPosition);
            currentX += ctx.measureText(userLabel + userValue).width;
            if (hasClientFilter) {
                ctx.fillStyle = "#000000";
                ctx.font = "12px Arial,sans-serif";
                ctx.fillText(" ,  ", currentX, filterYPosition);
                currentX += ctx.measureText(" ,  ").width;
                ctx.font = "bold 12px Arial,sans-serif";
                ctx.fillStyle = "#f39c12";
                ctx.fillText(clientLabel, currentX, filterYPosition);
                let clientLabelWidth = ctx.measureText(clientLabel).width;
                ctx.font = "12px Arial,sans-serif";
                ctx.fillStyle = "#000000";
                ctx.fillText(clientValue, currentX + clientLabelWidth, filterYPosition);
                currentX += ctx.measureText(clientLabel + clientValue).width;
            }
            if (categoryFilter) {
                ctx.fillStyle = "#000000";
                ctx.font = "12px Arial,sans-serif";
                ctx.fillText(" ,  ", currentX, filterYPosition);
                currentX += ctx.measureText(" ,  ").width;
                const catLabel = categoryFilter.startsWith("Categories:")
                    ? "Categories: "
                    : "";
                const catValue = categoryFilter.replace("Categories:", "");
                ctx.font = "bold 12px Arial,sans-serif";
                ctx.fillStyle = "#9b59b6";
                ctx.fillText(catLabel, currentX, filterYPosition);
                let catLabelWidth = ctx.measureText(catLabel).width;
                ctx.font = "12px Arial,sans-serif";
                ctx.fillStyle = "#000000";
                ctx.fillText(catValue, currentX + catLabelWidth, filterYPosition);
            }
            const numCards = Client ? 6 : 5;
            const leftMargin = 50;
            const standardCardWidth = 100;
            const wideCardWidth = 230;
            const cardHeight = 85;
            const cardSpacing = 10;
            const adjustColor = (color, amount) => {
                let r = parseInt(color.substring(1, 3), 16);
                let g = parseInt(color.substring(3, 5), 16);
                let b = parseInt(color.substring(5, 7), 16);
                r = Math.max(0, Math.min(255, r + amount));
                g = Math.max(0, Math.min(255, g + amount));
                b = Math.max(0, Math.min(255, b + amount));
                return `#${r.toString(16).padStart(2, "0")}${g
                    .toString(16)
                    .padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
            };
            const startY = from || to ? 220 : 220;
            const drawCard = (x, y, color, title, subtitle, value, width) => {
                ctx.save();
                ctx.shadowColor = "rgba(0, 0, 0, 0.15)";
                ctx.shadowBlur = 10;
                ctx.shadowOffsetX = 0;
                ctx.shadowOffsetY = 4;
                const gradient = ctx.createLinearGradient(x, y, x, y + cardHeight);
                gradient.addColorStop(0, color);
                gradient.addColorStop(1, adjustColor(color, -20));
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.roundRect(x, y, width, cardHeight, 10);
                ctx.fill();
                ctx.strokeStyle = "rgba(255, 255, 255, 0.3)";
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.roundRect(x + 1, y + 1, width - 2, cardHeight - 2, 9);
                ctx.stroke();
                ctx.restore();
                ctx.fillStyle = "#ffffff";
                ctx.textAlign = "center";
                const centerX = x + width / 2;
                ctx.font = "bold 12px Arial,sans-serif";
                if (subtitle) {
                    ctx.fillText(title, centerX, y + 20);
                    ctx.font = "12px Arial,sans-serif";
                    ctx.fillText(subtitle, centerX, y + 35);
                }
                else {
                    ctx.fillText(title, centerX, y + 28);
                }
                ctx.font = "bold 26px Arial,sans-serif";
                ctx.fillText(value, centerX, y + 60);
                ctx.textAlign = "left";
            };
            const cardPositions = [];
            const cardWidths = [];
            const standardCardCount = Client ? 4 : 3;
            for (let i = 0; i < standardCardCount; i++) {
                cardWidths.push(standardCardWidth);
            }
            cardWidths.push(wideCardWidth);
            cardWidths.push(wideCardWidth);
            let cardPositionX = leftMargin;
            for (let i = 0; i < numCards; i++) {
                cardPositions.push(cardPositionX);
                cardPositionX += cardWidths[i] + cardSpacing;
            }
            const totalCardsWidth = cardWidths.reduce((sum, width) => sum + width, 0) +
                (numCards - 1) * cardSpacing;
            const centeringOffset = Math.floor((canvasWidth - totalCardsWidth) / 2) - leftMargin;
            for (let i = 0; i < cardPositions.length; i++) {
                cardPositions[i] += centeringOffset;
            }
            const cardConfig = [
                {
                    color: "#A2C7E1", // Light blue
                    title: "Total",
                    subtitle: "Entries",
                    value: totalEntries.toString(),
                },
                {
                    color: "#F5A7A3", // Light red
                    title: "Users",
                    subtitle: null,
                    value: uniqueUsers.toString(),
                },
            ];
            if (Client) {
                cardConfig.push({
                    color: "#F9D480", // Light amber
                    title: "Clients",
                    subtitle: null,
                    value: uniqueClients.toString(),
                });
            }
            cardConfig.push({
                color: "#D5A7E3", // Light purple
                title: "Categories",
                subtitle: null,
                value: uniqueCategories.toString(),
            }, {
                color: "#8ed1f2",
                title: "Total Actual",
                subtitle: "Number",
                value: totalActualNumber.toString(),
            });
            // Format total time for display
            const totalTimeDisplay = totalTimeFormatted
                .replace(/hours|minutes|seconds/g, "")
                .replace(/,/g, "")
                .trim();
            // Add time card
            cardConfig.push({
                color: "#A8D9A5", // Light green
                title: "Total Time",
                subtitle: "Spent",
                value: totalTimeDisplay,
            });
            // Draw all cards
            cardConfig.forEach((card, index) => {
                drawCard(cardPositions[index], startY, card.color, card.title, card.subtitle, card.value, cardWidths[index]);
            });
            const tableStartY = startY + cardHeight + 15;
            const tableWidth = 1050 - 100;
            let colWidths = [];
            const srNoWidth = 70;
            const tableDateWidth = 120;
            const tableUserWidth = 120;
            const tableClientWidth = 200;
            const tableCategoryWidth = 200;
            const tableActualNumberWidth = 110;
            const tableTimeSpentWidth = 110;
            const hideDateColumn = screenshot === "true" && from && to;
            const isSingleUsername = Username && !Username.includes(",");
            colWidths.push(srNoWidth);
            if (!hideDateColumn) {
                colWidths.push(tableDateWidth);
            }
            if (!isSingleUsername) {
                colWidths.push(tableUserWidth);
            }
            if (Client) {
                colWidths.push(tableClientWidth);
            }
            colWidths.push(tableCategoryWidth);
            colWidths.push(tableActualNumberWidth);
            colWidths.push(tableTimeSpentWidth);
            const totalTableWidth = colWidths.reduce((sum, w) => sum + w, 0);
            const tableX = Math.floor((canvasWidth - totalTableWidth) / 2);
            ctx.shadowColor = "rgba(0, 0, 0, 0.12)";
            ctx.shadowBlur = 12;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 5;
            ctx.fillStyle = "#ffffff";
            ctx.fillRect(tableX, tableStartY, totalTableWidth, formattedData.length * rowHeight + rowHeight);
            ctx.shadowColor = "transparent";
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            const headerGradient = ctx.createLinearGradient(tableX, tableStartY, tableX, tableStartY + rowHeight);
            headerGradient.addColorStop(0, "#f8f9fa");
            headerGradient.addColorStop(1, "#e9ecef");
            ctx.fillStyle = headerGradient;
            ctx.fillRect(tableX, tableStartY, totalTableWidth, rowHeight);
            ctx.fillStyle = "#000000";
            ctx.font = "bold 12px Arial,sans-serif";
            ctx.textAlign = "center";
            const getColumnPosition = (columnIndex) => {
                let position = tableX;
                for (let i = 0; i < columnIndex; i++) {
                    position += colWidths[i];
                }
                return position + colWidths[columnIndex] / 2;
            };
            let columnIndex = 0;
            ctx.textAlign = "center";
            ctx.fillText("Sr No.", getColumnPosition(columnIndex), tableStartY + 20);
            columnIndex++;
            if (!hideDateColumn) {
                ctx.textAlign = "right";
                ctx.fillText("Date", getColumnPosition(columnIndex), tableStartY + 20);
                columnIndex++;
            }
            if (!isSingleUsername) {
                ctx.textAlign = "left";
                ctx.fillText("User", getColumnPosition(columnIndex) - colWidths[columnIndex] / 2 + 8, tableStartY + 20);
                columnIndex++;
            }
            if (Client) {
                ctx.textAlign = "left";
                ctx.fillText("Client", getColumnPosition(columnIndex) - colWidths[columnIndex] / 2 + 8, tableStartY + 20);
                columnIndex++;
            }
            ctx.textAlign = "left";
            ctx.fillText("Category", getColumnPosition(columnIndex) - colWidths[columnIndex] / 2 + 8, tableStartY + 20);
            columnIndex++;
            ctx.textAlign = "center";
            ctx.fillText("Actual Number", getColumnPosition(columnIndex), tableStartY + 20);
            columnIndex++;
            ctx.textAlign = "center";
            ctx.fillText("Time Spent", getColumnPosition(columnIndex), tableStartY + 20);
            const dataRows = formattedData.length - 1;
            ctx.font = "12px Arial,sans-serif";
            // Draw table data rows
            for (let i = 0; i < dataRows; i++) {
                const item = formattedData[i];
                const rowY = tableStartY + (i + 1) * rowHeight;
                ctx.fillStyle = i % 2 === 0 ? "#ffffff" : "#f5f7fa";
                ctx.fillRect(tableX, rowY, totalTableWidth, rowHeight);
                ctx.strokeStyle = "#e9ecef";
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(tableX, rowY);
                ctx.lineTo(tableX + totalTableWidth, rowY);
                ctx.stroke();
                ctx.fillStyle = "#333333";
                let rowColumnIndex = 0;
                ctx.textAlign = "center";
                ctx.fillText((i + 1).toString(), getColumnPosition(rowColumnIndex), rowY + 20);
                rowColumnIndex++;
                if (!hideDateColumn) {
                    ctx.textAlign = "center";
                    ctx.fillText(item.date, getColumnPosition(rowColumnIndex), rowY + 20);
                    rowColumnIndex++;
                }
                if (!isSingleUsername) {
                    ctx.textAlign = "left";
                    ctx.fillText(item.username, getColumnPosition(rowColumnIndex) - colWidths[rowColumnIndex] / 2 + 8, rowY + 20);
                    rowColumnIndex++;
                }
                if (Client) {
                    ctx.textAlign = "left";
                    ctx.fillText(item.client_name || "", getColumnPosition(rowColumnIndex) - colWidths[rowColumnIndex] / 2 + 8, rowY + 20);
                    rowColumnIndex++;
                }
                ctx.textAlign = "left";
                ctx.fillText(item.category_name || "", getColumnPosition(rowColumnIndex) - colWidths[rowColumnIndex] / 2 + 8, rowY + 20);
                rowColumnIndex++;
                ctx.textAlign = "center";
                ctx.fillText(item.actual_number.toString(), getColumnPosition(rowColumnIndex), rowY + 20);
                rowColumnIndex++;
                ctx.textAlign = "center";
                ctx.fillText(item.time_spent, getColumnPosition(rowColumnIndex), rowY + 20);
            }
            const totalRowY = tableStartY + (dataRows + 1) * rowHeight;
            ctx.shadowColor = "rgba(0, 0, 0, 0.08)";
            ctx.shadowBlur = 4;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 2;
            const totalRowGradient = ctx.createLinearGradient(tableX, totalRowY, tableX, totalRowY + rowHeight);
            totalRowGradient.addColorStop(0, "#e9ecef");
            totalRowGradient.addColorStop(1, "#dee2e6");
            ctx.fillStyle = totalRowGradient;
            ctx.fillRect(tableX, totalRowY, totalTableWidth, rowHeight);
            ctx.shadowColor = "transparent";
            ctx.shadowBlur = 0;
            ctx.shadowOffsetX = 0;
            ctx.shadowOffsetY = 0;
            ctx.strokeStyle = "#ced4da";
            ctx.lineWidth = 1;
            ctx.strokeRect(tableX, totalRowY, totalTableWidth, rowHeight);
            ctx.fillStyle = "#000000";
            let totalColIndex = 0;
            ctx.font = "bold 12px Arial,sans-serif";
            ctx.fillText("Total", getColumnPosition(totalColIndex), totalRowY + 20);
            totalColIndex = 1;
            if (!hideDateColumn) {
                totalColIndex++; // Skip Date
            }
            // Skip Username if not showing
            if (!isSingleUsername) {
                totalColIndex++;
            }
            // Skip Client if showing
            if (Client) {
                totalColIndex++;
            }
            // Skip Category
            totalColIndex++;
            ctx.font = "bold 12px Arial,sans-serif";
            ctx.fillText(totalActualNumber.toString(), getColumnPosition(totalColIndex), totalRowY + 20);
            totalColIndex++;
            ctx.font = "bold 12px Arial,sans-serif";
            ctx.fillText(totalTimeFormatted, getColumnPosition(totalColIndex), totalRowY + 20);
            // Send PNG
            const buffer = canvas.toBuffer("image/png");
            res.setHeader("Content-Type", "image/png");
            res.setHeader("Content-Disposition", 'attachment; filename="Work_Report_Summary.png"');
            return res.send(buffer);
        }
        // Excel export
        const wb = xlsx_1.default.utils.book_new();
        xlsx_1.default.utils.book_append_sheet(wb, ws, "Work Report");
        const excelBuffer = xlsx_1.default.write(wb, { bookType: "xlsx", type: "buffer" });
        if (data.length > 0) {
            const filename = formatFilename();
            res.setHeader("Content-Disposition", `attachment; filename="Work_Report_${filename}.xlsx"`);
            res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            return res.send(excelBuffer);
        }
        return res.status(200).json({ data: [], dataLength });
    }
    catch (error) {
        console.error("Error exporting work reports:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.exportCustomizeReport = exportCustomizeReport;
//# sourceMappingURL=view.js.map