"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteRolesPermission = void 0;
const utility_1 = require("./utility");
const permissions_1 = require("../../../utils/permissions");
const deleteRolesPermission = async (req, res) => {
    const role_id = req.params.id;
    const hasPermission = await (0, permissions_1.checkUserPermission)({
        req: req,
        res: res,
        action: "USER MANAGEMENT",
        permissiontype: "ROLE MANAGEMENT",
    });
    if (hasPermission) {
        await (0, utility_1.deleteTransaction)({
            model: "Roles",
            fieldName: "id",
            id: Number(role_id),
            res,
            successMessage: "Role have been deleted",
        });
    }
};
exports.deleteRolesPermission = deleteRolesPermission;
//# sourceMappingURL=delete.js.map