import { Response } from "express";
import { checkUserPermission } from "../../../utils/permissions"; // Assuming this is a utility to check permissions

export const createRolePermission = async (req, res) => {
  const { corporation_id } = req;
  // Check if the user has permission to manage roles
  const hasPermission = await checkUserPermission({
    req: req,
    res: res,
    action: "USER MANAGEMENT",
    permissiontype: "ROLE MANAGEMENT",
  });

  // If permission is granted, proceed with role creation
  if (hasPermission) {
    try {
      // Extract role data from the request
      const fields = {
        name: req.body.name.toUpperCase(), // Ensure the role name is in uppercase
        client_id: Number(req.body.client_id), // Assuming client_id is numeric
        corporation_id: corporation_id, // Replace this with the actual corporation ID from the request
      };
      //  (fields)
      // Create the new role in the "Roles" table
      const newRole = await prisma.roles.create({
        data: {
          name: fields.name,
          client_id: fields.client_id,
          corporation_id: fields.corporation_id,
        },
      });

      // Check if selected permissions are provided in the request body
      const selectedPermissions = req.body.permission; // Array of permission IDs to be assigned to the role

      // If permissions are provided, assign them to the new role
      if (selectedPermissions && selectedPermissions.length > 0) {
        const rolePermissions = selectedPermissions.map(
          (permission_id: number) => ({
            role_id: newRole.id, // Associate the created role
            permission_id: permission_id, // Assign the selected permission to the new role
          })
        );

        // Insert the role-permission associations into the "RolePermission" table
        await prisma.rolePermission.createMany({
          data: rolePermissions,
        });

        return res.status(201).json({
          success: true,
          message: "Role created and selected permissions assigned.",
          role: newRole,
        });
      } else {
        // If no permissions are selected, return a message indicating that no permissions were assigned
        return res.status(400).json({
          success: false,
          message: "No permissions selected to assign to the role.",
        });
      }
    } catch (error) {
      console.error(error);
      return res.status(500).json({
        success: false,
        message:
          error.message || "Something went wrong while creating the role.",
      });
    }
  } else {
    return res.status(403).json({
      success: false,
      message: "You do not have permission to manage roles.",
    });
  }
};
