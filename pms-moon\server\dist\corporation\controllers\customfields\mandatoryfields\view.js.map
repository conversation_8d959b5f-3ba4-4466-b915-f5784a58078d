{"version": 3, "file": "view.js", "sourceRoot": "", "sources": ["../../../../../src/corporation/controllers/customfields/mandatoryfields/view.ts"], "names": [], "mappings": ";;;AACA,2CAAyD;AAEzD,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAMlC,sCAAsC;AAC/B,MAAM,eAAe,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;IACnE,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,eAAe,CAA4B;;;;;KAKtE,CAAC,CAAC;QACH,MAAM,cAAc,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;QAExD,MAAM,MAAM,GAAG,MAAM;aAClB,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC;aAC7B,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAClD,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACb,KAAK;aACF,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aAC3D,IAAI,CAAC,GAAG,CAAC,CACb;aACA,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QAEtC,GAAG,CAAC,IAAI,CAAC,EAAE,eAAe,EAAE,MAAM,EAAE,CAAC,CAAC;IACxC,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,GAAG,CAAC,CAAC;QAC1D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,CAAC,CAAC;IACpE,CAAC;AACH,CAAC,CAAC;AA1BW,QAAA,eAAe,mBA0B1B"}