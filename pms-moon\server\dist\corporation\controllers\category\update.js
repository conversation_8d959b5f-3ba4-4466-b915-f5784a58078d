"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCategory = void 0;
const operation_1 = require("../../../utils/operation");
const updateCategory = async (req, res) => {
    const id = req.params.id;
    const { corporation_id } = req;
    const fields = {
        category_name: req.body.name,
        corporation_id: Number(corporation_id)
    };
    await (0, operation_1.updateItem)({
        model: "category",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "category has been updated",
    });
};
exports.updateCategory = updateCategory;
//# sourceMappingURL=update.js.map