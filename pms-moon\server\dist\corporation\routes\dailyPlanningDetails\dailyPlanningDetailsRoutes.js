"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const create_1 = require("../../controllers/dailyPlanningDetails/create");
const view_1 = require("../../controllers/dailyPlanningDetails/view");
const update_1 = require("../../controllers/dailyPlanningDetails/update");
const delete_1 = require("../../controllers/dailyPlanningDetails/delete");
const uploadfilerouter_1 = require("../uploadFile/uploadfilerouter");
const router = (0, express_1.Router)();
router.post("/create-dailyplanningdetails/:id", authentication_1.authenticate, create_1.createDailyPlanningDetails);
router.put("/update-dailyplanningdetails-statement/:id", authentication_1.authenticate, update_1.updateDailyPlanningDetails_Statement);
router.post("/excel-dailyplanningdetails/:id", uploadfilerouter_1.upload.single("file"), create_1.excelDailyPlanningDetails);
router.get("/get-all-dailyplanningdetails", 
// authenticate,
view_1.viewDailyPlanningDetails);
router.get("/get-specific-dailyplanningdetails/:id", 
// authenticate,
view_1.viewSpecificDailyPlanningDetails);
router.get("/:id/type/:type", 
// authenticate,
view_1.viewDailyPlanningDetailsDetailsManual);
router.put("/update-dailyplanningdetails/:id", authentication_1.authenticate, update_1.updateDailyPlanningDetails);
router.delete("/delete-dailyplanningdetails/:id", authentication_1.authenticate, delete_1.deleteDailyPlanningDetails);
router.get("/:id/:type", authentication_1.authenticate, view_1.viewDailyPlanningDetailsByType);
exports.default = router;
//# sourceMappingURL=dailyPlanningDetailsRoutes.js.map