"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/worktype/create");
const view_1 = require("../../controllers/worktype/view");
const update_1 = require("../../controllers/worktype/update");
const delete_1 = require("../../controllers/worktype/delete");
const authentication_1 = require("../../../middleware/authentication");
const checkPermission_1 = require("../../../middleware/checkPermission");
const router = (0, express_1.Router)();
router.post("/create-worktype", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORKTYPE MANAGEMENT", "create-workType"), create_1.createWorktype);
router.get("/get-all-worktype", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORKTYPE MANAGEMENT", "view-workType"), view_1.viewWorktype);
router.put("/update-worktype/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORKTYPE MANAGEMENT", "update-workType"), update_1.updateWorktype);
router.delete("/delete-worktype/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORKTYPE MANAGEMENT", "delete-workType"), delete_1.deleteWorktype);
exports.default = router;
//# sourceMappingURL=worktyperoutes.js.map