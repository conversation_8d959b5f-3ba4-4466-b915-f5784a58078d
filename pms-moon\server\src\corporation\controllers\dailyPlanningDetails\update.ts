import { Request, Response } from "express";
import { updateItem } from "../../../utils/operation";
import { transport } from "winston";

export const updateDailyPlanningDetails = async (req, res) => {
  console.log(req.body, "update req.body");

  const id = req.params.id;
  const invoice_entry_total =
    Number(req.body.old) + Number(req.body.new) || null;
  const fields = {
    carrier_id: Number(req.body.carrier),
    type: req.body.type,
    shipping_type: req.body.shipping_type,
    division: req.body.division,
    receive_date: req.body.receive_date
      ? new Date(req.body.receive_date)
      : undefined,
    reconcile_date: req.body.reconcile_date
      ? new Date(req.body.reconcile_date)
      : undefined,
    send_date: req.body.send_date ? new Date(req.body.send_date) : undefined,
    notes: req.body.notes,
    no_invoices: Number(req.body.no_invoices),
    amount_of_invoice: Number(req.body.amount_of_invoice),
    currency: req.body.currency,
    old: Number(req.body.old),
    new: Number(req.body.new),
    ute: Number(req.body.ute),
    invoice_entry_total,
  };
  await updateItem({
    model: "DailyPlanningDetails",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "Daily Planning has been updated",
  });
};

export const updateDailyPlanningDetails_Statement = async (req, res) => {
  const body = req.body;
  const id = req.params.id;

  if (!id) {
    return res.status(400).json({ message: "Missing ID" });
  }

  let fields: any = {};

  if (body.reconcile_date) {
    fields.reconcile_date = new Date(body.reconcile_date);

    fields.reconcile_by = body.reconcile_by;
  } else if (body.review_date) {
    fields.review_date = new Date(body.review_date);
    fields.review_by = body.review_by;
  } else if (body.send_date) {
    fields.send_date = new Date(body.send_date);
    fields.send_by = body.send_by;
  } else {
    return res.status(400).json({ message: "No valid date field provided" });
  }

  try {
    const existing = await prisma.dailyPlanningDetails.findUnique({
      where: { id: Number(id) },
      include: {
        carrier: true,
        daily_planning: true,
      },
    });

    if (!existing) {
      return res.status(404).json({ message: "Record not found" });
    }

    await prisma.dailyPlanningDetails.update({
      where: { id: Number(id) },
      data: {
        ...fields,
        carrier_id: existing.carrier_id,
        daily_planning_id: existing.daily_planning_id,
      },
    });

    const updated = await prisma.dailyPlanningDetails.findUnique({
      where: { id: Number(id) },
      include: {
        carrier: true,
        daily_planning: true,
      },
    });
    return res.status(200).json({
      success: true,
      message: "Daily Planning date has been updated",
      data: {
        ...updated,
        clientId: updated.daily_planning?.client_id ?? null,
        carrierId: updated.carrier?.id ?? null,
      },
    });
  } catch (err) {
    console.error("Update error:", err);
    return res
      .status(500)
      .json({ success: false, message: "Internal Server Error" });
  }
};
