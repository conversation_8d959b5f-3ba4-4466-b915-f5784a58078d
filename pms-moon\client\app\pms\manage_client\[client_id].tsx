"use client";
import { getAllData } from "@/lib/helpers";
import { setup_routes } from "@/lib/routePath";
import { useParams } from "next/navigation";
import { toast } from "sonner";

const ClientDetails = async() => {
    
  // const { client_id } = useParams();  
  // const [client, setClient] = useState(null);
  // const allClient = await getAllData(setup_routes.GETALL_SETUP);
  // useEffect(() => {
  //    (client_id);
    
  // })
//   useEffect(() => {
//     if (client_id) {
//       // Fetch client data based on client_id
//       allClient
//     //   fetchClientDetails(client_id)
//     //     .then((data) => setClient(data))
//     //     .catch((error) => {
//     //       toast.error("Error fetching client details");
//     //     });
//     }
//   }, [client_id]);
//  (client_id);

//   if (!client) return <div>Loading...</div>;

  return (
    <div>
      <h1>Client Details</h1>
      <p><strong>Client Name:</strong></p>
      {/* <p><strong>Owner Name:</strong> {client.}</p>
      <p><strong>Country:</strong> {client.country}</p> */}
      {/* Add more client details here */}
    </div>
  );
};

export default ClientDetails;
