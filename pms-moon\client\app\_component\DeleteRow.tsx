"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useContext, useEffect, useState, useTransition } from "react";
import { usePathname, useRouter } from "next/navigation";
import { MdDelete } from "react-icons/md";
import { showingToast, hasPermission } from "@/lib/clientHelpers";
import { deleteRow } from "@/lib/helpers";

interface DeleteRowProps {
  route: string;
  revPath?: string;
  userPermissionToCheck?: string;
  onSuccess?: () => void;
}
const DeleteRow = ({ route, revPath, onSuccess   }: DeleteRowProps) => {
  const [isPending, startTransition] = useTransition();
  const rev = "freight_audit/freight_setup";
  const [open, setOpen] = useState(false);
  const { refresh } = useRouter();
  const handleDelete = async () => {
    startTransition(() => {
      deleteRow(route).then((data: any) => {
        showingToast({ data, setOpen });
        onSuccess?.();
        refresh();
      });
    });
  };
  return (
    <>
      {
        <Dialog open={open} onOpenChange={() => setOpen(true)}>
          <DialogTrigger className="disabled:hover:cursor-not-allowed" title="Delete">
            <MdDelete className="w-5 h-5 font-extrabold text-gray-500" />
          </DialogTrigger>
          <DialogContent className="max-w-2xl dark:bg-gray-800">
            <DialogHeader>
              <DialogTitle className="text-3xl text-gray-800 dark:text-gray-300 text-center">
                Are you sure you want to delete ?
              </DialogTitle>
              <DialogDescription className="text-base text-center tracking-wider text-gray-800/70 font-medium dark:text-gray-400">
                This action cannot be undone.
              </DialogDescription>
            </DialogHeader>

            <div className="flex justify-between w-[60%] mx-auto mt-5">
              <Button
                className="px-12 py-6 text-lg capitalize bg-red-600 text-white hover:bg-main-color-foreground transition-all duration-300"
                onClick={handleDelete}
                disabled={isPending}
              >
                {isPending ? "deleting..." : " delete"}
              </Button>
              <Button
                className="px-12 py-6 text-lg capitalize bg-primary text-white hover:bg-primary/90 transition-all duration-300"
                onClick={() => setOpen(false)}
              >
                cancel
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      }
    </>
  );
};

export default DeleteRow;
