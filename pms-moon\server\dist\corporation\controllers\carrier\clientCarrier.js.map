{"version": 3, "file": "clientCarrier.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/carrier/clientCarrier.ts"], "names": [], "mappings": ";;;AAAA,oDAAqD;AAE9C,MAAM,OAAO,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACxC,IAAI,CAAC;QACH,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QAElC,MAAM,gBAAgB,GAAU,EAAE,CAAC;QAEnC,IAAI,WAAW,EAAE,IAAI,EAAE,EAAE,CAAC;YACxB,gBAAgB,CAAC,IAAI,CAAC;gBACpB,IAAI,EAAE;oBACJ,QAAQ,EAAE,WAAW,CAAC,IAAI,EAAE;oBAC5B,IAAI,EAAE,aAAa;iBACpB;aACF,CAAC,CAAC;QACL,CAAC;QAED,MAAM,WAAW,GAAG;YAClB,GAAG,EAAE,EAAE;SACR,CAAC;QAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC;gBACnB,GAAG,EAAE,gBAAgB;aACtB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACzC,KAAK,EAAE,WAAW;YAClB,OAAO,EAAE;gBACP,UAAU,EAAE,IAAI;aACjB;YACD,OAAO,EAAE;gBACP,EAAE,EAAE,MAAM;aACX;SACF,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC5C,KAAK,EAAE,WAAW;SACnB,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,IAAI;YACJ,KAAK,EAAE,UAAU;SAClB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AA9CW,QAAA,OAAO,WA8ClB"}