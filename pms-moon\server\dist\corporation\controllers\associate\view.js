"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewAssociate = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewAssociate = async (req, res) => {
    try {
        const data = await prisma.associate.findMany({
            orderBy: {
                id: 'desc',
            },
        });
        if (data) {
            return res.status(200).json({ data, datalength: data.length });
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewAssociate = viewAssociate;
//# sourceMappingURL=view.js.map