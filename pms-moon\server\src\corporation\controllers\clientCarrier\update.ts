import { createItem, updateItem } from "../../../utils/operation";

export const updateClientCarrier = async (req, res) => {
  const id = req.params.id;
  const { corporation_id } = req;
  const fields = {
    carrier_id: Number(req.body.carrier_id),
    client_id: Number(req.body.client_id),
    corporation_id: corporation_id,
    payment_terms: req.body.payment_terms
  };
  await updateItem({
    model: "clientCarrier",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "Client Carrier setup has been updated",
  });
};
