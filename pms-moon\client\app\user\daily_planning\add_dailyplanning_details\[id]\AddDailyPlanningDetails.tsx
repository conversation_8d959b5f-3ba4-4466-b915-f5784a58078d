"use client";
import { use<PERSON>allback, useEffect, useRef, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { PlusCircle, MinusCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { getAllData, formSubmit } from "@/lib/helpers";
import {
  carrier_routes,
  daily_planning,
  daily_planning_details_routes,
} from "@/lib/routePath";
import SelectComp from "@/app/_component/SelectComp";
import FormInput from "@/app/_component/FormInput";
import { toast } from "sonner";
import { SelectItem } from "@/components/ui/select";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";

import { z, ZodIssueCode } from "zod";
import { Import } from "./Import";

const InvoiceEntrySchema = z.object({
  carrier_id: z.string().min(1, "Carrier is required"),
  old: z.string().min(1, "Old is required"),
  new: z.string().min(1, "New is required"),
  ute: z.string().min(1, "UTE is required"),
  receive_date: z.string().optional(),
  review_date: z.string().optional(),
  reconcile_date: z.string().optional(),
  no_invoices: z.string().optional(),
  amount_of_invoice: z.string().optional(),
  currency: z.string().optional(),
});

const createStatementTableSchema = (dailyPlanningDate: string) => z.object({
  carrier_id: z.string().min(1, "Carrier is required"),
  shipping_type: z.string().min(1, "Transport type is required"),
  division: z.string().optional(),
  receive_date: z.string().min(1, "Receive date is required").refine(
    (date) => {
      const receiveDate = new Date(date);
      const planningDate = new Date(dailyPlanningDate);
      return receiveDate <= planningDate;
    },
    {
      message: "Receive date cannot be after daily planning date",
    }
  ),
  review_date: z.string().optional(),
  reconcile_date: z.string().optional(),
  send_date: z.string().optional(),
  no_invoices: z.string().min(1, "No invoices is required"),
  amount_of_invoice: z.string().min(1, "Amount of invoice is required"),
  currency: z.string().min(1, "Currency is required"),
  notes: z.string().optional(),
});

type FormValues = {
  type: "INVOICE_ENTRY_STATUS" | "STATEMENT_TABLE";
  entries: Array<{
    carrier_id: string;
    old?: string;
    new?: string;
    ute?: string;
    receive_date?: string;
    review_date?: string;
    reconcile_date?: string;
    send_date?: string;
    no_invoices?: string;
    amount_of_invoice?: string;
    currency?: string;
    shipping_type?: string;
    division?: string;
    notes?: string;
  }>;
};

const DynamicFormSchema = (dailyPlanningDate: string) => z
  .object({
    type: z.enum(["INVOICE_ENTRY_STATUS", "STATEMENT_TABLE"]),
    entries: z.array(
      z.object({
        carrier_id: z.string().min(1, "Carrier is required"),
        old: z.string().optional(),
        new: z.string().optional(),
        ute: z.string().optional(),
        receive_date: z.string().refine(
          (date) => {
            if (!date) return true; // Allow empty dates
            const receiveDate = new Date(date);
            const planningDate = new Date(dailyPlanningDate);
            return receiveDate <= planningDate;
          },
          {
            message: "Receive date cannot be after daily planning date",
          }
        ),
        review_date: z.string().optional(),
        reconcile_date: z.string().optional(),
        send_date: z.string().optional(),
        no_invoices: z.string().optional(),
        amount_of_invoice: z.string().optional(),
        currency: z.string().optional(),
        shipping_type: z.string().optional(),
        division: z.string().optional(),
        notes: z.string().optional(),
      })
    ),
  })
  .superRefine((data, ctx) => {
    if (data.type === "INVOICE_ENTRY_STATUS") {
      data.entries.forEach((entry, index) => {
        const result = InvoiceEntrySchema.safeParse(entry);
        if (!result.success) {
          result.error.issues.forEach((issue: any) => {
            ctx.addIssue({
              code: ZodIssueCode.invalid_type,
              expected: issue.expected,
              received: issue.received,
              path: ["entries", index, ...issue.path],
              message: issue.message,
            });
          });
        }
      });
    } else if (data.type === "STATEMENT_TABLE") {
      data.entries.forEach((entry, index) => {
        const result = createStatementTableSchema(dailyPlanningDate).safeParse(entry);
        if (!result.success) {
          result.error.issues.forEach((issue: any) => {
            ctx.addIssue({
              code: ZodIssueCode.invalid_type,
              expected: issue.expected,
              received: issue.received,
              path: ["entries", index, ...issue.path],
              message: issue.message,
            });
          });
        }
      });
    }
  });

export { DynamicFormSchema };

const AddDailyPlanningDetails = ({ carrierByClient, userData, dailyPlanning }: any) => {
  const router = useRouter();
  const carrierSelectRefs = useRef<(HTMLSelectElement | null)[]>([]);
  const { id } = useParams();
  
  // Format the daily planning date for max attribute
  const formattedPlanningDate = new Date(dailyPlanning.daily_planning_date)
    .toISOString()
    .split('T')[0];  // This will give YYYY-MM-DD format

  carrierByClient.sort((a, b) => a.carrier.name.localeCompare(b.carrier.name));

  const form = useForm<FormValues>({
    resolver: zodResolver(DynamicFormSchema(dailyPlanning.daily_planning_date)),
    defaultValues: {
      type: "INVOICE_ENTRY_STATUS",
      entries: [
        {
          carrier_id: "",
          old: "",
          new: "",
          ute: "",
          receive_date: "",
          no_invoices: "",
          amount_of_invoice: "",
          currency: "",
        },
      ],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "entries",
  });

  const onSubmit = async (data: FormValues) => {
    try {
      const enrichedEntries = data.entries.map((entry) => ({
        ...entry,
        receive_by: entry.receive_date ? userData.username : null,
      }));

      const submittedValues = await formSubmit(
        `${daily_planning_details_routes.CREATE_DAILY_PLANNING_DETAILS}/${id}`,
        "POST",
        {
          ...data,
          entries: enrichedEntries,
        }
      );
      if (submittedValues.success) {
        toast.success("Successfully added new entry");
        router.push("/user/daily_planning");
      } else {
        toast.error(submittedValues.errors);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const addNewEntry = useCallback(() => {
    append({
      carrier_id: "",
      old: "",
      new: "",
      ute: "",
      receive_date: "",
      no_invoices: "",
      amount_of_invoice: "",
      currency: "",
      shipping_type: "",
      division: "",
      notes: "",
    });
  }, [append]);

  useEffect(() => {
    const lastIndex = fields.length - 1;
    if (lastIndex >= 0 && carrierSelectRefs.current[lastIndex]) {
      carrierSelectRefs.current[lastIndex]?.focus();
    }
  }, [fields.length]);

  const removeEntry = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const selectType = form.watch("type");

  return (
    <>
      <Import selectType={selectType} userData={userData} />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit, (errors) =>
            console.error("Validation Errors:", errors)
          )}
        >
          <div className="flex justify-start  md:flex md:justify-end items-center gap-3   md:pr-5">
            <SelectComp
              form={form}
              label="Type"
              name="type"
              placeholder="Select type"
              isRequired
              className="md:w-48 pb-8 md:pb-5"
            >
              <SelectItem value="INVOICE_ENTRY_STATUS">
                Invoice entry status
              </SelectItem>
              <SelectItem value="STATEMENT_TABLE">Statement table</SelectItem>
            </SelectComp>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="flex items-center gap-4">
              {selectType === "INVOICE_ENTRY_STATUS" ? (
                <>
                  <div className="w-48  ">
                    <label
                      htmlFor={`entries.${index}.carrier_id`}
                      className="text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Carrier
                      <span className="text-red-500 ml-0.5">*</span>
                    </label>
                    <select
                      {...form.register(`entries.${index}.carrier_id`)}
                      id={`entries.${index}.carrier_id`}
                      className="flex h-9 w-48 items-center justify-between rounded-md bg-gray-200 px-3 py-2 text-sm ring-offset-white focus:outline-none focus:ring-1 focus:ring-blue-600  disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option
                        value=""
                        disabled
                        selected
                        className="text-gray-500 "
                      >
                        Select a carrier
                      </option>
                      {carrierByClient.map((carrier: any) => (
                        <option
                          key={carrier.carrier.id}
                          value={carrier.carrier.id.toString()}
                          className="text-sm bg-white hover:bg-gray-200 w-96"
                        >
                          {carrier.carrier.name}
                        </option>
                      ))}
                    </select>
                    {form.formState.errors.entries?.[index] && (
                      <p className="text-red-500 text-xs mt-1">
                        {
                          (form.formState.errors.entries[index] as any)
                            .carrier_id?.message
                        }
                      </p>
                    )}
                  </div>

                  <FormInput
                    label="Old"
                    form={form}
                    name={`entries.${index}.old`}
                    type="number"
                    className="w-48"
                    isRequired
                  />
                  <FormInput
                    label="New"
                    form={form}
                    name={`entries.${index}.new`}
                    type="number"
                    className="w-48"
                    isRequired
                  />
                  <FormInput
                    label="UTE"
                    form={form}
                    name={`entries.${index}.ute`}
                    type="number"
                    className="w-48"
                    isRequired
                  />
                </>
              ) : (
                <div className="">
                  <div className="  ">
                    <label
                      htmlFor={`entries.${index}.carrier_id`}
                      className="text-base font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Carrier
                      <span className="text-red-500 ml-0.5">*</span>
                    </label>
                    <select
                      {...form.register(`entries.${index}.carrier_id`)}
                      id={`entries.${index}.carrier_id`}
                      className="flex h-9 w-48 px-3 items-center justify-between rounded-md bg-gray-200  text-sm ring-offset-white focus:outline-none focus:ring-1 focus:ring-blue-600  disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option
                        value=""
                        disabled
                        selected
                        className="text-gray-500"
                      >
                        Select a carrier
                      </option>
                      {carrierByClient.map((carrier: any) => (
                        <option
                          key={carrier.carrier.id}
                          value={carrier.carrier.id.toString()}
                          className="text-sm bg-white hover:bg-gray-200 w-96"
                        >
                          {carrier.carrier.name}
                        </option>
                      ))}
                    </select>
                    {form.formState.errors.entries?.[index] && (
                      <p className="text-red-500 text-xs mt-1">
                        {
                          (form.formState.errors.entries[index] as any)
                            .carrier_id?.message
                        }
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-4">
                    <SelectComp
                      label="Currency"
                      form={form}
                      name={`entries.${index}.currency`}
                      placeholder="Currency"
                      isRequired
                      className="w-48"
                    >
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="CAD">CAD</SelectItem>
                      <SelectItem value="KRW">KRW</SelectItem>
                    </SelectComp>

                    <FormInput
                      label="Transport Type"
                      form={form}
                      name={`entries.${index}.shipping_type`}
                      placeholder="Enter Transport Type"
                      type="text"
                      className="w-48"
                      isRequired
                    />

                    <FormInput
                      label="Division"
                      form={form}
                      name={`entries.${index}.division`}
                      placeholder="Enter Division"
                      type="text"
                      className="w-48"
                    />
                    <FormInput
                      label="No. of Invoices"
                      placeholder="Enter No. Of Invoices"
                      form={form}
                      name={`entries.${index}.no_invoices`}
                      type="number"
                      className="w-48"
                      isRequired
                    />
                    <FormInput
                      label="Amount"
                      placeholder="Enter  Amount"
                      form={form}
                      name={`entries.${index}.amount_of_invoice`}
                      type="number"
                      className="w-48"
                      isRequired
                    />
                  </div>
                  <div className="flex items-center gap-4">
                    <FormInput
                      label="Received Date"
                      form={form}
                      name={`entries.${index}.receive_date`}
                      type="date"
                      className="w-48"
                      isRequired
                      max={formattedPlanningDate}
                    />

                    <FormInput
                      label="Notes"
                      form={form}
                      name={`entries.${index}.notes`}
                      placeholder="Enter notes"
                      type="text"
                      className="w-48"
                    />
                  </div>
                </div>
              )}

              <div
                className={`flex gap-2 ${
                  selectType === "INVOICE_ENTRY_STATUS" ? " md:pt-6" : " mt-21 "
                }`}
              >
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  onClick={() => removeEntry(index)}
                  disabled={fields.length === 1}
                >
                  <MinusCircle className="h-4 w-4" />
                </Button>
                {index === fields.length - 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={addNewEntry}
                  >
                    <PlusCircle className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          ))}

          <div className="mt-2 md:flex md:justify-end space-x-2 px-5">
            <Button type="submit" className="w-full md:w-auto">
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
};

export default AddDailyPlanningDetails;
