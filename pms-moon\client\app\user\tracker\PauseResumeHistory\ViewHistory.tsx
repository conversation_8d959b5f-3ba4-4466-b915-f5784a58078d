"use client";
import DataTable from "@/app/_component/DataTable";
import React from "react";
import { Column } from "./Column";

const ViewHistory = ({ data, permissions, getAllPermission }: any) => {
  // Process the data to pair pause and resume events
  const processedData = [];

  // Create a map to store pause events by index
  const pauseMap = new Map();

  // First, add all pause events to the map
  data.pause.forEach((pauseDate, index) => {
    pauseMap.set(index, {
      pause_date: pauseDate,
      resume_date: null,
    });
  });

  // Then match resume events with their corresponding pause events
  data.resume.forEach((resumeDate, index) => {
    if (pauseMap.has(index)) {
      // Update the existing entry with the resume date
      const entry = pauseMap.get(index);
      entry.resume_date = resumeDate;
      pauseMap.set(index, entry);
    } else {
      // If there's no matching pause event, create a new entry
      pauseMap.set(index, {
        pause_date: null,
        resume_date: resumeDate,
      });
    }
  });

  // Convert the map to an array
  pauseMap.forEach((value) => {
    processedData.push(value);
  });

  return (
    <>
      <DataTable
        data={processedData}
        columns={Column()}
        showSearchColumn={false}
      />
    </>
  );
};

export default ViewHistory;
