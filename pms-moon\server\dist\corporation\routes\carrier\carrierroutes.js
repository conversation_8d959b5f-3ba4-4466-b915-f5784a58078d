"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/carrier/create");
const view_1 = require("../../controllers/carrier/view");
const update_1 = require("../../controllers/carrier/update");
const delete_1 = require("../../controllers/carrier/delete");
const authentication_1 = require("../../../middleware/authentication");
const checkPermission_1 = require("../../../middleware/checkPermission");
const multer_1 = __importDefault(require("multer"));
const exportCarrierService_1 = require("../../controllers/carrier/exportCarrierService");
const clientCarrier_1 = require("../../controllers/carrier/clientCarrier");
const router = (0, express_1.Router)();
//checkPermissionMiddleware("createClientProfile", "CLIENT MANAGEMENT")
const DIR = "./src/public";
const storage = multer_1.default.diskStorage({
    destination: function (req, file, cb) {
        cb(null, DIR);
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + "-" + file.originalname);
    },
});
const upload = (0, multer_1.default)({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 },
}).single("file");
router.post('/excelCarrier', upload, create_1.excelCarrier); //client
router.post("/create-carrier", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CARRIER MANAGEMENT", "create-carrier"), create_1.createCarrier);
router.get("/get-all-carrier", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CARRIER MANAGEMENT", "view-carrier"), view_1.viewCarrier);
router.put("/update-carrier/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CARRIER MANAGEMENT", "update-carrier"), update_1.updateCarrier);
router.delete("/delete-carrier/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CARRIER MANAGEMENT", "delete-carrier"), delete_1.deleteCarrier);
router.get("/get-carrier-by-client/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CARRIER MANAGEMENT", "view-carrier-by-client"), view_1.viewCarrierByClient);
router.get("/get-carrier", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CARRIER MANAGEMENT", "view-carrier"), clientCarrier_1.Carrier);
router.get("/export-carrier", exportCarrierService_1.exportCarrierService);
exports.default = router;
//# sourceMappingURL=carrierroutes.js.map