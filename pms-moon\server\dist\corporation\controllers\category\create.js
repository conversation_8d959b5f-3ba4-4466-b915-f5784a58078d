"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCategory = void 0;
const operation_1 = require("../../../utils/operation");
const createCategory = async (req, res) => {
    const { corporation_id } = req;
    const fields = {
        category_name: req.body.name,
        corporation_id: Number(corporation_id),
    };
    await (0, operation_1.createItem)({
        model: "category",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "category has been created",
    });
};
exports.createCategory = createCategory;
//# sourceMappingURL=create.js.map