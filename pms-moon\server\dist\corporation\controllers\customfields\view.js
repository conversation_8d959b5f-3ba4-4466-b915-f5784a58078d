"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCustomFields = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// --- GET: Fetch All Custom Fields ---
const getCustomFields = async (_req, res) => {
    try {
        const fields = await prisma.customField.findMany({
            orderBy: { createdAt: "desc" },
            include: {
            //autoOption: true, // if you want to return associated AUTO options
            },
        });
        res.status(200).json(fields);
    }
    catch (error) {
        console.error("Error fetching custom fields:", error);
        res.status(500).json({ error: "Server error" });
    }
};
exports.getCustomFields = getCustomFields;
//# sourceMappingURL=view.js.map