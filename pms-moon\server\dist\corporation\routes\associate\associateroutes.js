"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const checkPermission_1 = require("../../../middleware/checkPermission");
const create_1 = require("../../controllers/associate/create");
const view_1 = require("../../controllers/associate/view");
const update_1 = require("../../controllers/associate/update");
const delete_1 = require("../../controllers/associate/delete");
const router = (0, express_1.Router)();
router.post("/create-associate", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("ASSOCIATE MANAGEMENT", "create-associate"), create_1.createAssociate);
router.get("/get-all-associate", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("ASSOCIATE MANAGEMENT", "view-associate"), view_1.viewAssociate);
router.put("/update-associate/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("ASSOCIATE MANAGEMENT", "update-associate"), update_1.updateAssociate);
router.delete("/delete-associate/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("ASSOCIATE MANAGEMENT", "delete-associate"), delete_1.deleteAssociate);
exports.default = router;
//# sourceMappingURL=associateroutes.js.map