"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.upload = void 0;
const express_1 = require("express");
const multer_1 = __importDefault(require("multer"));
const create_1 = require("../../controllers/uploadfile/create");
const createTwoTen_1 = require("../../controllers/uploadfile/createTwoTen");
const router = (0, express_1.Router)();
const DIR = "./src/public";
const storage = multer_1.default.diskStorage({
    destination: function (req, file, cb) {
        cb(null, DIR);
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + "-" + file.originalname);
    },
});
exports.upload = (0, multer_1.default)({
    storage: storage,
});
router.post("/upload-file/:id", exports.upload.single("file"), create_1.uploadCSV);
router.post("/upload-csv-twoten/:id", exports.upload.single("file"), createTwoTen_1.uploadCSVTwoTen);
exports.default = router;
//# sourceMappingURL=uploadfilerouter.js.map