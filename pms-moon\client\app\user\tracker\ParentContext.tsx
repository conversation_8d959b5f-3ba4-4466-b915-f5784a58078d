"use client";
import React, { createContext, useState, useContext, ReactNode } from "react";

// Define types for context values
interface ParentContextType {
  selectedCarrier: string;
  setSelectedCarrier: React.Dispatch<React.SetStateAction<string>>;
  selectedClient: string;
  setSelectedClient: React.Dispatch<React.SetStateAction<string>>;
  isTimerRunning: boolean;
  setIsTimerRunning: React.Dispatch<React.SetStateAction<boolean>>;
  elapsedTime: number;
  setElapsedTime: React.Dispatch<React.SetStateAction<number>>;
  isAddTaskOpen: boolean;
  setIsAddTaskOpen: React.Dispatch<React.SetStateAction<boolean>>;
  seletedWorkType: boolean;
  setSeletedWorkType: React.Dispatch<React.SetStateAction<boolean>>;
  carrier: any[];
  setCarrier: React.Dispatch<React.SetStateAction<any[]>>;
}

// Create Context
const ParentContext = createContext<ParentContextType | undefined>(undefined);

// Context Provider
export const ParentProvider = ({ children }: { children: ReactNode }) => {
  const [selectedCarrier, setSelectedCarrier] = useState("Carrier");
  const [selectedClient, setSelectedClient] = useState("");
  const [isTimerRunning, setIsTimerRunning] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [isAddTaskOpen, setIsAddTaskOpen] = useState(false);
  const [seletedWorkType, setSeletedWorkType] = useState(false);
  const [carrier, setCarrier] = useState<any[]>([]);

  return (
    <ParentContext.Provider
      value={{
        selectedCarrier,
        setSelectedCarrier,
        selectedClient,
        setSelectedClient,
        isTimerRunning,
        setIsTimerRunning,
        elapsedTime,
        setElapsedTime,
        isAddTaskOpen,
        setIsAddTaskOpen,
        seletedWorkType,
        setSeletedWorkType,
        carrier,
        setCarrier,
      }}
    >
      {children}
    </ParentContext.Provider>
  );
};

// Custom hook to use the context
export const useParentContext = (): ParentContextType => {
  const context = useContext(ParentContext);
  if (!context) {
    throw new Error("useParentContext must be used within a ParentProvider");
  }
  return context;
};

