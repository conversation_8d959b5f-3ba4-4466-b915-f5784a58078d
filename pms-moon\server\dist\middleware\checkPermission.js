"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkPermissionMiddleware = void 0;
const checkPermissionMiddleware = (action, module) => {
    return async (req, res, next) => {
        //  ("user_id in checkPermissionMiddleware:", req.user_id);
        try {
            if (req.corporation_id) {
                return next();
            }
            const id = req.corporation_id;
            if (!id) {
                res.status(401).json({ success: false, message: "Unauthorized" });
                return; // Return after sending the response
            }
            const user = await prisma.user.findUnique({
                where: { id: id },
                include: {
                    role: {
                        include: {
                            role_permission: {
                                include: {
                                    permission: true,
                                },
                            },
                        },
                    },
                },
            });
            if (!user || !user.role) {
                res
                    .status(403)
                    .json({ success: false, message: "User has no role assigned" });
                return;
            }
            let hasPermission = false;
            for (let rolePermission of user.role.role_permission) {
                const permission = rolePermission.permission;
                if (permission.action === action && permission.module === module) {
                    hasPermission = true;
                    break;
                }
            }
            if (!hasPermission) {
                res.status(403).json({
                    success: false,
                    message: `You do not have permission to ${action} in ${module}.`,
                });
                return; // Return after sending the response
            }
            // If permission granted, move to the next middleware or route handler
            next();
        }
        catch (error) {
            console.error("Error in checkPermissionMiddleware:", error);
            // Only send the error response if headers haven't been sent yet
            if (!res.headersSent) {
                res
                    .status(500)
                    .json({ success: false, message: "Internal Server Error" });
                return; // Return after sending the response
            }
        }
    };
};
exports.checkPermissionMiddleware = checkPermissionMiddleware;
//# sourceMappingURL=checkPermission.js.map