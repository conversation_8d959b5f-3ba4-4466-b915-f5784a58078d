"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../controllers/create");
const update_1 = require("../controllers/update");
const login_1 = require("../../utils/login");
const delete_1 = require("../controllers/delete");
const view_1 = require("../controllers/view");
const logout_1 = require("../controllers/logout");
const authentication_1 = require("../../middleware/authentication");
const router = (0, express_1.Router)();
router.post("/create-superadmin", create_1.createSuperAdmin);
router.post("/login", login_1.superAdminLogin);
router.post("/logout", logout_1.logoutSuperAdmin);
router.get("/get-all-superadmin", authentication_1.authenticateSuperAdmin, view_1.viewSuperAdmin);
router.post("/update-superadmin/:id", authentication_1.authenticateSuperAdmin, update_1.updateSuperAdmin);
router.delete("/delete-superadmin/:id", authentication_1.authenticateSuperAdmin, delete_1.deleteSuperAdmin);
exports.default = router;
//# sourceMappingURL=superadminroutes.js.map