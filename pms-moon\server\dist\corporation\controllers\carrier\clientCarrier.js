"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Carrier = void 0;
const helpers_1 = require("../../../utils/helpers");
const Carrier = async (req, res) => {
    try {
        const { CarrierName } = req.query;
        const searchConditions = [];
        if (CarrierName?.trim()) {
            searchConditions.push({
                name: {
                    contains: CarrierName.trim(),
                    mode: "insensitive",
                },
            });
        }
        const whereClause = {
            AND: [],
        };
        if (searchConditions.length > 0) {
            whereClause.AND.push({
                AND: searchConditions,
            });
        }
        const data = await prisma.carrier.findMany({
            where: whereClause,
            include: {
                WorkReport: true,
            },
            orderBy: {
                id: "desc",
            },
        });
        const datalength = await prisma.carrier.count({
            where: whereClause,
        });
        return res.status(200).json({
            data,
            total: datalength,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.Carrier = Carrier;
//# sourceMappingURL=clientCarrier.js.map