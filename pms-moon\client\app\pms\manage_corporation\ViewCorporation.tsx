import DataTable from '@/app/_component/DataTable'
import React from 'react'
import { column } from './column'

const ViewCorporation = ({data}:any) => {
    
  return (
   <>
   <div className="w-full">
      <DataTable
        data={data}
        columns={column}
        // filter
        // filter_column="name"
        showColDropDowns
        showPageEntries
        // filter2
        // filter_column2="register1"
        className="w-full"
      />
    </div>
   </>
  )
}

export default ViewCorporation
