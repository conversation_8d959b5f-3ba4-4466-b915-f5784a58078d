{"version": 3, "file": "permissions.js", "sourceRoot": "", "sources": ["../../src/utils/permissions.ts"], "names": [], "mappings": ";;;AAAA,2CAA8C;AAC9C,MAAM,MAAM,GAAG,IAAI,qBAAY,EAAE,CAAC;AAO3B,MAAM,aAAa,GAAG,KAAK,EAChC,OAAe,EACf,MAAc,EACd,MAAc,EACd,SAAkB,EAClB,EAAE;IACF,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;QACjD,KAAK,EAAE;YACL,EAAE,EAAE,OAAO;SACZ;QACD,MAAM,EAAE;YACN,IAAI,EAAE;gBACJ,MAAM,EAAE;oBACN,SAAS,EAAE,IAAI;oBACf,eAAe,EAAE;wBACf,MAAM,EAAE;4BACN,UAAU,EAAE;gCACV,MAAM,EAAE;oCACN,MAAM,EAAE,IAAI;oCACZ,MAAM,EAAE,IAAI;iCACb;6BACF;yBACF;qBACF;iBACF;aACF;SACF;KACF,CAAC,CAAC;IAEH,IAAI,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACnD,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CACzD,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,KAAK,MAAM,CAC5C,CAAC;QAEF,OAAO,CAAC,CAAC,UAAU,CAAC;IACtB,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AAtCW,QAAA,aAAa,iBAsCxB;AAEK,MAAM,mBAAmB,GAAG,KAAK,EAAE,EACxC,GAAG,EACH,GAAG,EACH,MAAM,EACN,cAAc,EACd,SAAS,GAOV,EAAE,EAAE;IACH,MAAM,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC;IACvB,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,iBAAiB,GAAG,MAAM,IAAA,qBAAa,EAC3C,MAAM,CAAC,EAAE,CAAC,EACV,cAAc,EACd,MAAM,EACN,SAAS,CACV,CAAC;IAEF,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;QACvE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AA/BW,QAAA,mBAAmB,uBA+B9B;AAEK,MAAM,iBAAiB,GAAG,KAAK,EAAE,EACtC,SAAS,EACT,OAAO,GAIR,EAAE,EAAE;IACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;QACvC,KAAK,EAAE;YACL,EAAE,EAAE,OAAO;YACX,IAAI,EAAE;gBACJ,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC;aAC7B;SACF;QACD,MAAM,EAAE;YACN,IAAI,EAAE;gBACJ,MAAM,EAAE;oBACN,SAAS,EAAE,IAAI;iBAChB;aACF;SACF;KACF,CAAC,CAAC;IAEH,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAxBW,QAAA,iBAAiB,qBAwB5B"}