"use client"
import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";

import DeleteRow from "@/app/_component/DeleteRow";
import { carrier_routes, corporation_routes } from "@/lib/routePath";
import UpdateCorporation from "./UpdateCorporation";

export interface Carrier {
  name: string;
  register1: string;
  code: string;
  country: string;
  state: string;
  city: string;
  address: string;
  phone: string;
  postalcode: string;
  corporation_id: any
}

export const column: ColumnDef<Carrier>[] = [
  {
    accessorKey: "username",
    header: "User Name",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
 
  {
    accessorKey: "country",
    header: "Country",
  },
  {
    accessorKey: "state",
    header: "State",
  },
  {
    accessorKey: "city",
    header: "City",
  },
  {
    accessorKey: "address",
    header: "Address",
  },
  {
    accessorKey: "action",
    header: "Action",
    id: "action",
    cell: ({ row }) => {
      const corporation = row?.original;
      
      return (
        <div className="flex items-center">
          <UpdateCorporation initialData={corporation} />
          <DeleteRow
            route={`${corporation_routes.DELETE_CORPORATION}/${corporation?.corporation_id}`}
          />
        </div>
      );
    },
  },
];
