import { Button } from "@/components/ui/button";
import { useState } from "react";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import ExcelJS from "exceljs";
import { useRouter, useSearchParams } from "next/navigation";
import { formatDate } from "@/lib/swrFetching";
import { getAllData } from "@/lib/helpers";
import { trackSheets_routes } from "@/lib/routePath";

function tryParseCustomFields(fields: any): any {
  if (typeof fields === "string") {
    try {
      return JSON.parse(fields) || {};
    } catch (e) {
      console.error("Error parsing custom fields string:", e);
      return {};
    }
  }
  return fields || {};
}

const ExportTrackSheet = ({
  filteredTrackSheetData,
  customFieldsMap,
  selectedClients,
}: any) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  const Export = async () => {
    setIsLoading(true);
    try {
      let allData: any[] = [];

      let clientId = null;
      if (filteredTrackSheetData?.length > 0) {
        clientId =
          filteredTrackSheetData[0]?.client?.id ||
          filteredTrackSheetData[0]?.clientId;
      } else if (selectedClients?.length > 0) {
        clientId = selectedClients[0]?.value;
      }

      if (!clientId) throw new Error("No client selected");

      const params = new URLSearchParams(searchParams);
      params.delete("pageSize");
      params.delete("page");

      const baseUrl = `${
        trackSheets_routes.GETALL_TRACK_SHEETS
      }/${clientId}?${params.toString()}`;
      const response = await getAllData(baseUrl);
      allData = response.data || [];

      allData = allData.map((row) => ({
        ...row,
        customFields: (row.TrackSheetCustomFieldMapping || []).reduce(
          (acc: any, mapping: any) => {
            acc[mapping.customFieldId] = mapping.value;
            return acc;
          },
          {}
        ),
      }));

      const staticHeaders = [
        "Client",
        "Company",
        "Division",
        "Carrier",
        "FTP File Name",
        "FTP Page",
        "File Path",
        "Master Invoice",
        "Invoice",
        "Bol",
        "Received Date",
        "Invoice Date",
        "Shipment Date",
        "Invoice Total",
        "Currency",
        "Qty Shipped",
        "Quantity Billed Text",
        "Invoice Status",
        "Manual Matching",
        "Invoice Type",
        "Weight Unit",
        "Savings",
        "Doc Available",
        "Notes",
        "Mistake",
      ];

      const customFieldIds = Object.keys(customFieldsMap || {});
      const customFieldHeaders = customFieldIds.map(
        (id) => customFieldsMap[id]?.name || `Custom Field ${id}`
      );

      const allHeaders = [...staticHeaders, ...customFieldHeaders];

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet("TrackSheet Report");

      worksheet.columns = allHeaders.map((header) => ({
        header,
        width: header === "File Path" ? 50 : 20,
      }));

      allData.forEach((item) => {
        const formattedRow = [
          item.client?.client_name,
          item.company,
          item.division,
          item.carrier?.name,
          item.ftpFileName,
          item.ftpPage,
          item.filePath,
          item.masterInvoice,
          item.invoice,
          item.bol,
          formatDate(item.receivedDate),
          formatDate(item.invoiceDate),
          formatDate(item.shipmentDate),
          item.invoiceTotal,
          item.currency,
          item.qtyShipped,
          item.quantityBilledText,
          item.invoiceStatus,
          item.manualMatching,
          item.invoiceType,
          item.weightUnitName,
          item.savings,
          item.docAvailable,
          item.notes,
          item.mistake,
        ];

        const itemCustomFields = tryParseCustomFields(item.customFields);
        const customFieldValues = customFieldIds.map((fieldId) => {
          const value = itemCustomFields[fieldId];
          const fieldType = customFieldsMap[fieldId]?.type;

          if (!value) return "";
          if (fieldType === "DATE") {
            const dt = new Date(value);
            if (!isNaN(dt.getTime())) {
              return `${String(dt.getDate()).padStart(2, "0")}-${String(
                dt.getMonth() + 1
              ).padStart(2, "0")}-${String(dt.getFullYear()).slice(-2)}`;
            }
          }
          return value;
        });

        worksheet.addRow([...formattedRow, ...customFieldValues]);
      });

      const fileBuffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([fileBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = `TrackSheet_Report_${
        new Date().toISOString().split("T")[0]
      }.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Export error:", error);
    } finally {
      setIsLoading(false);
      router.refresh();
    }
  };

  return (
    <div>
      <Button
        onClick={Export}
        className="mt-6 bg-gradient-to-r from-main-color/70 to-[#065c6d] mb-1 px-3 py-1.5 max-h-8 hover:to-main-color-foreground mr-2 text-white font-semibold uppercase"
        disabled={isLoading}
      >
        {isLoading ? (
          <span className="animate-spin">
            <AiOutlineLoading3Quarters />
          </span>
        ) : (
          "Download report"
        )}
        {isLoading ? "Exporting..." : ""}
      </Button>
    </div>
  );
};

export default ExportTrackSheet;
