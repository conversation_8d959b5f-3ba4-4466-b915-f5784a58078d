const { PrismaClient } = require("@prisma/client");
const bcrypt = require("bcrypt");

const prisma = new PrismaClient();
async function createPermissions() {
  const module = [
    // dashboard
    // { module: "DASHBOARD", action: "view-dashboard" },
    // //client
    // { module: "CLIENT MANAGEMENT", action: "create-client" },
    // { module: "CLIENT MANAGEMENT", action: "view-client" },
    // { module: "CLIENT MANAGEMENT", action: "update-client" },
    // { module: "CLIENT MANAGEMENT", action: "delete-client" },
    // // carrier
    // { module: "CARRIER MANAGEMENT", action: "create-carrier" },
    // { module: "CARRIER MANAGEMENT", action: "view-carrier" },
    // { module: "CARRIER MANAGEMENT", action: "update-carrier" },
    // { module: "CARRIER MANAGEMENT", action: "delete-carrier" },
    // // user
    // { module: "USER MANAGEMENT", action: "create-user" },
    // { module: "USER MANAGEMENT", action: "view-user" },
    // { module: "USER MANAGEMENT", action: "update-user" },
    // { module: "USER MANAGEMENT", action: "delete-user" },
    // // worktype
    // { module: "WORKTYPE MANAGEMENT", action: "create-workType" },
    // { module: "WORKTYPE MANAGEMENT", action: "view-workType" },
    // { module: "WORKTYPE MANAGEMENT", action: "update-workType" },
    // { module: "WORKTYPE MANAGEMENT", action: "delete-workType" },
    // // workreport
    // { module: "WORK REPORT", action: "create-workReport" },
    // { module: "WORK REPORT", action: "view-workReport" },
    // { module: "WORK REPORT", action: "update-workReport" },
    // { module: "WORK REPORT", action: "delete-workReport" },
    // // tracker
    // { module: "TRACKER", action: "TRACKER" },
    // { module: "TRACKER", action: "update-tracker" },
     // TrackSheet
    { module: "TrackSheet", action: "TrackSheet" },
    // daily_planning
    // { module: "DAILY PLANNING", action: "DAILY PLANNING" },
    // { module: "DAILY PLANNING", action: "create-dailyPlannig" },
    // { module: "DAILY PLANNING", action: "delete-dailyPlanning" },
    // { module: "DAILY PLANNING", action: "update-dailyPlanning" },
    // { module: "DAILY PLANNING", action: "upload-dailyPlanningDetails" },
    // { module: "DAILY PLANNING", action: "add-dailyPlanningDetails" },
    // {module: "BRANCH MANAGEMENT", action: "create-branch"},
    // {module: "BRANCH MANAGEMENT", action: "view-branch"},
    // {module: "BRANCH MANAGEMENT", action: "update-branch"},
    // {module: "BRANCH MANAGEMENT", action: "delete-branch"},
    // // daily_planning
    // { module: "DAILY PLANNING", action: "DAILY PLANNING" },
    // // manageRoles
    // { module: "ROLE MANAGEMENT", action: "create-role" },
    // { module: "ROLE MANAGEMENT", action: "view-role" },
    // { module: "ROLE MANAGEMENT", action: "update-role" },
    // { module: "ROLE MANAGEMENT", action: "delete-role" },
    // { module: "DAILY PLANNING", action: "create-backdate-dailyplanning" },
    // // daily planning details
    // { module: "DAILY PLANNING DETAILS", action: "update-dailyplanningdetails" },
    // { module: "DAILY PLANNING DETAILS", action: "delete-dailyplanningdetails" },

    // // Associate
    // { module: "ASSOCIATE MANAGEMENT", action: "create-associate" },
    // { module: "ASSOCIATE MANAGEMENT", action: "view-associate" },
    // { module: "ASSOCIATE MANAGEMENT", action: "update-associate" },
    // { module: "ASSOCIATE MANAGEMENT", action: "delete-associate" },

    // // Customize Report
    // { module: "CUSTOMIZE REPORT", action: "view-report" },
    

  ];

  const data = module.map(({ module, action }) => ({
    module,
    action,
  }));

  try {
    await prisma.permissions.createMany({
      data,
    });
  } catch (error) {
    console.error("Error while creating permissions:", error);
  }
}

async function createUserTitles() {
  const titles = [
    // { level: 1, title: "GU" },
    // { level: 2, title: "TL" },
    // { level: 3, title: "CSA" },
    // { level: 4, title: "CM" },
    // { level: 5, title: "HR" },
  ];

  try {
    await prisma.userTitle.createMany({
      data: titles,
    });
    console.log("User titles seeded successfully!");
  } catch (error) {
    console.error("Error while creating user titles:", error);
  }
}

async function createVisibilityRules() {
  // const rules = [
  //   {
  //     level: 5,
  //     table_name: "DailyPlanning",
  //     self: true,
  //     ancestors: 0,
  //     descendants: 4,
  //     siblings: false,
  //   },
  //   {
  //     level: 4,
  //     table_name: "DailyPlanning",
  //     self: true,
  //     ancestors: 1,
  //     descendants: 3,
  //     siblings: false,
  //   },
  //   {
  //     level: 3,
  //     table_name: "DailyPlanning",
  //     self: true,
  //     ancestors: 2,
  //     descendants: 2,
  //     siblings: false,
  //   },
  //   {
  //     level: 2,
  //     table_name: "DailyPlanning",
  //     self: true,
  //     ancestors: 3,
  //     descendants: 1,
  //     siblings: true,
  //   },
  //   {
  //     level: 1,
  //     table_name: "DailyPlanning",
  //     self: true,
  //     ancestors: 4,
  //     descendants: 0,
  //     siblings: true,
  //   },
  //   {
  //     level: 5,
  //     table_name: "WorkReport",
  //     self: true,
  //     ancestors: 0,
  //     descendants: 0,
  //     siblings: false,
  //   },
  //   {
  //     level: 4,
  //     table_name: "WorkReport",
  //     self: true,
  //     ancestors: 0,
  //     descendants: 0,
  //     siblings: false,
  //   },
  //   {
  //     level: 3,
  //     table_name: "WorkReport",
  //     self: true,
  //     ancestors: 0,
  //     descendants: 0,
  //     siblings: false,
  //   },

  //   {
  //     level: 2,
  //     table_name: "WorkReport",
  //     self: true,
  //     ancestors: 0,
  //     descendants: 0,
  //     siblings: false,
  //   },
  //   {
  //     level: 1,
  //     table_name: "WorkReport",
  //     self: true,
  //     ancestors: 0,
  //     descendants: 0,
  //     siblings: false,
  //   },
  // ];

  try {
    await prisma.visibilityRule.createMany({
      data: rules,
    });
    console.log("Visibility rules seeded successfully!");
  } catch (error) {
    console.error("Error while creating visibility rules:", error);
  }
}

async function main() {
  // const password = await bcrypt.hash("Tech9898", 10);

  // const superAdmin = await prisma.superAdmin.create({
  //   data: {
  //     username: "admin",
  //     email: "<EMAIL>",
  //     password,
  //   },
  // });
  // const corporation = await prisma.corporation.create({
  //   data: {
  //     username: "moonsquare",
  //     email: "<EMAIL>",
  //     password,
  //   },
  // });

  await createPermissions();
  await createUserTitles();
  await createVisibilityRules();
}

async function run() {
  try {
    await main();
  } catch (e) {
    console.error(e);
  } finally {
    await prisma.$disconnect();
  }
}

run();
