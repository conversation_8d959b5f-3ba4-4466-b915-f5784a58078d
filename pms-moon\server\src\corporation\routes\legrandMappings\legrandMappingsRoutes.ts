import { Router } from "express";
import { createLegrandMapping } from "../../controllers/legrandMappings/create";
import { viewLegrandMapping, viewLegrandMappingById } from "../../controllers/legrandMappings/view";
import { updateLegrandMapping } from "../../controllers/legrandMappings/update";
import { deleteLegrandMapping } from "../../controllers/legrandMappings/delete";

const router = Router();

router.post(
    "/",
    // authenticate,
    createLegrandMapping
);

router.get(
    "/",
    // authenticate,
    viewLegrandMapping
);
router.get(
    "/:id",
    // authenticate,
    viewLegrandMappingById
)

router.put(
    "/:id",
    // authenticate,
    updateLegrandMapping
);

router.delete(
    "/:id",
    // authenticate,
    deleteLegrandMapping
);
export default router;