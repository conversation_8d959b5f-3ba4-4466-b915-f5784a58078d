"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteLegrandMapping = void 0;
const operation_1 = require("../../../utils/operation");
const deleteLegrandMapping = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "LegrandMapping",
        fieldName: "id",
        id: id,
        res: res,
        req: req,
        successMessage: "LegrandMapping has been deleted",
    });
};
exports.deleteLegrandMapping = deleteLegrandMapping;
//# sourceMappingURL=delete.js.map