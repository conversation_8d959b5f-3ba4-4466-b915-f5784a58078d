import React, { useState, useRef } from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Search, X, Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface SelectProps {
  name: string;
  form?: any;
  label?: string;
  placeholder?: string;
  children?: React.ReactNode;
  isRequired?: boolean;
  disabled?: boolean;
  isEntryPage?: boolean;
  className?: string;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  onValueChange?: (value: string) => void;
  options?: Array<{ value: string; label: string; badge?: string; badgeColor?: string }>;
}

const SearchSelect = ({
  name,
  label,
  form,
  children,
  placeholder,
  isRequired,
  disabled,
  isEntryPage,
  className,
  onKeyDown,
  onValueChange,
  options = []
}: SelectProps) => {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [highlightedIndex, setHighlightedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const optionRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Extract options from children if not provided directly
  const selectOptions = options.length > 0 ? options : React.Children.toArray(children)
    .filter(child => React.isValidElement(child) && child.props.value)
    .map(child => {
      if (React.isValidElement(child)) {
        return {
          value: child.props.value,
          label: child.props.children
        };
      }
      return null;
    })
    .filter(Boolean) as Array<{ value: string; label: string }>;

  // Filter options based on search input
  const filteredOptions = searchValue
    ? selectOptions.filter(option =>
        option.label.toString().toLowerCase().includes(searchValue.toLowerCase()))
    : selectOptions;

  // Handle selecting the currently highlighted option
  const selectHighlightedOption = () => {
    if (filteredOptions.length > 0 && highlightedIndex >= 0 && highlightedIndex < filteredOptions.length) {
      const selectedOption = filteredOptions[highlightedIndex];
      if (onValueChange) onValueChange(selectedOption.value);
      setSearchValue("");
      setOpen(false);
      return selectedOption.value;
    }
    return null;
  };

  return (
    <div className={cn("", className)}>
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => {
          // Find the selected option to display its label
          const selectedOption = field.value
            ? selectOptions.find(option => option.value === field.value)
            : null;

          return (
            <FormItem
              className={isEntryPage ? "mb-1 space-y-0.5" : "md:mb-3 space-y-0.5 pt-1"}
            >
              <FormLabel
                className={`${
                  isEntryPage ? "md:text-xs" : "md:text-base"
                } text-gray-800 dark:text-gray-300 whitespace-nowrap`}
              >
                {label}
                {isRequired && <span className="text-red-500">*</span>}
              </FormLabel>
              <FormControl>
                <div className="relative" ref={dropdownRef}>
                  {/* Input field */}
                  <div
                    className={`flex items-center px-3 py-2 rounded-md bg-gray-200 dark:bg-gray-700 ${
                      isEntryPage ? "h-7 text-[0.80rem]" : "h-10"
                    } ${disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer"} focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 focus-within:ring-offset-0 focus-within:ring-inset`}
                    onClick={() => {
                      if (!disabled) {
                        setOpen(true);
                        inputRef.current?.focus();
                      }
                    }}
                    tabIndex={-1}
                    onBlur={(e) => {
                      // Only close if focus moves outside the container
                      if (!e.currentTarget.contains(e.relatedTarget)) {
                        setOpen(false);
                        setSearchValue("");
                      }
                    }}
                  >
                    {/* <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" /> */}
                    <input
                      ref={inputRef}
                      type="text"
                      placeholder={field.value ? "" : placeholder}
                      className={`flex-1 bg-transparent border-none outline-none text-xs focus:outline-none ${
                        disabled ? "cursor-not-allowed" : ""
                      }`}
                      value={open ? (searchValue !== "" ? searchValue : selectedOption?.label || "") : selectedOption?.label || ""}
                      onChange={(e) => {
                        setSearchValue(e.target.value);
                        setHighlightedIndex(0);
                        if (!open) setOpen(true);
                        if (e.target.value === "") {
                          field.onChange("");
                          if (onValueChange) onValueChange("");
                        }
                      }}
                      onFocus={() => setOpen(true)}
                      onBlur={(e) => {
                        // Only close if focus moves outside the container
                        if (!e.currentTarget.parentElement?.contains(e.relatedTarget)) {
                          setOpen(false);
                          setSearchValue("");
                        }
                      }}
                      onKeyDown={(e) => {
                        // Allow custom key handling
                        if (onKeyDown) onKeyDown(e);
                        // Handle keyboard navigation
                        switch (e.key) {
                          case "ArrowDown":
                            e.preventDefault();
                            if (!open) {
                              setOpen(true);
                            } else {
                              setHighlightedIndex((prev) =>
                                Math.min(prev + 1, filteredOptions.length - 1)
                              );
                              // Scroll to highlighted option
                              if (optionRefs.current[highlightedIndex + 1]) {
                                optionRefs.current[highlightedIndex + 1]?.scrollIntoView({
                                  block: 'nearest',
                                  behavior: 'smooth'
                                });
                              }
                            }
                            break;
                          case "ArrowUp":
                            e.preventDefault();
                            setHighlightedIndex((prev) => Math.max(prev - 1, 0));
                            // Scroll to highlighted option
                            if (optionRefs.current[highlightedIndex - 1]) {
                              optionRefs.current[highlightedIndex - 1]?.scrollIntoView({
                                block: 'nearest',
                                behavior: 'smooth'
                              });
                            }
                            break;
                          case "Enter":
                            e.preventDefault();
                            if (open) {
                              const value = selectHighlightedOption();
                              if (value) {
                                field.onChange(value);
                              }
                            } else {
                              setOpen(true);
                            }
                            break;
                          case "Escape":
                            e.preventDefault();
                            setOpen(false);
                            setSearchValue("");
                            break;
                          case "Tab":
                            setOpen(false);
                            setSearchValue("");
                            break;
                        }
                      }}
                      disabled={disabled}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (!disabled) setOpen(true);
                      }}
                    />
                    {(field.value || searchValue) && (
                      <X
                        className="h-4 w-4 shrink-0 opacity-50 cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          field.onChange("");
                          setSearchValue("");
                          if (onValueChange) onValueChange("");
                        }}
                      />
                    )}
                  </div>

                  {/* Dropdown */}
                  {open && (
                    <div className="absolute z-50 w-full mt-1 max-h-60 overflow-auto rounded-md bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-md">
                      {filteredOptions.length === 0 ? (
                        <div className={`py-2 px-3 ${isEntryPage ? 'text-sm' : 'text-sm'} text-gray-500`}>
                          No results found.
                        </div>
                      ) : (
                        <div className="p-1 capitalize">
                          {filteredOptions.map((option, index) => (
                            <div
                              key={option.value}
                              ref={(el: HTMLDivElement | null) => { optionRefs.current[index] = el }}
                              className={`relative cursor-pointer ${isEntryPage ? 'text-sm' : 'text-xs'} py-2 px-3 hover:bg-gray-100 dark:hover:bg-gray-800 ${
                                field.value === option.value ? "bg-gray-100 dark:bg-gray-800" : ""
                              } ${
                                index === highlightedIndex ? "bg-blue-50 dark:bg-blue-900" : ""
                              }`}
                              onMouseDown={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                field.onChange(option.value);
                                setSearchValue("");
                                setOpen(false);
                                setTimeout(() => {
                                  if (onValueChange) onValueChange(option.value);
                                  inputRef.current?.focus();
                                }, 0);
                              }}
                              onMouseEnter={() => setHighlightedIndex(index)}
                            >
                              <div className="flex items-center">
                                <span className="flex-1 pr-16">{option.label}</span>
                                {field.value === option.value && (
                                  <Check className="h-4 w-4 text-blue-500" />
                                )}
                              </div>
                              {option.badge && (
                                <span
                                  className={`absolute bottom-1 right-2 text-[10px] px-2 py-1 rounded text-white font-medium ${
                                    option.badgeColor || (option.badge === 'Alias' ? 'bg-blue-500' : 'bg-green-500')
                                  }`}
                                >
                                  {option.badge}
                                </span>
                              )}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </FormControl>
              <FormMessage
                className={`${
                  isEntryPage ? "text-xs tracking-wider" : "tracking-wider"
                }`}
              />
            </FormItem>
          );
        }}
      />
    </div>
  );
};

export default SearchSelect;
