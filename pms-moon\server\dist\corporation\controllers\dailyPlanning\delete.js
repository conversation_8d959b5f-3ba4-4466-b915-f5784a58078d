"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteDailyPlanning = void 0;
const operation_1 = require("../../../utils/operation");
const deleteDailyPlanning = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "dailyPlanning",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "workReport has been deleted",
    });
};
exports.deleteDailyPlanning = deleteDailyPlanning;
//# sourceMappingURL=delete.js.map