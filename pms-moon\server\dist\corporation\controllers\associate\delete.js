"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteAssociate = void 0;
const operation_1 = require("../../../utils/operation");
const deleteAssociate = async (req, res) => {
    const { id } = req.params;
    await (0, operation_1.deleteItem)({
        model: "associate",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "Associate has been deleted",
    });
};
exports.deleteAssociate = deleteAssociate;
//# sourceMappingURL=delete.js.map