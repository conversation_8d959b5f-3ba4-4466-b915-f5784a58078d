"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const create_1 = require("../../controllers/rolespermission/create");
const view_1 = require("../../controllers/rolespermission/view");
const update_1 = require("../../controllers/rolespermission/update");
const delete_1 = require("../../controllers/rolespermission/delete");
const checkPermission_1 = require("../../../middleware/checkPermission");
const router = (0, express_1.Router)();
router.post("/add-roles", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("ROLE MANAGEMENT", "create-role"), create_1.createRolePermission);
router.get("/get-all-roles", view_1.viewRoles);
router.get("/get-all-permissions", view_1.getPermissions);
router.get("/get-all-role-permissions", view_1.viewRolesPermissions);
router.put("/update-roles/:id", authentication_1.authenticate, update_1.updateRolesPermission);
router.delete("/delete-roles/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("ROLE MANAGEMENT", "delete-role"), delete_1.deleteRolesPermission);
// router.get("/get-all-role-permissions",      viewRolesPermissions);
exports.default = router;
//# sourceMappingURL=rolesPermissionRoutes.js.map