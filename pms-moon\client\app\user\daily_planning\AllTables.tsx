"use client";

import React, { useState } from "react";
import { getAllData } from "@/lib/helpers";
import { daily_planning_details_routes } from "@/lib/routePath";
import toast from "react-hot-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import AllView from "./table/AllView";
import ViewInvoiceEntry from "./table/ViewInvoiceEntry";
import ViewStatementTable from "./table/ViewStatementTable";
import CustomTableForDP from "@/app/_component/CustomTableForDP";
import TableSkeleton from "@/app/_component/TableSkeleton";

interface TableData {
  dailyPlanningDetails: any[];
  dailyPlanning: any[];
  dailyPlanningDetailsInvoice: any[];
  dailyPlanningDetailsStatement: any[];
  dailyPlanningDetailsPF: any[];
  dailyPlanningDetailsBatch: any[];
  dailyPlanningDetailsHold: any[];
  dailyPlanningDetailsReview: any[];
  dailyPlanningDetailsTwoTenError: any[];
  dailyPlanningDetailsEntry: any[];
  dailyPlanningDetailsCorrect: any[];
  dailyPlanningDetailsTwoTenSuccess: any[];
  dailyPlanningDetailsTwoTenMF: any[];
  dailyPlanningDetailsTwoTenHold: any[];
  dailyPlanningDetailsTwoTenImportAdditional: any[];
  dailyPlanningDetailsTwoTenManualMatch: any[];
}

// Different API endpoint types
enum ApiEndpointType {
  ALL = "ALL",
  TYPE = "TYPE",
  MANUAL = "MANUAL",
}

interface TabConfig {
  apiType: ApiEndpointType;
  typeParam?: string;
  dataKey: keyof TableData;
  heading: string;
  typeTable?: string;
  component: string;
}

const AllTables = ({ id, type, permissions, userData }: { id: number; type?: string; permissions: any , userData: any}) => {
  const [selectedTable, setSelectedTable] = useState("all");
  const [loading, setLoading] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const [tableData, setTableData] = useState<TableData>({
    dailyPlanningDetails: [],
    dailyPlanning: [],
    dailyPlanningDetailsInvoice: [],
    dailyPlanningDetailsStatement: [],
    dailyPlanningDetailsPF: [],
    dailyPlanningDetailsBatch: [],
    dailyPlanningDetailsHold: [],
    dailyPlanningDetailsReview: [],
    dailyPlanningDetailsTwoTenError: [],
    dailyPlanningDetailsEntry: [],
    dailyPlanningDetailsCorrect: [],
    dailyPlanningDetailsTwoTenSuccess: [],
    dailyPlanningDetailsTwoTenMF: [],
    dailyPlanningDetailsTwoTenHold: [],
    dailyPlanningDetailsTwoTenImportAdditional: [],
    dailyPlanningDetailsTwoTenManualMatch: [],
  });

  // Define tab configurations
  const tabConfigs: Record<string, TabConfig> = {
    all: {
      apiType: ApiEndpointType.ALL,
      dataKey: "dailyPlanningDetails",
      heading: "All",
      component: "AllView",
    },
    invoice: {
      apiType: ApiEndpointType.MANUAL,
      typeParam: "INVOICE_ENTRY_STATUS",
      dataKey: "dailyPlanningDetailsInvoice",
      heading: "Invoice Entry",
      component: "ViewInvoiceEntry",
    },
    statement: {
      apiType: ApiEndpointType.MANUAL,
      typeParam: "STATEMENT_TABLE",
      dataKey: "dailyPlanningDetailsStatement",
      heading: "Statement",
      component: "ViewStatementTable",
    },
    review: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "REVIEW_STATUS",
      dataKey: "dailyPlanningDetailsReview",
      heading: "Under Review",
      typeTable: "review_status",
      component: "CustomTableForDP",
    },
    hold: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "HOLD_STATUS",
      dataKey: "dailyPlanningDetailsHold",
      heading: "On Hold",
      typeTable: "hold",
      component: "CustomTableForDP",
    },
    batch: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "BATCH_ERROR_STATUS",
      dataKey: "dailyPlanningDetailsBatch",
      heading: "Batch Errors",
      typeTable: "batch_error",
      component: "CustomTableForDP",
    },
    entry: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "ENTRY",
      dataKey: "dailyPlanningDetailsEntry",
      heading: "Entry",
      typeTable: "entry",
      component: "CustomTableForDP",
    },
    correct: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "CORRECT",
      dataKey: "dailyPlanningDetailsCorrect",
      heading: "Correct",
      typeTable: "correct",
      component: "CustomTableForDP",
    },
    twoTenError: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "TWO_TEN_ERROR",
      dataKey: "dailyPlanningDetailsTwoTenError",
      heading: "210 Error",
      typeTable: "two_ten_error",
      component: "CustomTableForDP",
    },
    twoTenSuccess: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "TWO_TEN_SUCCESS",
      dataKey: "dailyPlanningDetailsTwoTenSuccess",
      heading: "210 Success",
      typeTable: "two_ten_success",
      component: "CustomTableForDP",
    },
    twoTenMF: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "TWO_TEN_M_F",
      dataKey: "dailyPlanningDetailsTwoTenMF",
      heading: "210 Match Fail",
      typeTable: "two_ten_m_f",
      component: "CustomTableForDP",
    },
    twoTenHold: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "TWO_TEN_HOLD",
      dataKey: "dailyPlanningDetailsTwoTenHold",
      heading: "210 Hold",
      typeTable: "two_ten_hold",
      component: "CustomTableForDP",
    },
    twoTenImportAdditional: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "TWO_TEN_IMPORT_ADDITIONAL",
      dataKey: "dailyPlanningDetailsTwoTenImportAdditional",
      heading: "210 Import Additional",
      typeTable: "two_ten_import_additional",
      component: "CustomTableForDP",
    },
    twoTenManualMatch: {
      apiType: ApiEndpointType.TYPE,
      typeParam: "TWO_TEN_MANUAL_MATCH",
      dataKey: "dailyPlanningDetailsTwoTenManualMatch",
      heading: "210 Manual Match",
      typeTable: "two_ten_manual_match",
      component: "CustomTableForDP",
    },
  };

  const getApiEndpoint = (config: TabConfig): string => {
    switch (config.apiType) {
      case ApiEndpointType.ALL:
        return `${daily_planning_details_routes.GETALL_DAILY_PLANNING_DETAILS}/${id}`;
      case ApiEndpointType.TYPE:
        return `${daily_planning_details_routes.GET_DAILY_PLANNING_DETAILS_TYPE}/${id}/${config.typeParam}`;
      case ApiEndpointType.MANUAL:
        return `${daily_planning_details_routes.GET_DAILY_PLANNING_DETAILS_MANUAL}/${id}/type/${config.typeParam}`;
      default:
        return "";
    }
  };

  const fetchDataForTab = async (tabValue: string) => {
    if (!tabConfigs[tabValue]) return;

    setLoading(true);
    try {
      const config = tabConfigs[tabValue];
      const endpoint = getApiEndpoint(config);
      const data = await getAllData(endpoint);

      setTableData((prev) => ({
        ...prev,
        [config.dataKey]: data,
      }));
    } catch (error: any) {
      toast.error(`Error fetching ${tabValue} data`);
    } finally {
      setLoading(false);
    }
  };

  // Helper function to check if data already exists for the selected tab
  const checkIfDataExists = (tabValue: string): boolean => {
    const config = tabConfigs[tabValue];
    if (!config) return false;

    return tableData[config.dataKey].length > 0;
  };

  const handleTabChange = async (value: string) => {
    setSelectedTable(value);

    // Check if we already have data for this tab
    const hasData = checkIfDataExists(value);

    if (!hasData) {
      await fetchDataForTab(value);
    }
  };

  // Render the component based on the selected tab
  const renderComponent = (tabValue: string) => {
    const config = tabConfigs[tabValue];
    if (!config) return null;

    switch (config.component) {
      case "AllView":
        return <AllView dailyPlanningDetails={tableData[config.dataKey]} />;
      case "ViewInvoiceEntry":
        return (
          <ViewInvoiceEntry
            dailyPlanningDetailsInvoice={tableData[config.dataKey]}
            permissions={permissions}
          />
        );
      case "ViewStatementTable":
        return (
          <ViewStatementTable
            dailyPlanningDetailsStatement={tableData[config.dataKey]}
            permissions={permissions}
            userData={userData}
            dailyPlanningDetails={tableData[config.dataKey]}
          />
        );
      case "CustomTableForDP":
        return (
          <CustomTableForDP
            tableData={tableData[config.dataKey]}
            typeTable={config.typeTable || ""}
            heading={config.heading}
          />
        );
      default:
        return null;
    }
  };

  const renderSelectedTable = () => {
    // If this is the first render and "all" is selected but no data loaded yet
    if (
      !initialized &&
      selectedTable === "all" &&
      tableData.dailyPlanningDetails.length === 0
    ) {
      // Start loading the initial data for "all" tab
      if (!loading) {
        setLoading(true);
        fetchDataForTab("all").then(() => {
          setInitialized(true);
        });
      }
      return <TableSkeleton  heading="Loading..."/>;
    }

    if (loading) {
      return <TableSkeleton  heading="Loading..."/>;
    }

    return renderComponent(selectedTable);
  };

  return (
    <div className="w-full text-xs rounded-lg shadow-sm">
      <Tabs
        defaultValue="all"
        value={selectedTable}
        onValueChange={handleTabChange}
        className="w-full"
      >
        <TabsList className="w-full flex justify-start gap-1 text-xs bg-gray-50 rounded-lg">
          {Object.entries(tabConfigs).map(([key, config]) => (
            <TabsTrigger
              key={key}
              value={key}
              className="transition-all hover:text-white hover:bg-primary"
            >
              {config.heading}
            </TabsTrigger>
          ))}
        </TabsList>
        <TabsContent value={selectedTable} className="mt-2">
          <div className="bg-white rounded-lg">{renderSelectedTable()}</div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AllTables;
