"use client";
import * as React from "react";
import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Command as CommandPrimitive } from "cmdk";

import { FormMessage } from "@/components/ui/form";

export type Framework = {
  value: string;
  label: string;
};

type FancySelectProps = {
  frameworks: Framework[];
  selected: Framework[];
  setSelected: React.Dispatch<React.SetStateAction<Framework[]>>;
  label?: string;
  disabled?: boolean;
  placeholder?: string;
  errorMessage?: string;
  className?: string; // add className prop
  onChange?: (value: string) => void; // add onChange prop
  multiSelect?: boolean; // add multiSelect prop
};

export function FancySelect({
  frameworks,
  selected,
  setSelected,
  label,
  disabled = false,
  placeholder = "Search...",
  errorMessage,
  className = "",
  onChange,
  multiSelect = false,
}: FancySelectProps) {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");

  const handleUnselect = React.useCallback(
    (framework: Framework) => {
      setSelected((prev) => {
        const newSelected = prev.filter((s) => s.value !== framework.value);
        // If we're removing the last item and onChange is provided, call it with empty string
        if (newSelected.length === 0 && onChange) {
          onChange("");
        }
        return newSelected;
      });
    },
    [setSelected, onChange]
  );

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      const input = inputRef.current;
      if (input) {
        if (
          (e.key === "Delete" || e.key === "Backspace") &&
          input.value === ""
        ) {
          setSelected((prev) => {
            const newSelected = [...prev];
            newSelected.pop();
            return newSelected;
          });
        }
        if (e.key === "Escape") {
          input.blur();
        }
      }
    },
    [setSelected]
  );

  const handleSelect = React.useCallback(
    (framework: Framework) => {
      if (multiSelect) {
        setSelected((prev) => [...prev, framework]);
      } else {
        setSelected([framework]); // single select
      }

      // Call onChange if provided
      if (onChange) {
        onChange(framework.value);
      }
    },
    [setSelected, onChange, multiSelect]
  );

  const selectables = frameworks.filter((framework) =>
    !Array.isArray(selected)
      ? true
      : !selected.some((s) => s.value === framework.value)
  );

  return (
    <div className={`space-y-1 ${className}`}>
      {label && (
        <label className="text-gray-800 dark:text-gray-300 whitespace-nowrap">
          {label}
        </label>
      )}
      <Command
        onKeyDown={handleKeyDown}
        className={`overflow-visible bg-transparent ${
          disabled ? "pointer-events-none" : ""
        }`}
      >
        <div
          className={`
    group
    flex
    flex-wrap
    items-center
    gap-[2px]
    rounded-md
    border
    px-3
    text-sm
    ring-offset-background
    focus-within:ring-2
    focus-within:ring-ring
    focus-within:ring-offset-2
    bg-gray-100
    h-9
    min-h-9
    min-w-0
    relative
    ${disabled ? "cursor-not-allowed" : ""}
    ${
      errorMessage && (!Array.isArray(selected) || selected.length === 0)
        ? "border-destructive"
        : "border-input"
    }
  `}
        >
          <div className="flex flex-nowrap gap-1 items-center w-full h-full m-0 p-0">
            {Array.isArray(selected) &&
              selected.map((framework) => (
                <Badge
                  key={framework.value}
                  variant="secondary"
                  className="max-w-full px-1 m-0 flex items-center"
                >
                  <span
                    title={framework.label}
                    className="block overflow-hidden text-ellipsis whitespace-nowrap font-semibold text-slate-800 flex-1 min-w-0 max-w-full"
                  >
                    {framework.label}
                  </span>
                  <button
                    type="button"
                    className="ml-1 rounded-full flex items-center justify-center"
                    tabIndex={-1}
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleUnselect(framework);
                      }
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onClick={() => handleUnselect(framework)}
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}

            <CommandPrimitive.Input
              ref={inputRef}
              value={inputValue}
              onValueChange={setInputValue}
              onBlur={() => {
                setTimeout(() => setOpen(false), 100);
              }}
              onFocus={() => setOpen(true)}
              placeholder={
                Array.isArray(selected) && selected.length > 0
                  ? ""
                  : placeholder
              }
              disabled={disabled}
              className={`
        ml-1
        flex-1
        bg-transparent
        bg-gray-200
        outline-none
        placeholder:text-muted-foreground
        min-h-0
        min-w-[50px]
        h-6
        text-sm
        p-0
        border-0
        shadow-none
      `}
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "Tab") {
                  setOpen(false);
                }
              }}
            />
          </div>
        </div>

        <div className="relative mt-1">
          <CommandList>
            {open && selectables.length > 0 ? (
              <div className="absolute top-0 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in">
                <CommandGroup className="h-40 overflow-auto cbar">
                  {selectables.map((framework) => (
                    <CommandItem
                      key={framework.value}
                      onMouseDown={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onSelect={() => {
                        setInputValue("");
                        handleSelect(framework);
                      }}
                      className={"cursor-pointer"}
                    >
                      {framework.label}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </div>
            ) : null}
          </CommandList>
          {errorMessage &&
            (!Array.isArray(selected) || selected.length === 0) && (
              <FormMessage className="px-1 text-xs text-destructive mt-0">
                {errorMessage}
              </FormMessage>
            )}
        </div>
      </Command>
    </div>
  );
}
