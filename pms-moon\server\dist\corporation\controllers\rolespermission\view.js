"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewSetupPermissions = exports.viewRolesPermissions = exports.viewRoles = exports.getPermissions = void 0;
const find_1 = require("./find");
const utility_1 = require("./utility");
const helpers_1 = require("../../../utils/helpers");
const permissions_1 = require("../../../utils/permissions");
const getPermissions = async (req, res) => {
    try {
        const getPermissions = await prisma.permissions.findMany({
            select: {
                id: true,
                module: true,
                action: true,
            },
            orderBy: {
                id: 'desc'
            }
        });
        const groupedPermissions = getPermissions.reduce((acc, perm) => {
            const key = `${perm.action}`;
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(perm);
            return acc;
        }, {});
        res.status(200).json(groupedPermissions);
    }
    catch (error) {
        console.log(error);
        res.status(500).json({
            message: error.message || "something went wrong",
        });
    }
};
exports.getPermissions = getPermissions;
const viewRoles = async (req, res) => {
    try {
        const params = await (0, find_1.getAllRoleParams)(req.corporation_id);
        // Querying the 'roles' model and including related tables
        const roles = await prisma.roles.findMany({
            where: {
                corporation_id: req.corporation_id, // Assuming you're filtering by 'corporation_id'
            },
            orderBy: {
                created_at: "asc", // Sorting by 'created_at' in ascending order
            },
            include: {
                corporation: true, // Including the 'Corporation' relation
                permissions: true, // Including the 'Permissions' relation
                role_permission: {
                    select: {
                        permission: {
                            select: {
                                action: true,
                                module: true,
                                id: true,
                            },
                        },
                    },
                }, // Including the 'RolePermission' relation
                User: true, // Including the 'User' relation
            },
        });
        // Handling case when no roles are found
        if (!roles || roles.length === 0) {
            return res.status(404).json({
                success: false,
                message: "No roles found",
            });
        }
        // Returning the roles with all related data
        res.status(200).json(roles);
    }
    catch (error) {
        console.error(error);
        res.status(500).json({
            success: false,
            message: error.message || "Something went wrong",
        });
    }
};
exports.viewRoles = viewRoles;
const viewRolesPermissions = async (req, res) => {
    try {
        const params = await (0, find_1.getRoleParams)(req.body.corporation_id);
        const roles = await (0, utility_1.getData)("roles", params);
        const hasPermission = await (0, permissions_1.checkUserPermission)({
            req,
            res: res,
            action: "USER MANAGEMENT",
            permissiontype: "manageRoles",
        });
        if (hasPermission) {
            if (!roles || roles.length === 0) {
                return res.status(404).json({
                    success: false,
                    message: "No roles found",
                });
            }
            //  (roles, "roles");
            const rolesPermission = await Promise.all(roles.map(async (item) => {
                const data = await (0, find_1.getRolePermissionParams)(item.role_id);
                const permission = data.map((perm) => ({
                    module: perm.permission.module,
                    permission_id: perm.permission.permission_id,
                }));
                // const client_id = JSON.parse(item.client_id);
                // const client_names = await prisma.client.findMany({
                //   where: {
                //     client_id: { in: client_id },
                //   },
                //   select: {
                //     client_name: true,
                //   },
                // });
                return {
                    role: item.name,
                    role_id: item.role_id,
                    permission,
                    // client_id: item.client_id,
                    // client_names,
                };
            }));
            return res.status(200).json(rolesPermission);
        }
    }
    catch (error) {
        console.log(error);
        return res.status(500).json({
            success: false,
            message: error.message || "Something went wrong",
        });
    }
};
exports.viewRolesPermissions = viewRolesPermissions;
const viewSetupPermissions = async (req, res) => {
    try {
        const params = await (0, find_1.getFreightSetupParams)(req.corporation_id);
        const findPermissions = await findOne("user", params);
        const data = findPermissions?.role?.role_permission.map((permission) => permission.permission.module) || [];
        return res.status(200).json(data);
    }
    catch (error) {
        console.log(error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewSetupPermissions = viewSetupPermissions;
const findOne = async (model, params, postProcess) => {
    try {
        const data = await prisma[model].findFirst(params);
        const result = postProcess ? await postProcess(data) : data;
        return result;
    }
    catch (error) {
        throw error;
    }
};
//# sourceMappingURL=view.js.map