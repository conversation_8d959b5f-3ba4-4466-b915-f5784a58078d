{"version": 3, "file": "update.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/userClients/update.ts"], "names": [], "mappings": ";;;AAAO,MAAM,iBAAiB,GAAG,KAAK,EAAE,MAAc,EAAE,YAAsB,EAAE,EAAE;IAChF,IAAI,CAAC;QACH,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACzD,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;SAC3B,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE5E,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;QAEhF,MAAM,eAAe,GAAG,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;QAEnF,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAChD,MAAM;gBACN,QAAQ;aACT,CAAC,CAAC,CAAC;YAEJ,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gBAClC,IAAI,EAAE,WAAW;gBACjB,cAAc,EAAE,IAAI;aACrB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC;gBAClC,KAAK,EAAE;oBACL,MAAM;oBACN,QAAQ,EAAE,EAAE,EAAE,EAAE,eAAe,EAAE;iBAClC;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACrD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;IACpE,CAAC;AACH,CAAC,CAAC;AAvCW,QAAA,iBAAiB,qBAuC5B"}