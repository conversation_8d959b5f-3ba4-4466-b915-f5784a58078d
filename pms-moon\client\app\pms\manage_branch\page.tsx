import React from "react";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { getAllData, getCookie } from "@/lib/helpers";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { branch_routes, employee_routes } from "@/lib/routePath";
import AddBranch from "./AddBranch";
import ViewBranch from "./ViewBranch";



const BranchPage = async () => {
  const allBranch = await getAllData(branch_routes.GETALL_BRANCH);
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];
  const corporationCookie = await getCookie("corporationtoken");
  
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

//  (permissions); 

  return (
    <>          
      <div className="w-full pl-4 p-2">
        <div className="h-9 flex items-center">
          <AdminNavBar link={"/pms/manage_branch"} name={"Manage Branch"} />                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              
        </div>
        <div className="space-y-2">
          <h1 className="text-2xl">Manage Branch</h1>
          <p className="text-sm text-gray-700">Here You Can Manage Branch</p>
        </div>
        <div className="w-full">
          <div className="flex justify-end">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["create-branch"]}
            >
            <AddBranch />
            </PermissionWrapper>
          </div>
          <div className="w-full py-4 animate-in fade-in duration-1000">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["view-branch"]}
            >
              <ViewBranch data={allBranch} permissions={permissions} />
            </PermissionWrapper>
          </div>
        </div>
      </div>
    </>
  );
};

export default BranchPage;