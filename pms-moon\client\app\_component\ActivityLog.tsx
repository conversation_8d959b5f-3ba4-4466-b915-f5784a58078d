import React, { useState } from "react";
import ActivityLogEntry from "./ActivityLogEntry";
import { FaHistory } from "react-icons/fa";

interface ActivityLogProps {
  auditLogs: any;
}
const ActivityLog: React.FC<ActivityLogProps> = ({ auditLogs }) => {
  const [click, setClick] = useState(false);
  const handleClick = () => {
    setClick(!click);
    setTimeout(() => {
      setClick(false);
    }, 3000);
  };
  return (
    <>
    
      <button>
        <FaHistory onClick={handleClick} className="h-5 w-5 text-main-color" />
      </button>
      <div className="relative ">
        {click && (
          <div className=" z-10 absolute bottom-5 h-auto overflow-clip   bg-gray-100 w-[24.5rem]  right-1 mx-1 shadow-3xl  text-main-color font-semibold border-2 border-main-color  rounded-lg  text-wrap">
            {Array.isArray(auditLogs) &&
              auditLogs.map((log: any, index: number) => (
                <ActivityLogEntry log={log} key={index} />
              ))}
          </div>
        )}
      </div>
    </>
  );
};

export default ActivityLog;
