"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const create_1 = require("../../controllers/clientCarrier/create");
const view_1 = require("../../controllers/clientCarrier/view");
const update_1 = require("../../controllers/clientCarrier/update");
const delete_1 = require("../../controllers/clientCarrier/delete");
const multer_1 = __importDefault(require("multer"));
const router = (0, express_1.Router)();
const DIR = "./src/public";
const storage = multer_1.default.diskStorage({
    destination: function (req, file, cb) {
        cb(null, DIR);
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + "-" + file.originalname);
    },
});
const upload = (0, multer_1.default)({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 },
}).single("file");
router.post('/excelClientCarrier', upload, create_1.excelClientCarrier); // clientCarrier
router.post("/create-setup", authentication_1.authenticate, create_1.createClientCarrier);
router.get("/get-all-setup", authentication_1.authenticate, view_1.viewClientCarrier);
router.get("/get-all-setupbyId/:id", authentication_1.authenticate, view_1.viewClientCarrierById);
router.put("/update-setup/:id", authentication_1.authenticate, update_1.updateClientCarrier);
router.delete("/delete-setup/:id", authentication_1.authenticate, delete_1.deleteClientCarrier);
exports.default = router;
//# sourceMappingURL=clientCarrierRoutes.js.map