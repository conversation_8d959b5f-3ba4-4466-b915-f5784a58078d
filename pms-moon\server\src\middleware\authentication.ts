import { NextFunction, response, Response } from "express";
import { handleError } from "../utils/helpers";
import jwt from "jsonwebtoken";

export const authenticate = async (
  req: any,
  res: Response,
  next: NextFunction
) => {
  try {
    // const cookie = req.headers.authorization(" ")[1];
    const cookie = req.headers.cookie;

    // const cookie = req.headers.cookie;

    if (!cookie) {
      return res
        .status(400)
        .json({ success: false, message: "No cookie found" });
    }
    const [type, token] = cookie?.split("=");

    let decoded;

    if (type && type === "customer") {
      decoded = jwt.verify(token, process.env.JWT_SECRET);

      const { id } = decoded;

      req.user_id = id;
  
      if (req.user_id) {
        const existingSession = await prisma.session.findUnique({
          where: {
            user_id: req.user_id,
          },
        });

        if (!existingSession) {
          return res.status(400).json({
            success: false,
            message: "Session expired or not valid",
          });
        }
      }
    } else {
      decoded = jwt.verify(cookie, process.env.JWT_SECRET);

      const { id } = decoded;

      req.user_id = id;
      if (req.user_id) {
        const existingSession = await prisma.session.findUnique({
          where: {
            user_id: req.user_id,
          },
        });

        if (!existingSession) {
          return res.status(400).json({
            success: false,
            message: "Session expired or not valid",
          });
        }
      }
    }
    const { corporation_id } = decoded;
    //  ("coo", corporation_id);

    req.corporation_id = corporation_id;
    //  ("user_id in authenticate:", req.user_id);
    //  ("Decoded token:", decoded);
    next();
  } catch (error) {
    return handleError(res, error);
  }
};

export const authenticateSuperAdmin = async (
  req,
  res: Response,
  next: NextFunction
) => {
  try {
    const cookie = req.headers.authorization?.split(" ")[1];
    //  ("cooke", cookie);
    if (!cookie) {
      return res
        .status(400)
        .json({ success: false, message: "No token found" });
    }
    const decoded: any = jwt.verify(cookie, process.env.JWT_SECRET);
    const { super_admin_id } = decoded;

    req.super_admin_id = super_admin_id;
    next();
  } catch (error) {
    return handleError(req, error);
  }
};
