import { Request, Response } from "express";
import { handleError } from "../../utils/helpers";

export const logoutSuperAdmin = async (req:Request, res: Response): Promise<any> => {
  try {
    const {cookieTobeDeleted} = req.body;
    //  ('cookiedelete superadmin',cookieTobeDeleted);
     if (!cookieTobeDeleted) {
      return res.status(400).json({
        success: false,
        error: "No token provided for logout.",
      });
    }
    return res.clearCookie('superadmintoken', {
      httpOnly: true,
    }).status(200).json({
      success: true,
      message: "Logout successful",
    });
  } catch (error) {
    handleError(res, error);
  }
};
