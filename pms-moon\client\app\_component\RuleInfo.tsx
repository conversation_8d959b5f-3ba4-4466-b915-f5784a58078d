import React, { useState } from "react";
import { FaInfoCircle } from "react-icons/fa";

interface InfoProps {
  info: any;
}

const RuleInfo: React.FC<InfoProps> = ({ info }) => {
  const [click, setClick] = useState(false);
  const handleClick = () => {
    setClick(!click);
    setTimeout(() => {
      setClick(false);
    }, 8000);
  };
  return (
    <>
      <button type="button" onClick={handleClick}>
        <FaInfoCircle className=" w-5 h-5 hover:text-gray-400 text-gray-400/45 cursor-pointer" />
      </button>
      <div className="relative   ">
        {click && (
          <div className="z-10 absolute top-1 right-1 h-auto overflow-clip text-[0.8rem] bg-gray-100 w-[24.5rem] mx-1 p-1   text-gray-400 border-2 border-gray-300 rounded-md  text-wrap">
            {info.map((item: any, index: any) => (
              <p className="mb-1" key={index}>
                {item}
              </p>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default RuleInfo;
