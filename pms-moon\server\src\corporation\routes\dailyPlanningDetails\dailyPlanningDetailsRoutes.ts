import { Router } from "express";
import { authenticate } from "../../../middleware/authentication";
import {
  createDailyPlanningDetails,
  excelDailyPlanningDetails,
} from "../../controllers/dailyPlanningDetails/create";
import {
  viewDailyPlanningDetails,
  viewDailyPlanningDetailsByType,
 
  viewDailyPlanningDetailsDetailsManual,
 
  viewSpecificDailyPlanningDetails,
} from "../../controllers/dailyPlanningDetails/view";
import { updateDailyPlanningDetails, updateDailyPlanningDetails_Statement } from "../../controllers/dailyPlanningDetails/update";
import { deleteDailyPlanningDetails } from "../../controllers/dailyPlanningDetails/delete";
import { upload } from "../uploadFile/uploadfilerouter";

const router = Router();

router.post(
  "/create-dailyplanningdetails/:id",
  authenticate,
  createDailyPlanningDetails
);

router.put(
  "/update-dailyplanningdetails-statement/:id",
  authenticate,
  updateDailyPlanningDetails_Statement
);

router.post(
  "/excel-dailyplanningdetails/:id",

  upload.single("file"),
  excelDailyPlanningDetails
);

router.get(
  "/get-all-dailyplanningdetails",
  // authenticate,
  viewDailyPlanningDetails
);
router.get(
  "/get-specific-dailyplanningdetails/:id",
  // authenticate,
  viewSpecificDailyPlanningDetails
);
router.get(
  "/:id/type/:type",
  // authenticate,
  viewDailyPlanningDetailsDetailsManual
);


router.put(
  "/update-dailyplanningdetails/:id",
  authenticate,
  updateDailyPlanningDetails
);
router.delete(
  "/delete-dailyplanningdetails/:id",
  authenticate,
  deleteDailyPlanningDetails
);

router.get("/:id/:type", authenticate, viewDailyPlanningDetailsByType);

export default router;
