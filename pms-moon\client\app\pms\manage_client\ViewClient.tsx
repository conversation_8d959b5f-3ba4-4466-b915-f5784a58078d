"use client";
import React, { useState } from "react";
import { Client, column, Carrier } from "./column";
import ServerSideDataTable from "@/app/_component/ServerSideDataTable";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import DataGridTable from "@/app/_component/DataGridTable";

export const ViewClient = ({
  alldata,
  permissions,
  allBranch,
  allUser,
  allAssociate,
  userData
}: any) => {
  //  (typeof Column)
  //  (permissions)
  //
  //  (allCarrier);

  const [totallength, setTotallength] = useState<number>(0);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams);
  const pathname = usePathname();
   const router = useRouter(); 

  const pageSizedata = parseInt(searchParams.get("pageSize"));
  const totaldatalength = Math.ceil(
    alldata?.datalength / (pageSizedata ? pageSizedata : 50)
  );

  const pageSize = Number(searchParams.get("pageSize"));

  return (
    <div>
      <DataGridTable
        data={alldata?.data}
        columns={column(permissions, allBranch, allUser, allAssociate,userData, router)}
        // filter
        // filter_column="client_name"
        showColDropDowns
        showPageEntries
        // filter2
        // filter_column2="owner_name"
        pageSize={pageSize}
        totalPages={totallength ? totallength : totaldatalength}
      />
    </div>
  );
};
