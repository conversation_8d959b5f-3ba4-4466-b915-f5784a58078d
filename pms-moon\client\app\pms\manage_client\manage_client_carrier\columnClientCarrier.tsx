"use client";
import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import DeleteRow from "@/app/_component/DeleteRow";
import { setup_routes } from "@/lib/routePath";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import UpdateClientCarrier from "./UpdateClientCarrier";

export interface ClientCarrier {
  id: any;
  client_id: any;
  client: Client;
  corporation_id: any;
  carrier_id: any;
  carrier: Carrier;
  payment_terms: any;
  created_at: any;
  updated_at: any;
}
export interface Client {
  client_id: number;
  corporation_id?: number | null;
  client_name: string;
  owner_name: string;
  city: string;
  state: string;
  country: string;
  address?: string | null;
  phone_number: string;
  postalcode: string;
  created_at: string; // or Date
  updated_at: string; // or Date
  //   WorkReport: WorkReport[]; //
}
export interface Carrier {
  carrier_id: number;
  name: string;
  register1?: string | null;
  code: string;
  country?: string | null;
  state?: string | null;
  city?: string | null;
  phone: string;
  postalcode?: string | null;
  address?: string | null;
  created_at: string; // or Date
  updated_at: string; // or Date
  corporation_id?: number | null;
  //   WorkReport: WorkReport[];
}

const ColumnClientCarrier = (
  permissions: string[],
  allclient: Client[],
  allcarrier: Carrier[]
): ColumnDef<ClientCarrier>[] => [
  {
    accessorKey: "client_name",
    header: "Client Name",
    accessorFn: (row) => row.client?.client_name,
    cell: ({ row }) => {
      return row.original.client?.client_name;
    },
  },
  {
    accessorKey: "name",
    header: "Carrier Name",
    accessorFn: (row) => row.carrier?.name,
    cell: ({ row }) => {
      return row?.original.carrier?.name;
    },
  },
  {
    accessorKey: "payment_terms",
    header: "Payment Terms",
  },

  {
    accessorKey: "action",
    header: "Action",
    id: "action",
    cell: ({ row }) => {
      const client = row?.original;
      //  (client)
      return (
        <div className="flex items-center">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["update-setup"]}
          >
            <UpdateClientCarrier
              data={client}
              allclient={allclient}
              allcarrier={allcarrier}
            />
          </PermissionWrapper>

          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["delete-setup"]}
          >
            <DeleteRow
              route={`${setup_routes.DELETE_SETUP}/${client?.id}`}
            />
          </PermissionWrapper>
        </div>
      );
    },
  },
];
export default ColumnClientCarrier;
