"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTransactions = void 0;
const createTransactions = async ({ model01, fieldName01, fields01, logging_relationship01, model02, fieldName02, fields02, logging_relationship02, model03, fieldName03, fields03, logging_relationship03, res, req, successMessage, }) => {
    try {
        // const { freightadminid, freightuserid } = req;
        // const user_role =
        //   freightadminid && freightuserid ? "FREIGHT_ADMIN_USER" : "FREIGHT_ADMIN";
        // const userId =
        //   freightadminid && freightuserid ? freightuserid : freightadminid;
        await prisma.$transaction(async (tx) => {
            const result = await tx[model01].create({
                data: {
                    ...fields01,
                },
            });
            const id = result[fieldName01];
            for (let i = 0; i < fields02.length; i++) {
                await tx[model02].create({
                    data: {
                        ...fields02[i],
                        [fieldName01]: id,
                    },
                });
            }
            return res.status(200).json({
                success: true,
                message: successMessage,
            });
        });
    }
    catch (error) {
    }
};
exports.createTransactions = createTransactions;
//# sourceMappingURL=createTransaction.js.map