import { createItem } from "../../../utils/operation";


export const createLegrandMapping = async (req, res) => {
    const { corporationID } = req;

    const fields = {
        businessUnit: req.body.businessUnit,
        legalName: req.body.legalName,
        customeCode: req.body.customeCode,
        shippingBillingName: req.body.shippingBillingName,
        shippingBillingAddress: req.body.shippingBillingAddress,
        location: req.body.location,
        zipPostal: req.body.zipPostal,
        aliasCity: req.body.aliasCity,
        aliasShippingNames: req.body.aliasShippingNames,
        corporationId: Number(corporationID),
    };

    await createItem({
        model: "LegrandMapping",
        fieldName: "id",
        fields: fields,
        res: res as Response,
        req: req,
        successMessage: "LegrandMapping has been created",
    });
};