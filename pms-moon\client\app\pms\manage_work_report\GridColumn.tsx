// "use client";

// import { AgGridReact } from "ag-grid-react";
// import { useRouter } from "next/navigation";
// import UpdateWorkReport from "./UpdateWorkReport";
// import { PermissionWrapper } from "@/lib/permissionWrapper";
// import DeleteRow from "@/app/_component/DeleteRow";
// import { formatDate } from "@/lib/swrFetching";
// import { workreport_routes } from "@/lib/routePath";
// import { WorkReportContext } from "./WorkReportContext";
// import { useContext, useState, useEffect, useRef } from "react";
// import { AllCommunityModule, Column, ModuleRegistry } from "ag-grid-community";
// import { getAllData } from "@/lib/helpers";
// import Pagination from "@/app/_component/Pagination";
// import { formatTimeZone } from "@/lib/swrFetching";
// import { Button } from "@/components/ui/button";
// import { AiOutlineLoading3Quarters } from "react-icons/ai";
// import { Calendar, Search } from "lucide-react";
// import {
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from "@/components/ui/popover";
// import { Calendar as CalendarComponent } from "@/components/ui/calendar";
// import { format } from "date-fns";
// import { cn } from "@/lib/utils";
// import ExportReport from "./ExportReport";
// import { ColumnDef } from "@tanstack/react-table";
// import { Data } from "react-csv/lib/core";

// ModuleRegistry.registerModules([AllCommunityModule]);

// export interface WorkReport {
//   work_report_id: number;
//   date: string;
//   id: number;
//   user: User;
//   client_id: number;
//   client: Client;
//   carrier_id?: number | null;
//   carrier?: Carrier | null;
//   work_type_id: number;
//   work_type: WorkType;
//   category: any;
//   planning_nummbers?: string | null;
//   expected_time: string;
//   actual_number?: string | null;
//   start_time: string;
//   finish_time: string;
//   time_spent: string;
//   notes: string;
//   created_at: string;
//   updated_at: string;
//   permissions: any[];
//   task_type: any;
// }
// interface User {
//   id: number;
//   corporation_id?: number | null;
//   user_type: "HR" | "TL" | "CSA" | "NORMAL_MEMBER"; // Enum for user types
//   role_id: number;
//   email: string;
//   username: string;
//   password: string;
//   created_at: string;
//   updated_at: string;
//   WorkReport: WorkReport[]; // Assuming you have a WorkReport interface defined
// }
// interface Client {
//   client_id: number;
//   corporation_id?: number | null;
//   client_name: string;
//   owner_name: string;
//   city: string;
//   state: string;
//   country: string;
//   address?: string | null;
//   phone_number: string;
//   postalcode: string;
//   created_at: string; // or Date
//   updated_at: string; // or Date
//   WorkReport: WorkReport[]; // Assuming you have a WorkReport interface defined
// }
// interface Carrier {
//   carrier_id: number;
//   name: string;
//   register1?: string | null;
//   code: string;
//   country?: string | null;
//   state?: string | null;
//   city?: string | null;
//   phone: string;
//   postalcode?: string | null;
//   address?: string | null;
//   created_at: string; // or Date
//   updated_at: string; // or Date
//   corporation_id?: number | null;
//   WorkReport: WorkReport[]; // Assuming you have a WorkReport interface defined
// }
// interface WorkType {
//   id: number;
//   work_type: string;
//   created_at: string; // or Date
//   updated_at: string;
//   WorkReport: WorkReport[];
//   category: any;
// }

// const GridColumn = ({ permissions }:any) => {
//   const [rowData, setRowData] = useState([]);
//   const [totalRows, setTotalRows] = useState(0);
//   const [page, setPage] = useState(1);
//   const [pageSize, setPageSize] = useState(50);
//   const [isLoading, setIsLoading] = useState(false);
//   const [fromDate, setFromDate] = useState<Date | undefined>(undefined);
//   const [toDate, setToDate] = useState<Date | undefined>(undefined);

//   const gridRef = useRef<any>(null);

//   // State for holding filter model
//   const [filterModel, setFilterModel] = useState<any>(null);

//   // onFilterChanged event handler to capture the filter model
//   const onFilterChanged = () => {
//     if (gridRef.current) {
//       const model = gridRef.current.api.getFilterModel();
//       setFilterModel(model); // Set the filter model to the state
//     }
//   };

//   // Fetch data with filters
//   const showData = async () => {
//     try {
//       const filters = {
//         fDate: fromDate ? format(fromDate, "yyyy-MM-dd") : null,
//         tDate: toDate ? format(toDate, "yyyy-MM-dd") : null,
//         Client: filterModel?.client_name?.filter || null,
//         Carrier: filterModel?.carriername?.filter || null,
//         Username: filterModel?.user?.filter || null,
//         Work: filterModel?.work_type?.filter || null,
//         Category: filterModel?.category?.filter || null,
//         Type: filterModel?.task_type?.filter || null,
//         Workstatus: filterModel?.work_status?.filter || null,
//         ActualNumber: filterModel?.actual_number?.filter || null,
//         Notes: filterModel?.notes?.filter || null,
//         page: page,
//         pageSize: pageSize,
//       };

//       // Remove null/undefined values to prevent them from being sent as query params
//       const cleanFilters = Object.fromEntries(
//         Object.entries(filters).filter(
//           ([_, value]) => value !== null && value !== undefined
//         )
//       );

//       const queryParams = new URLSearchParams(cleanFilters).toString();

//       // Fetch data with query parameters
//       const response = await getAllData(
//         `${workreport_routes.GETALL_WORKREPORT}?${queryParams.toString()}`
//       );

//       setRowData(response.data);
//       setTotalRows(response.totalRows);
//     } catch (error) {
//       console.error("Error fetching work reports", error);
//     }
//   };

//   // Trigger data fetching on filter change, pagination, or date range change
//   useEffect(() => {
//     showData();
//   }, [filterModel, page, pageSize, fromDate, toDate]);

//   // Handle page changes
//   const onPageChange = (newPage: number) => {
//     setPage(newPage);
//   };

//   // Handle page size change
//   const onPageSizeChanged = (event: any) => {
//     const newPageSize = event.target.value;
//     setPageSize(newPageSize);
//     setPage(1);
//   };

//   // Reset the date filters
//   const resetDateFilters = () => {
//     setFromDate(undefined);
//     setToDate(undefined);
//   };

//   // Column definitions for AG Grid
//   const columnDefs = [
//     {
//       headerName: "Sr No.",
//       valueGetter: (params) => {
//         const serialNo = (page - 1) * pageSize + params.node.rowIndex + 1;
//         return serialNo;
//       },
//       sortable: false,
//       width: 80,
//     },
//     {
//       field: "date",
//       headerName: "Date",
//       valueFormatter: (params) =>
//         params.value ? formatDate(params.value) : "No Date",
//       filterParams: {
//         comparator: (filterLocalDateAtMidnight, cellValue) => {
//           const cellDate = new Date(cellValue);
//           if (filterLocalDateAtMidnight < cellDate) return 1;
//           if (filterLocalDateAtMidnight > cellDate) return -1;
//           return 0;
//         },
//       },
//       width: 100,
//     },
//     {
//       field: "user",
//       headerName: "Username",
//       valueGetter: (params) => params.data?.user?.username || "No Username",
//       filter: "agTextColumnFilter",
//       width: 120,
//     },
//     {
//       field: "client_name",
//       headerName: "Client",
//       valueGetter: (params) =>
//         params.data?.client?.client_name || "No Client Name",
//       filter: "agTextColumnFilter",
//       width: 120,
//     },
//     {
//       field: "carriername",
//       headerName: "Carrier",
//       filter: "agTextColumnFilter",
//       valueGetter: (params) => params.data?.carrier?.name || "No Carrier",
//       width: 120,
//     },
//     {
//       field: "work_type",
//       headerName: "Work",
//       filter: "agTextColumnFilter",
//       valueGetter: (params) =>
//         params.data?.work_type?.work_type || "No Work Type",
//       width: 120,
//     },
//     {
//       field: "category",
//       headerName: "Category",
//       valueGetter: (params) =>
//         params.data?.category?.category_name || "No Category",
//       filter: "agTextColumnFilter",
//       width: 120,
//     },
//     {
//       field: "task_type",
//       headerName: "Type",
//       valueGetter: (params) => params.data?.task_type || "No Task Type",
//       width: 120,
//     },
//     {
//       field: "actual_number",
//       headerName: "ActualNumber",
//       valueGetter: (params) => params.data?.actual_number || "No Actual Number",
//       filter: "agNumberColumnFilter",
//       width: 120,
//     },
//     {
//       field: "start_time",
//       headerName: "Start Time",
//       valueGetter: (params) => formatTimeZone(params.data?.start_time || ""),
//       width: 120,
//     },
//     {
//       field: "finish_time",
//       headerName: "Finish Time",
//       valueGetter: (params) => formatTimeZone(params.data?.finish_time || ""),
//       width: 120,
//     },
//     {
//       field: "time_spent",
//       headerName: "Time Spent",
//       valueGetter: (params) => params.data?.time_spent || "No Time Spent",
//       width: 120,
//     },
//     {
//       field: "notes",
//       headerName: "Notes",
//       valueGetter: (params) => params.data?.notes || "No Notes",
//       width: 120,
//     },
//     {
//       field: "action",
//       headerName: "Action",
//       cellRenderer: (params) => {
//         return (
//           <div className="flex items-center">
//             <PermissionWrapper
//               permissions={permissions}
//               requiredPermissions={["update-workReport"]}
//             >
//               <UpdateWorkReport workReport={params?.data} />
//             </PermissionWrapper>
//             <PermissionWrapper
//               permissions={permissions}
//               requiredPermissions={["delete-workReport"]}
//             >
//               <DeleteRow
//                 route={`${workreport_routes.DELETE_WORKREPORT}/${params.data?.id}`}
//                 onSuccess={() => showData()}
//               />
//             </PermissionWrapper>
//           </div>
//         );
//       },
//       width: 120,
//     },
//   ];

//   return (
//     <>
//       <div
//         style={{
//           width: "100%",
//           height: "100vh",
//           display: "flex",
//           flexDirection: "column",
//         }}
//       >
//         {/* <Button onClick={downloadReport}>
//             {isLoading ? (
//               <span className="animate-spin">
//                 <AiOutlineLoading3Quarters />
//               </span>
//             ) : (
//               "DOWNLOAD REPORT"
//             )}
//             {isLoading ? " EXPORTING..." : ""}
//           </Button> */}
//         <div className="flex justify-between mb-2">
//           <div className="flex gap-2">
//             {/* Date Range Picker */}
//             <Popover>
//               <PopoverTrigger asChild>
//                 <Button
//                   variant="outline"
//                   className={cn(
//                     "w-full sm:w-[170px] justify-start text-left font-normal",
//                     !fromDate && "text-muted-foreground"
//                   )}
//                 >
//                   <Calendar className="mr-2 h-4 w-4" />
//                   {fromDate ? format(fromDate, "PPP") : "From date"}
//                 </Button>
//               </PopoverTrigger>
//               <PopoverContent className="w-auto p-0" align="start">
//                 <CalendarComponent
//                   mode="single"
//                   selected={fromDate}
//                   onSelect={setFromDate}
//                   initialFocus
//                 />
//               </PopoverContent>
//             </Popover>

//             <Popover>
//               <PopoverTrigger asChild>
//                 <Button
//                   variant="outline"
//                   className={cn(
//                     "w-full sm:w-[170px] justify-start text-left font-normal",
//                     !toDate && "text-muted-foreground"
//                   )}
//                 >
//                   <Calendar className="mr-2 h-4 w-4" />
//                   {toDate ? format(toDate, "PPP") : "To date"}
//                 </Button>
//               </PopoverTrigger>
//               <PopoverContent className="w-auto p-0" align="start">
//                 <CalendarComponent
//                   mode="single"
//                   selected={toDate}
//                   onSelect={setToDate}
//                   initialFocus
//                 />
//               </PopoverContent>
//             </Popover>

//             {(fromDate || toDate) && (
//               <Button
//                 variant="ghost"
//                 onClick={resetDateFilters}
//                 className="bg-gray-100"
//               >
//                 Clear dates
//               </Button>
//             )}
//           </div>
          
//           <div>
//             <ExportReport
//               filterModel={filterModel}
//               permissions={permissions}
//               fromDate={fromDate}
//               toDate={toDate}
//             />
//           </div>
//         </div>

//         <AgGridReact
//           ref={gridRef}
//           rowData={rowData}
//           columnDefs={columnDefs}
//           onFilterChanged={onFilterChanged} // Listen for filter changes
//           // pagination={true}
//           // paginationPageSize={pageSize}
//         />

//         <div>
//           {rowData && (
//             <Pagination
//               currentPage={page}
//               totalPages={Math.ceil(totalRows / pageSize)}
//               onPageChange={onPageChange}
//             />
//           )}
//         </div>
//       </div>
//     </>
//   );
// };

// export default GridColumn;
