"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logout = exports.sessionlogout = void 0;
const helpers_1 = require("../../utils/helpers");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const sessionlogout = async (req, res) => {
    try {
        const { cookieTobeDeleted } = req.body;
        //  ('cookiedelete',cookieTobeDeleted);
        if (!cookieTobeDeleted) {
            return res.status(400).json({
                success: false,
                error: "No token provided for logout.",
            });
        }
        if (req.cookies["corporationtoken"]) {
            return res
                .clearCookie("corporationtoken", {
                httpOnly: true,
                // secure: process.env.NODE_ENV === "production",
                // sameSite: "strict",
            })
                .status(200)
                .json({
                success: true,
                message: "Logout successful",
            });
        }
        else if (req.cookies["token"]) {
            const decodedToken = jsonwebtoken_1.default.decode(req.cookies["token"]);
            const userId = decodedToken?.id;
            await prisma.session.deleteMany({
                where: { user_id: userId },
            });
            return res
                .clearCookie("token", {
                httpOnly: true,
                // secure: process.env.NODE_ENV === "production",
                // sameSite: "strict",
                maxAge: 0,
            })
                .status(200)
                .json({
                success: true,
                message: "Logout successful",
            });
        }
        else {
            return res.status(400).json({
                success: false,
                error: "No token found for logout.",
            });
        }
    }
    catch (error) {
        (0, helpers_1.handleError)(res, error);
    }
};
exports.sessionlogout = sessionlogout;
const logout = async (req, res) => {
    try {
        const { cookieTobeDeleted } = req.body;
        if (!cookieTobeDeleted) {
            return res.status(400).json({
                success: false,
                error: "No token provided for logout.",
            });
        }
        if (req.cookies["corporationtoken"]) {
            return res
                .clearCookie("corporationtoken", {
                httpOnly: true,
                // secure: process.env.NODE_ENV === "production",
                // sameSite: "strict",
            })
                .status(200)
                .json({
                success: true,
                message: "Logout successful",
            });
        }
        else if (req.cookies["token"]) {
            return res
                .clearCookie("token", {
                httpOnly: true,
                // secure: process.env.NODE_ENV === "production",
                // sameSite: "strict",
                maxAge: 0,
            })
                .status(200)
                .json({
                success: true,
                message: "Logout successful",
            });
        }
        else {
            return res.status(400).json({
                success: false,
                error: "No token found for logout.",
            });
        }
    }
    catch (error) {
        (0, helpers_1.handleError)(res, error);
    }
};
exports.logout = logout;
//# sourceMappingURL=logout.js.map