"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateRolesPermission = void 0;
const utility_1 = require("./utility");
const permissions_1 = require("../../../utils/permissions");
const updateRolesPermission = async (req, res) => {
    const { name, permission } = req.body;
    const role_id = Number(req.params.id);
    //  (role_id)
    const hasPermission = await (0, permissions_1.checkUserPermission)({
        req: req,
        res: res,
        action: "USER MANAGEMENT",
        permissiontype: "ROLE MANAGEMENT",
    });
    if (hasPermission) {
        await (0, utility_1.updateTransaction)({
            model: "Roles",
            fieldName: "id",
            id: role_id,
            data: { name, permission },
            res,
            req,
            logging_relationship: "rolesRole_id",
            successMessage: "Role updated successfully",
        });
    }
};
exports.updateRolesPermission = updateRolesPermission;
//# sourceMappingURL=update.js.map