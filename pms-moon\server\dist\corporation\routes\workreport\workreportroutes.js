"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/workreport/create");
const view_1 = require("../../controllers/workreport/view");
const update_1 = require("../../controllers/workreport/update");
const delete_1 = require("../../controllers/workreport/delete");
const authentication_1 = require("../../../middleware/authentication");
const checkPermission_1 = require("../../../middleware/checkPermission");
const manualcreate_1 = require("../../controllers/workreport/manualcreate");
const UpdateWorkReport_1 = require("../../controllers/workreport/UpdateWorkReport");
const exportservice_1 = require("../../controllers/workreport/exportservice");
// import { updateWorkReports } from "../../controllers/workreport/UpdateworkReport";
const router = (0, express_1.Router)();
router.post("/create-workreport", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORK REPORT", "create-workReport"), create_1.createWorkreport);
router.post("/create-workreport-manually", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORK REPORT", "create-workReport"), manualcreate_1.createWorkreportManually);
router.get("/get-all-workreport", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORK REPORT", "view-workReport"), view_1.viewWorkreport);
router.get("/get-user-workreport/:id", authentication_1.authenticate, update_1.updateWorkReport);
router.get("/get-all-clientworkreport", view_1.viewclientWorkreport);
router.get("/get-all-userworkreport", authentication_1.authenticate, view_1.viewuserWorkreport);
router.get("/get-all-worktypeworkreport", view_1.viewworktypeWorkreport);
router.get("/get-all-categoryworkreport", view_1.viewcategoryWorkreport);
router.get("/get-current-user-workreport", authentication_1.authenticate, view_1.getCurrentUserWorkReport);
router.put("/update-workreport/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORK REPORT", "update-workReport"), update_1.updateWorkReport);
router.delete("/delete-workreport/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORK REPORT", "delete-workReport"), delete_1.deleteWorkreport);
router.put("/update-workreports/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("WORK REPORT", "update-workReport"), UpdateWorkReport_1.updateWorkReports);
router.get("/", authentication_1.authenticate, view_1.getCurrentUserWorkReportStatusCount);
router.get("/get-workreport", 
// authenticate,
exportservice_1.exportWorkReportToExcel);
exports.default = router;
//# sourceMappingURL=workreportroutes.js.map