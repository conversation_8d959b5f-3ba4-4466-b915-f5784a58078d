import React from "react";

import { getAllData, getCookie } from "@/lib/helpers";
import { carrier_routes, category_routes, employee_routes } from "@/lib/routePath";

import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import AddCategory from "./AddCategory";
import ViewCategory from "./ViewCategory";

const CategoryPage = async () => {
  const allCarrier = await getAllData(category_routes.GETALL_CATEGORY);

  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];
  

  const corporationCookie = await getCookie("corporationtoken");
 
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;


  return (
    <div className="w-full p-2 pl-4">
      <div className="h-9 flex items-center">
        <AdminNavBar link={"/pms/manage_category"} name={"Manage Category"} />
      </div>
      <div className="space-y-2">
        <h1 className="text-2xl">Manage Category</h1>
        <p className="text-sm text-gray-700">Here You Can Manage Category</p>
      </div>
      <div className="w-full">
        <div className="flex justify-end">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["create-category"]}
          >
            <AddCategory />
          </PermissionWrapper>
        </div>
        <div className="w-full py-4 animate-in fade-in duration-1000">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["view-category"]}
          >
            <ViewCategory data={allCarrier} permissions={permissions} />
          </PermissionWrapper>
        </div>
      </div>
    </div>
  );
};

export default CategoryPage;
