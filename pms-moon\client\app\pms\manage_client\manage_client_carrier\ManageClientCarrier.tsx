"use client";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import React, { useEffect, useState } from "react";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { ViewClientCarrier } from "./ViewClientCarrier";
import ClientCarrierSetup from "./clientcarriersetup";
import { Button } from "@/components/ui/button";
import * as XLSX from 'xlsx';
import { ClientCarrierImport } from "./ClientCarrierImport";

const ManageClientCarrier = ({
  permissions,
  allCarriersetup,
  allClient,
  client_id,
  allCarrier,
}: any) => {

  const handleExportClientCarrier = async () => {

    const datas = allCarriersetup?.map((clientCarrier: any) => ({
      'Client_name': clientCarrier.client.client_name || "",
      'Carrier_name': clientCarrier.carrier.name || "",
      'Payment Terms': clientCarrier.payment_terms || "",

    }))


    const ws = XLSX.utils.json_to_sheet(datas)
    const wb = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(wb, ws, 'client')


    const excelFile = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })

    const blob = new Blob([excelFile], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheet.sheet'
    })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = 'clientCarrier.xlsx'
    link.click()
  }
  return (
    <div className="w-full pl-4">
      <div className="h-9 flex items-center">
        <AdminNavBar
             link="/pms/manage_client"
             name="Manage Client"
             link1={`/pms/manage_client/manage_client_carrier/${client_id}`}
             name1="Manage Client Carrier"
            />
      </div>
      <div className="space-y-2">
        <h1 className="text-2xl">Manage Client Carrier </h1>
        <p className="text-sm text-gray-700">Here you can manage client carrier</p>
      </div>
      <div className="flex items-center">
        <ClientCarrierSetup
          allCarriersetup={allCarriersetup}
          client_id={client_id}
          allclient={allClient}
          allCarrier={allCarrier}
        />
      </div>


      <div className="text-end m-2 space-x-2" >
        <Button onClick={handleExportClientCarrier} >Export</Button>
        <ClientCarrierImport />
      </div>
      <div className="w-full pr-2">
        <PermissionWrapper
          permissions={permissions}
          requiredPermissions={["view-setup"]}
        >
          <div className="w-full  animate-in fade-in duration-1000">
            <ViewClientCarrier
              data={allCarriersetup}
              permissions={permissions}
              allclient={allClient}
              allcarrier={allCarrier}
            />
          </div>
        </PermissionWrapper>
      </div>
    </div>
  );
};

export default ManageClientCarrier;
