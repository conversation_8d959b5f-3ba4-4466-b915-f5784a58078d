"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWorktype = void 0;
const operation_1 = require("../../../utils/operation");
const createWorktype = async (req, res) => {
    const fields = {
        work_type: req.body.work_type,
        category_id: Number(req.body.category),
        is_work_carrier_specific: req.body.is_work_carrier_specific,
        does_it_require_planning_number: req.body.does_it_require_planning_number,
        is_backlog_regular_required: req.body.is_backlog_regular_required,
    };
    await (0, operation_1.createItem)({
        model: "workType",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "WorkType has been created",
    });
};
exports.createWorktype = createWorktype;
//# sourceMappingURL=create.js.map