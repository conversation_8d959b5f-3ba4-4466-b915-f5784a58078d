"use client";
import React, { useEffect, useState } from "react";
import { Car } from "lucide-react";
import {
  carrier_routes,
  client_routes,
  employee_routes,
  workreport_routes,
  worktype_routes,
} from "@/lib/routePath";
import { getAllData, getCookie } from "@/lib/helpers";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import Sidebar from "@/components/sidebar/Sidebar";
import { NavBar } from "@/app/_component/NavBar";
import Parent from "./parent";
import { useRouter } from "next/navigation";
import { useSession } from "@/lib/useSession";
import { Dialog, DialogContent } from "@/components/ui/dialog";

const Manage = ({
  allDailyPlanning,
  permissions,
  userData,
  actions,
  allClient,
}: any) => {
  const [isLogoutConfirmationOpen, setIsLogoutConfirmationOpen] =
    useState(false);
  const { checkSessionToken, isSessionValid } = useSession(userData);

  useEffect(() => {
    const validateSession = async () => {
      const sessionValid = await checkSessionToken();

      if (!sessionValid || !isSessionValid) {
        setIsLogoutConfirmationOpen(true);
      }
    };

    validateSession();
  }, []);

  return (
    <>
      {isLogoutConfirmationOpen && (
        <Dialog open={isLogoutConfirmationOpen}>
          <DialogContent>
            <div className="text-center">
              <p className="mb-4">
                You are already logged in on another device.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
      <SidebarProvider>
        <Sidebar {...{ permissions, profile: userData }} />

        <div className="p-5 mt-8 w-full">
          <Parent
            allDailyPlanning={allDailyPlanning}
            permissions={actions}
            allClient={allClient}
            userData={userData}
          />
        </div>
      </SidebarProvider>
    </>
  );
};

export default Manage;
