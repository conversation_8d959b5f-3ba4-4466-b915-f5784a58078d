"use client";
import { Checkbox } from "@/components/ui/checkbox";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";

interface CheckBoxProps {
  form: any;
  name: string;
  label: string;
}

function CheckboxInputComponent({ form, name, label }: CheckBoxProps) {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        return (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 border-none">
            <FormControl>
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <div className=" leading-none border-none cursor-pointer">
              <FormLabel className="cursor-pointer tracking-wide text-[1rem]">
                {" "}
                {label}{" "}
              </FormLabel>
            </div>
          </FormItem>
        );
      }}
    />
  );
}
export default CheckboxInputComponent;
