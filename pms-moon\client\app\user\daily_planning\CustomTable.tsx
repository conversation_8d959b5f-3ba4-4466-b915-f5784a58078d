"use client";
import { useEffect, useState, useCallback } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Calendar, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import Pagination from "@/app/_component/Pagination";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatDate } from "@/lib/swrFetching";
import AllTables from "./AllTables";
import { daily_planning } from "@/lib/routePath";
import DeleteRow from "@/app/_component/DeleteRow";
import UpdateDailyPlanning from "./UpdateDailyPlanning";
import { getAllData } from "@/lib/helpers";
import { TableSkeletonRow } from "@/app/_component/TableSkeletonRow";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import { LuUpload } from "react-icons/lu";
import { FaPlus } from "react-icons/fa";

type User = {
  id: number;
  corporation_id: number | null;
  role_id: number;
  firstName: string;
  lastName: string;
  email: string;
  username: string;
  password: string;
  level: number;
  parent_id: number;
  created_at: string;
  updated_at: string;
};

type Client = {
  client_id: number;
  corporation_id: number;
  client_name: string;
  ownership: string;
  user_id: number | null;
  owner_name: string | null;
  country: string | null;
  branch_id: number;
  created_at: string;
  updated_at: string;
};

type PlanningData = {
  id: number;
  corporation_id: number | null;
  corporation: any; // Adjust if needed
  user_id: number;
  user: User;
  daily_planning_date: string;
  client_id: number;
  client: Client;
};

type Pagination = {
  totalRecords: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
};

type PlanningState = {
  data: PlanningData[];
  pagination: Pagination;
};

// Debounce function
function useDebounce(value: string, delay: number): string {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

const CustomTable = ({ permissions, allClient, refresh, userData }: any) => {
  const [searchInput, setSearchInput] = useState("");
  const debouncedSearchTerm = useDebounce(searchInput, 500); // 500ms delay

  const [currentPage, setCurrentPage] = useState(1);
  const [fromDate, setFromDate] = useState<Date | undefined>(undefined);
  const [toDate, setToDate] = useState<Date | undefined>(undefined);
  const [itemsPerPage, setItemsPerPage] = useState(50);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState<PlanningState>({
    data: [],
    pagination: {
      totalRecords: 0,
      totalPages: 0,
      currentPage: 1,
      pageSize: 50,
    },
  });
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Build query params based on current filters
  const buildQueryParams = useCallback(() => {
    return new URLSearchParams({
      page: currentPage.toString(),
      pageSize: itemsPerPage.toString(),
      ...(debouncedSearchTerm ? { search: debouncedSearchTerm } : {}),
      ...(fromDate ? { fDate: format(fromDate, "yyyy-MM-dd") } : {}),
      ...(toDate ? { tDate: format(toDate, "yyyy-MM-dd") } : {}),
    }).toString();
  }, [currentPage, itemsPerPage, debouncedSearchTerm, fromDate, toDate]);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const queryParams = buildQueryParams();
      const url = `${daily_planning.GETSPECIFIC_DAILY_PLANNING}?${queryParams}`;
      const res = await getAllData(url);
      setData(res);
    } catch (err) {
      console.error("Error fetching data:", err);
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  }, [buildQueryParams]);

  // Reset to first page when search term changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm, fromDate, toDate, itemsPerPage]);

  // Fetch data when filters, page, or items per page change
  useEffect(() => {
    fetchData();
  }, [
    fetchData,
    currentPage,
    itemsPerPage,
    debouncedSearchTerm,
    fromDate,
    toDate,
    refreshTrigger,
    refresh,
  ]);

  const refreshData = () => {
    setRefreshTrigger((prev) => prev + 1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value);
    // No need to reset page here as it's handled in the useEffect above
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
  };

  const resetDateFilters = () => {
    setFromDate(undefined);
    setToDate(undefined);
  };

  const renderSkeletonRows = () => {
    // Show at least 3 skeletons, or itemsPerPage if less than 10
    const skeletonCount = Math.min(itemsPerPage, 10);
    return Array(skeletonCount)
      .fill(0)
      .map((_, index) => <TableSkeletonRow key={index} />);
  };

  return (
    <>
      <div className="flex flex-col sm:flex-row gap-4 justify-between pt-2">
        <div className="relative w-full sm:w-[300px]">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search client..."
            value={searchInput}
            onChange={handleSearchChange}
            className="pl-8"
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-2 justify-between">
          <div className="flex items-center gap-2">
            <Select
              value={itemsPerPage.toString()}
              onValueChange={handleItemsPerPageChange}
            >
              <SelectTrigger className="w-[80px]">
                <SelectValue placeholder="50" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
                <SelectItem value="250">250</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full sm:w-[200px] justify-start text-left font-normal",
                    !fromDate && "text-muted-foreground"
                  )}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {fromDate ? format(fromDate, "PPP") : "From date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  mode="single"
                  selected={fromDate}
                  onSelect={setFromDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full sm:w-[200px] justify-start text-left font-normal",
                    !toDate && "text-muted-foreground"
                  )}
                >
                  <Calendar className="mr-2 h-4 w-4" />
                  {toDate ? format(toDate, "PPP") : "To date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <CalendarComponent
                  mode="single"
                  selected={toDate}
                  onSelect={setToDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            {(fromDate || toDate) && (
              <Button
                variant="ghost"
                onClick={resetDateFilters}
                className="h-10"
              >
                Clear dates
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="w-full grid grid-cols-3 gap-4 font-semibold text-sm mb-2 py-4 border-b mt-4">
        <span className="text-left align-middle py-2 text-sm font-medium text-muted-foreground">
          Date
        </span>
        <span className="text-left align-middle py-2 text-sm font-medium text-muted-foreground">
          Client Name
        </span>
        <span className="text-right align-middle py-2 text-sm font-medium text-muted-foreground">
          Actions
        </span>
      </div>

      {isLoading ? (
        <div className="text-center py-8"> {renderSkeletonRows()}</div>
      ) : error ? (
        <div className="text-red-500 text-center py-4">
          Error loading data: {error}
        </div>
      ) : data?.data?.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No records found.
        </div>
      ) : (
        <Accordion type="single" collapsible className="w-full space-y-2">
          {data?.data?.map((item: PlanningData) => (
            <AccordionItem key={item.id} value={item.id.toString()}>
              {/* <AccordionTrigger className="hover:no-underline"> */}
              <AccordionTrigger className="hover:no-underline  bg-white z-10 ">
                <div className="flex flex-col w-full">
                  <div className="grid grid-cols-3 gap-4 w-full items-center">
                    <span>{formatDate(item.daily_planning_date)}</span>
                    <span className="uppercase">{item.client.client_name}</span>
                    <div className="flex justify-end space-x-2">
                      <PermissionWrapper
                        permissions={permissions}
                        requiredPermissions={["update-dailyPlanning"]}
                      >
                        <UpdateDailyPlanning
                          data={item}
                          allClient={allClient}
                          onSuccess={refreshData}
                        />
                      </PermissionWrapper>
                      <PermissionWrapper
                        permissions={permissions}
                        requiredPermissions={["delete-dailyPlanning"]}
                      >
                        <DeleteRow
                          route={`${daily_planning.DELETE_DAILY_PLANNING}/${item.id}`}
                          onSuccess={refreshData}
                        />
                      </PermissionWrapper>
                      <PermissionWrapper
                        permissions={permissions}
                        requiredPermissions={["upload-dailyPlanningDetails"]}
                      >
                        <Link
                          href={`/user/daily_planning/import/${item?.id}`}
                          prefetch={false}
                          className="hover:bg-gray-100 transition-colors flex justify-center items-center"
                        >
                          <LuUpload className="text-gray-500 w-5 h-5" />
                        </Link>
                      </PermissionWrapper>

                      <PermissionWrapper
                        permissions={permissions}
                        requiredPermissions={["add-dailyPlanningDetails"]}
                      >
                        <Link
                          href={`/user/daily_planning/add_dailyplanning_details/${item?.id}`}
                          prefetch={false}
                          className="hover:bg-gray-100  transition-colors flex justify-center items-center"
                        >
                          <FaPlus className="text-gray-500 w-4 h-4" />
                        </Link>
                      </PermissionWrapper>
                    </div>
                  </div>
                  <div className="text-gray-500 text-xs mt-1 hover:cursor-pointer">
                    Created By: {item.user?.username ?? "Unknown User"}
                  </div>
                </div>
              </AccordionTrigger>

              <AccordionContent>
                <div className="">
                  <AllTables
                    id={item.id}
                    permissions={permissions}
                    userData={userData}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      )}

      {/* Pagination */}
      {isLoading ? (
        <div className="flex justify-center mt-4">
          <Skeleton className="h-10 w-64" />
        </div>
      ) : (
        data?.pagination?.totalPages > 1 && (
          <Pagination
            currentPage={data.pagination.currentPage}
            totalPages={data.pagination.totalPages}
            onPageChange={setCurrentPage}
          />
        )
      )}
    </>
  );
};

export default CustomTable;
