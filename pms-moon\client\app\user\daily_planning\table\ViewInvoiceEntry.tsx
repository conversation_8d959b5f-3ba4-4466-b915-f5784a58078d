"use client";

import React, { useState, useMemo } from "react";
import { CSVLink } from "react-csv";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Download } from "lucide-react";
import DeleteRow from "@/app/_component/DeleteRow";
import { daily_planning_details_routes } from "@/lib/routePath";
import TriggerButton from "@/app/_component/TriggerButton";
import UpdateDailyPlanningDetails from "../UpdateDailyplanningDetails";
import { PermissionWrapper } from "@/lib/permissionWrapper";

const ViewInvoiceEntry = ({
  dailyPlanningDetailsInvoice,
  permissions,
}: any) => {
  const [searchTerm, setSearchTerm] = useState("");
  const aggregatedData = useMemo(() => {
    const acc: any = {};

    // Ensure data exists and is an array

    dailyPlanningDetailsInvoice?.forEach((item: any) => {
      // Ensure DailyPlanningDetails exists and is an array
      const clientId = item.daily_planning?.client_id;
      item?.DailyPlanningDetails.forEach((detail: any) => {
        // Use carrier name and ID as key to ensure unique entries
        if (detail.carrier) {
          const carrierKey = `${detail.carrier.name}_${detail.carrier_id}`;

          if (!acc[carrierKey]) {
            acc[carrierKey] = {
              clientId: clientId,
              carrierId: detail?.carrier?.id,
              daily_planning_id: detail.daily_planning_id,
              daily_planning_details_id: detail.id,
              name: detail.carrier.name,
              old: 0,
              new: 0,
              ute: 0,
              invoice_entry_total: 0,
            };
          }

          // Safely add values, defaulting to 0 if undefined
          acc[carrierKey].old += detail.old ?? 0;
          acc[carrierKey].new += detail.new ?? 0;
          acc[carrierKey].ute += detail.ute ?? 0;
          acc[carrierKey].invoice_entry_total +=
            detail.invoice_entry_total ?? 0;
        }
      });
    });

    return acc;
  }, [dailyPlanningDetailsInvoice]);

  const filteredCarriers = useMemo(() => {
    return (
      aggregatedData &&
      Object.values(aggregatedData).filter((carrier: any) =>
        carrier.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [aggregatedData, searchTerm]);

  const totals: any = useMemo(() => {
    return filteredCarriers?.reduce(
      (totals: any, carrier: any) => {
        totals.totalOld += carrier.old;
        totals.totalNew += carrier.new;
        totals.totalUte += carrier.ute;
        totals.invoice_entry_total += carrier.invoice_entry_total;

        return totals;
      },
      {
        totalOld: 0,
        totalNew: 0,
        totalUte: 0,
        invoice_entry_total: 0,
      }
    );
  }, [filteredCarriers]);

  const csvData = useMemo(() => {
    const headers = ["Carrier", "Old", "New", "UTE", "Total"];

    const rows = filteredCarriers?.map((carrier: any) => [
      carrier.name,
      carrier.old,
      carrier.new,
      carrier.ute,
      carrier.invoice_entry_total,
    ]);

    const totalRow = totals && [
      "Total",
      totals.totalOld,
      totals.totalNew,
      totals.totalUte,
      totals.invoice_entry_total,
    ];

    return [headers, ...rows, totalRow];
  }, [filteredCarriers, totals]);

  return (
    <div className="">
      {/* <div className="flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search carriers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div> */}
      <div className="rounded-md ">
        <Table>
          <TableHeader>
            <TableRow className="text-sm">
              <TableHead className="w-[200px] text-black">Carrier</TableHead>
              <TableHead className="text-right text-black">Old</TableHead>
              <TableHead className="text-right text-black">New</TableHead>
              <TableHead className="text-right text-black">Total</TableHead>
              <TableHead className="text-right text-black">UTE</TableHead>
              <TableHead className="text-right text-black">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCarriers.map((carrier: any, index: number) => {
              return (
                <TableRow key={index}>
                  <TableCell className="font-medium">{carrier.name}</TableCell>
                  <TableCell className="text-right">{carrier.old}</TableCell>
                  <TableCell className="text-right">{carrier.new}</TableCell>
                  <TableCell className="text-right">
                    {carrier.invoice_entry_total}
                  </TableCell>
                  <TableCell className="text-right">{carrier.ute}</TableCell>

                  <TableCell className="text-right  ">
                    <PermissionWrapper
                      permissions={permissions}
                      requiredPermissions={["update-dailyplanningdetails"]}
                    >
                      <UpdateDailyPlanningDetails
                        data={carrier}
                        type="INVOICE_ENTRY_STATUS"
                      />
                    </PermissionWrapper>
                    <PermissionWrapper
                      permissions={permissions}
                      requiredPermissions={["delete-dailyplanningdetails"]}
                    >
                      <DeleteRow
                        route={`${daily_planning_details_routes.DELETE_DAILY_PLANNING_DETAILS}/${carrier.daily_planning_details_id}`}
                      />
                    </PermissionWrapper>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
          <TableFooter className="text-muted-foreground">
            <TableRow>
              <TableCell className="font-bold">Total</TableCell>
              <TableCell className="text-right">{totals.totalOld}</TableCell>
              <TableCell className="text-right ">{totals.totalNew}</TableCell>
              <TableCell className="text-right ">
                {totals.invoice_entry_total}
              </TableCell>
              <TableCell className="text-right ">{totals.totalUte}</TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </div>
    </div>
  );
};

export default ViewInvoiceEntry;
