"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.excelUsers = exports.uploadProfileImage = exports.createUser = exports.createCorporation = void 0;
const operation_1 = require("../../utils/operation");
const xlsx_1 = __importDefault(require("xlsx"));
const bcrypt_1 = __importDefault(require("bcrypt"));
const fs_1 = __importDefault(require("fs"));
const createCorporation = async (req, res) => {
    const fields = {
        username: req.body.username,
        email: req.body.email,
        password: req.body.password,
        country: req.body.country,
        state: req.body.state,
        city: req.body.city,
        address: req.body.address,
    };
    await (0, operation_1.createItem)({
        model: "corporation",
        fieldName: "corporation_id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "Corporation has been created",
    });
};
exports.createCorporation = createCorporation;
const createUser = async (req, res) => {
    const { corporation_id } = req;
    const { username, firstName, lastName, email, password, role_id, level, parent_id, clients, } = req.body;
    const hashedPassword = await bcrypt_1.default.hash(password, 10);
    const user = await prisma.user.create({
        data: {
            username,
            firstName,
            lastName,
            email: email.toLowerCase(),
            password: hashedPassword,
            role_id: Number(role_id),
            level: Number(level),
            parent_id: Number(parent_id),
            corporation_id: Number(corporation_id),
            branch_id: Number(req.body.branch),
            date_of_joining: new Date(req.body.date_of_joining),
            userClients: {
                create: clients.map((clientId) => ({
                    clientId,
                })),
            },
        },
    });
    res
        .status(200)
        .json({ success: true, message: "User has been created", user });
};
exports.createUser = createUser;
const uploadProfileImage = async (req, res) => {
    if (!req.file) {
        return res
            .status(400)
            .json({ success: false, message: "No file uploaded." });
    }
    const { corporation_id, id } = req;
    const profileImage = req.file;
    const fields = {
        corporation_id: Number(corporation_id),
        // size:profileImage.size,
        name: profileImage.name,
        path: profileImage.path,
        type: profileImage.type,
        id: Number(id),
    };
    await (0, operation_1.createItem)({
        model: "imageFile",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "Profile Image Uploaded",
    });
};
exports.uploadProfileImage = uploadProfileImage;
const excelUsers = async (req, res) => {
    try {
        // Check if the file is provided
        if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" });
        }
        // Validate the file extension
        const allowedExtensions = [".xlsx", ".xls"];
        const fileExtension = req.file.originalname.split(".").pop();
        if (!allowedExtensions.includes(`.${fileExtension}`)) {
            return res
                .status(400)
                .json({ error: "Invalid file type. Only Excel files are allowed." });
        }
        // Read the Excel file
        const workbook = xlsx_1.default.readFile(req.file.path);
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        // Parse the Excel file into JSON
        const import_data = xlsx_1.default.utils.sheet_to_json(worksheet, { header: 1 });
        // Define the expected header for the Excel file
        const header = [
            "Role",
            "FirstName",
            "LastName",
            "Email",
            "Username",
            "Password",
        ];
        let errors = [];
        // Validate the headers
        if (!import_data[0].includes(header[0]) ||
            !import_data[0].includes(header[1]) ||
            !import_data[0].includes(header[2]) ||
            !import_data[0].includes(header[3]) ||
            !import_data[0].includes(header[4])) {
            errors.push("Invalid file format. Please ensure the file has the correct headers.");
            return res.status(200).json({
                message: "Invalid file format. Please ensure the file has the correct headers.",
                errors,
            });
        }
        // Process each row and check for existing users
        const userData = await Promise.all(import_data.slice(1).map(async (row, index) => {
            const role = await prisma.roles.findFirst({
                where: {
                    name: row[0],
                },
                select: { id: true },
            });
            const corporation = await prisma.corporation.findFirst({
                where: {
                    corporation_id: row[6], // Make sure the column index is correct
                },
                select: { corporation_id: true },
            });
            const firstName = row[1];
            const lastName = row[2];
            const email = row[3];
            const username = row[4];
            const password = row[5];
            // Check if the user already exists based on email or username
            const existingUser = await prisma.user.findFirst({
                where: {
                    OR: [{ email: email }, { username: username }],
                },
            });
            if (existingUser) {
                if (existingUser.email === email &&
                    existingUser.username === username) {
                    return {
                        error: `Email ${email} and Username ${username} already exists.`,
                    };
                }
                else if (existingUser.email === email) {
                    return { error: `Email ${email} already exists.` };
                }
                else if (existingUser.username === username) {
                    return { error: `Username ${username} already exists.` };
                }
            }
            // Hash the password before saving
            const passwordHash = await bcrypt_1.default.hash(password, 10);
            // Return the user data to be inserted into the database
            return {
                corporation_id: Number(corporation.corporation_id),
                role_id: role.id,
                firstName: firstName,
                lastName: lastName,
                email: email,
                username: username,
                password: passwordHash,
            };
        }));
        // Filter out any rows that have errors (duplicates)
        const validUsers = userData.filter((user) => !user.error);
        const errorMessages = userData
            .filter((user) => user.error)
            .map((user) => user.error);
        // If no valid users, return an error message
        // if (validUsers.length === 0) {
        //   errors.push('No valid user data found. Ensure all required fields are correctly filled.');
        //   return res.status(200).json({ message: 'No new valid users to import.', errors });
        // }
        // Insert the valid users into the database
        await prisma.user.createMany({
            data: validUsers,
        });
        // Return success message for newly created users and errors for existing data
        return res.status(200).json({
            message: "User data imported successfully",
            errors: errorMessages, // Include error messages for existing data
            successCount: validUsers.length, // You can also return how many users were successfully added
        });
    }
    catch (error) {
        console.error(error);
        res.status(500).json({ error: "Internal Server Error" });
    }
    finally {
        fs_1.default.unlinkSync(req.file.path);
    }
};
exports.excelUsers = excelUsers;
//# sourceMappingURL=create.js.map