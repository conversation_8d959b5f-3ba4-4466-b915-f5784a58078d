"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit, getAllData } from "@/lib/helpers";
import { client_routes, daily_planning } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { AddDailyPlanningSchema } from "@/lib/zodSchema";
import { Client } from "@/app/pms/manage_client/column";
import { toast } from "sonner";
import { useSession } from "@/lib/useSession";
import { Dialog, DialogContent } from "@/components/ui/dialog";

export const AddDailyPlanning = ({ allClient, setRefresh, userData, permissions }: any) => {
  const router = useRouter();
  const [isLogoutConfirmationOpen, setIsLogoutConfirmationOpen] =
    useState(false);
  const { checkSessionToken, isSessionValid } = useSession(userData);
  const refreshData = () => {
    setRefresh((prev) => prev + 1);
  };
  allClient.sort((a, b) => a.client_name.localeCompare(b.client_name));
  const todayDate = new Date().toISOString().split("T")[0];
  const hasBackdatePermission = permissions?.includes("create-backdate-dailyplanning");
  const { form } = useDynamicForm(AddDailyPlanningSchema, {
    daily_planning_date: todayDate,
    client_id: "",
  });

  const onSubmit = async (values: any) => {
    const sessionValid = await checkSessionToken();

    if (!sessionValid || !isSessionValid) {
      setIsLogoutConfirmationOpen(true);
      return;
    }
    try {
      const submittedValues = {
        ...values,
        daily_planning_date: new Date(values.daily_planning_date),
        client_id: parseInt(values.client_id),
      };

      const data = await formSubmit(
        daily_planning.CREATE_DAILY_PLANNING,
        "POST",
        submittedValues
      );

      if (data.success) {
        toast.success(data.message);
        form.reset();
        router.refresh();
        refreshData();
      } else {
        toast.error(
          data.message || "An error occurred while adding the carrier."
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };

  return (
    <>
      {isLogoutConfirmationOpen && (
        <Dialog open={isLogoutConfirmationOpen}>
          <DialogContent>
            <div className="text-center">
              <p className="mb-4">
                You are already logged in on another device.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
      <div className="w-full">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-2  md:grid md:grid-cols-3 items-center gap-3 ">
              <FormInput
                label="Date"
                form={form}
                name="daily_planning_date"
                type="date"
                className="mt-3"
                min={!hasBackdatePermission ? todayDate : undefined}
                 isRequired
              />
              <SelectComp
                form={form}
                label="Client"
                name="client_id"
                placeholder="Select client"
                isRequired
                className="  rounded-md mt-2"
              >
                {allClient &&
                  allClient.map((clien: any) => (
                    <SelectItem
                      value={clien?.id?.toString()}
                      key={clien?.id}
                    >
                      {clien.client_name}
                    </SelectItem>
                  ))}
              </SelectComp>

              <SubmitBtn
                className=" md:mt-3 w-full bg-primary/80 hover:bg-primary/90 text-white font-medium rounded-md transition-colors"
                text="Submit"
              />
            </div>
          </form>
        </Form>
      </div>
    </>
  );
};
