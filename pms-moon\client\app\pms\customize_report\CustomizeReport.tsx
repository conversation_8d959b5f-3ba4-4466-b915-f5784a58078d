"use client"
import { PermissionWrapper } from "@/lib/permissionWrapper";
import Report from "./Report";


const CustomizeReport = ({ categories, users,permissions,clients }) => {


  return (
    <div>
      <PermissionWrapper 
      permissions={permissions}
      requiredPermissions={["view-report"]}
      >
      <Report categories={categories} users={users} permissions={permissions} clients={clients} />
      </PermissionWrapper>
    </div>
  );
};

export default CustomizeReport;