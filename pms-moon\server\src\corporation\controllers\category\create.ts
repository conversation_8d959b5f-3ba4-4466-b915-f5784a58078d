import { createItem } from "../../../utils/operation";

export const createCategory = async (req, res) => {
  const { corporation_id } = req;
  const fields = {
    category_name: req.body.name,
    corporation_id: Number(corporation_id),
  };
  await createItem({
    model: "category",
    fieldName: "id",
    fields: fields,
    res: res as Response,
    req: req,
    successMessage: "category has been created",
  });
};
