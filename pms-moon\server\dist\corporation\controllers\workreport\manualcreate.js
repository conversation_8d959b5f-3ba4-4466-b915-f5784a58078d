"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWorkreportManually = void 0;
const operation_1 = require("../../../utils/operation");
const helper_1 = require("./helper");
const createWorkreportManually = async (req, res) => {
    const userId = req.user_id;
    const { date, client_id, carrier_id, work_type_id, category, actualNumber, start_time, end_time, task_type, notes, } = req.body;
    // Calculate duration in minutes
    const startTime = new Date(start_time);
    const endTime = new Date(end_time);
    const duration = (endTime - startTime) / (1000 * 60); // Convert ms to minutes
    const fields = {
        date: new Date(date),
        user_id: userId,
        work_status: helper_1.WorkStatus.FINISHED,
        start_time: startTime,
        finish_time: endTime,
        time_spent: duration,
        notes: notes,
    };
    await (0, operation_1.createItem)({
        model: "workReport",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "workReport has been created",
    });
};
exports.createWorkreportManually = createWorkreportManually;
//# sourceMappingURL=manualcreate.js.map