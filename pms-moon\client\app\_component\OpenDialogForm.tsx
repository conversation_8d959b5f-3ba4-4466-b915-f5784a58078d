import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTrigger,
} from "@/components/ui/dialog";
import DialogHeading from "./DialogHeading";
import CloseDialog from "./CloseDialog";
import { cn } from "@/lib/utils";
import TriggerButton from "./TriggerButton";

interface OpenDialogFormProps {
  children: React.ReactNode;
  triggerButton?: React.ReactNode;
  heading: string;
  description: string;
  contentClassName?: string;
  type?: "add" | "edit";
  text?: string;
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const OpenDialogForm = ({
  children,
  triggerButton,
  heading,
  description,
  contentClassName,
  type,
  text,
  open,
  setOpen,
}: OpenDialogFormProps) => {
  return (
    <>
      <Dialog
        open={open}
        onOpenChange={() => {
          setOpen(true);
        }}
      >
        <DialogTrigger>
          {triggerButton ? (
            triggerButton
          ) : (
            <Trigger<PERSON>utton type={type} text={text} />
          )}
        </DialogTrigger>
        <DialogContent
          className={cn("max-w-5xl  dark:bg-gray-800", contentClassName)}
          //   onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <CloseDialog setOpen={setOpen} />
          <DialogHeader>
            <DialogHeading title={heading} description={description} />
          </DialogHeader>
          {children}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default OpenDialogForm;
