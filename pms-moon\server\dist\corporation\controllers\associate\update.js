"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateAssociate = void 0;
const operation_1 = require("../../../utils/operation");
const updateAssociate = async (req, res) => {
    const id = req.params.id;
    const { corporation_id } = req;
    const fields = {
        name: req.body.name,
        corporation_id: Number(corporation_id),
    };
    await (0, operation_1.updateItem)({
        model: "associate",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res: res,
        req: req,
        successMessage: "Associate has been updated",
    });
};
exports.updateAssociate = updateAssociate;
//# sourceMappingURL=update.js.map