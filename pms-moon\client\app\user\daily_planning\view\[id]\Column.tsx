import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";

import DeleteRow from "@/app/_component/DeleteRow";
import { carrier_routes } from "@/lib/routePath";



export const column: ColumnDef<any>[] = [
  {
    accessorKey: "carrier_name",
    header: "Carrier",
    cell: ({ row }) => row?.original.carrier.name,
  },
  {
    accessorKey: "pf_status",
    header: "PF Status",
    cell: ({ row }) => row?.original.pf_status || 0,
  },
  {
    accessorKey: "batch_error",
    header: "Batch Error",
    cell: ({ row }) => row?.original.batch_error || 0,
  },
  {
    accessorKey: "hold",
    header: "HOLD",
    cell: ({ row }) => row?.original.hold || 0,
  },
  {
    accessorKey: "review_status",
    header: "In Review",
    cell: ({ row }) => row?.original.review_status || 0,

  },

  {
    accessorKey: "action",
    header: "Action",
    id: "action",
    cell: ({ row }) => {
      const carrier = row?.original;

      return (
        <div className="flex items-center">
          <DeleteRow
            route={`${carrier_routes.DELETE_CARRIER}/${carrier?.id}`}
          />
        </div>
      );
    },
  },
];
