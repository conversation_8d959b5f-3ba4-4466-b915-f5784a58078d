import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import React from "react";

type FormTextareaProps = {
  form: any;
  name: string;
  label: string;
  placeholder?: string;
  className?: string;
  isRequired?: boolean;
  rows?: number;
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;
};

const FormTextarea = ({
  form,
  name,
  label,
  placeholder,
  className,
  isRequired,
  rows = 4,
  onBlur,
}: FormTextareaProps) => {
  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <FormItem className={cn`space-y-0.5`}>
          <FormLabel className="text-gray-800 dark:text-gray-300">
            {label}
            {isRequired && <span className="text-red-500">*</span>}
          </FormLabel>
          <FormControl>
            <Textarea
              {...field}
              id={name}
              name={name}
              placeholder={placeholder}
              rows={rows}
              className={`bg-gray-200 dark:bg-gray-700 border-none dark:border-gray-700 placeholder:text-gray-400 dark:placeholder:text-gray-100/50 outline-none focus:!outline-main-color resize-none rounded-md p-2 w-full ${className}`}
              onBlur={onBlur}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default FormTextarea;
