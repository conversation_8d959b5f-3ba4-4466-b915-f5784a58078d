import React, { createContext, Dispatch, SetStateAction } from "react";

interface WorkReportType {
  filterdata: any;
  fromDate: any;
  toDate: any;
  deleteData: boolean;
  setFilterData: Dispatch<SetStateAction<any>>;
  setFromDate: Dispatch<SetStateAction<any>>;
  setToDate: Dispatch<SetStateAction<any>>;
  setDeletedData: Dispatch<SetStateAction<any>>;
}

export const WorkReportContext = createContext<WorkReportType>({
  fromDate: "",
  toDate: "",
  filterdata: [],
  deleteData: false,
  setFilterData: () => {},
  setFromDate: () => {},
  setToDate: () => {},
  setDeletedData: () => {},
});
