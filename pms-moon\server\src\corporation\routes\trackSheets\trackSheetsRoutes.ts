import { Router } from "express";
import { authenticate } from "../../../middleware/authentication";
import { createTrackSheets } from "../../controllers/trackSheets/create";
import { viewTrackSheets, ViewTrackSheetsById } from "../../controllers/trackSheets/view";
import { updateTrackSheets } from "../../controllers/trackSheets/update";
import { deleteTrackSheets } from "../../controllers/trackSheets/delete";

const router = Router();

router.get(
    "/clients/:id",
    //  authenticate,
    viewTrackSheets
);

router.get(
    "/:id",
    // authenticate,
    ViewTrackSheetsById
);

router.post(
    "/",
    // authenticate,
    createTrackSheets
);


router.put(
    "/:id",
    // authenticate,
    updateTrackSheets
);

router.delete(
    "/:id",
    authenticate,
    deleteTrackSheets
);

export default router;
