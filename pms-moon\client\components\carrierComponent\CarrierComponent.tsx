"use client";

import { useState, useMemo, useEffect } from "react";
import { ChevronLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import { Input } from "../ui/input";
import useDebounce from "@/hooks/useDebounce";
import { Button } from "../ui/button";
import { getAllData } from "@/lib/helpers";
import { carrier_routes } from "@/lib/routePath";

export default function CarrierComponent({
  setSelectedCarrier,
  seletedWorkType,
  selectedCarrier,
  carrierByClient
}: any) {
  const [selectedTab, setSelectedTab] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLetter, setSelectedLetter] = useState<string | null>(null);
  const [carrier, setCarrier] = useState<any[]>([]);
  const debouncedSearchTerm = useDebounce(searchTerm, 300);
  const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".toLowerCase().split("");

  async function data() {
    const pageSize = 50;
  const page = 1;
    try {
      // const API_URL = `${carrier_routes.GETALL_CARRIER}?pageSize=${pageSize}&page=${page}`;
      // const allCarrier = await getAllData(API_URL);
      // setCarrier(allCarrier);
      // setCarrier(tabItems);
    } catch (error) {
      console.error("Error fetching carriers:", error);
    }
  }

  useEffect(() => {
    data();
  }, []);

  const filteredItems = useMemo(() => {
    return (
      carrierByClient &&
      carrierByClient.filter((item) => {
        const nameMatch = selectedLetter
          ? item.carrier.name.toLowerCase().startsWith(selectedLetter)
          : true;

        const searchMatch =
          item &&
          item.carrier.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase());

        return nameMatch && searchMatch;
      })
    );
  }, [debouncedSearchTerm, selectedLetter, carrierByClient]);

  return (
    <>
      {/* {seletedWorkType?.is_work_carrier_specific && ( */}
      <div className=" h-[100vh] mb-6 bg-primary/10 p-2   mr-1 rounded-lg space-y-2">
        {/* <div className="flex flex-wrap gap-1 mb-4 p-1">
            {alphabet.map((letter) => (
              <Button
                key={letter}
                variant={
                  selectedLetter === letter ? "secondary" : "customButton"
                }
                className="w-7 h-7 p-0"
                onClick={() =>
                  setSelectedLetter(selectedLetter === letter ? null : letter)
                }
              >
                {letter}
              </Button>
            ))}
          </div> */}
        <div className="text-2xl pt-2 font-bold text-right mr-2  text-primary">
          Carrier
        </div>

        <div className="p-1 rounded-none ">
          <Input
            placeholder="Search Carrier"
            className="w-full shadow-md bg-white"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        {/* <div className="flex justify-end p-1">
            <Button
              onClick={() => {
                setSelectedTab("");
                setSelectedCarrier("");
                setSelectedLetter("");
                setSearchTerm("");
              }}
            >
              RESET
            </Button>
          </div> */}

        <nav className="flex flex-col items-stretch space-y-1">
          {filteredItems && filteredItems.length > 0 ? (
            filteredItems.slice(0, 10).map((item, id) => (
              <div
                key={id}
                onClick={() => {
                  if (selectedCarrier === item.carrier.id) {
                    setSelectedTab("");
                    setSelectedCarrier("");
                  } else {
                    setSelectedTab(item.carrier.name);
                    setSelectedCarrier(item.carrier.id);
                  }
                }}
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    if (selectedCarrier === item.carrier.id) {
                      setSelectedTab("");
                      setSelectedCarrier("");
                    } else {
                      setSelectedTab(item.carrier.name);
                      setSelectedCarrier(item.carrier.id);
                    }
                  }
                }}
                className={cn(
                  "flex items-center rounded-md m-2 px-4 py-2 space-y-2 text-sm font-medium text-left cursor-pointer",
                  "transition-colors duration-150 ease-in-out",
                  "hover:bg-primary/10",
                  selectedTab === item.carrier.name
                    ? "bg-primary text-primary-foreground hover:bg-primary/90"
                    : "text-gray-700"
                )}
              >
                <ChevronLeft
                  className={cn(
                    "h-4 w-4 mr-2",
                    selectedTab === item.carrier.name
                      ? "text-primary-foreground"
                      : "text-gray-400"
                  )}
                />
                <span className="uppercase text-xs">{item.carrier.name}</span>
              </div>
            ))
          ) : (
            <div className="text-center text-gray-500">
              No carriers available
            </div>
          )}
        </nav>

        <div className="p-6">
          <h2 className="text-2xl font-bold mb-4">
            {carrierByClient &&
              carrierByClient.find((item) => item.carrier.name === selectedTab)?.label}
          </h2>
        </div>
      </div>
      {/* )} */}
    </>
  );
}
