"use client";

import * as React from "react";
import { Check } from "lucide-react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";
import { carrier_routes } from "@/lib/routePath";
import { getAllData } from "@/lib/helpers";
import { useRouter } from "next/navigation"; // ✅ Import router

interface ItemType {
  id: number;
  name: string;
}

interface ComboboxDemoProps {
  form: any;
  name: string;
  label?: string;
  placeholder?: string;
  className?: string;
  isRequired?: boolean;
}

export function ComboboxDemo({
  form,
  label,
  name,
  placeholder,
  className,
  isRequired,
}: ComboboxDemoProps) {
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");
  const [items, setItems] = React.useState<ItemType[]>([]);
  const [highlightedIndex, setHighlightedIndex] = React.useState(0);
  const debounceTimeout = React.useRef<NodeJS.Timeout | null>(null);
  const fieldValue = form.watch(name);
  const router = useRouter(); // ✅ Init router

  const fetchItems = async (query: string) => {
    try {
      const API_URL = `${carrier_routes.GET_CARRIER}`;
      const url = new URL(API_URL);
      url.searchParams.append("CarrierName", query);
      const datas = await getAllData(url.toString());
      setItems(datas.data);
      setHighlightedIndex(0);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setInputValue(newValue);

    if (debounceTimeout.current) clearTimeout(debounceTimeout.current);

    debounceTimeout.current = setTimeout(() => {
      const trimmed = newValue.trim();

      if (trimmed.length >= 3) {
        fetchItems(trimmed);
        setOpen(true);
      } else {
        setItems([]);
        setOpen(false);
      }
    }, 300);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    const maxIndex = items.length - 1;

    if (event.key === "ArrowDown") {
      event.preventDefault();
      setHighlightedIndex((prev) => (prev < maxIndex ? prev + 1 : 0));
    } else if (event.key === "ArrowUp") {
      event.preventDefault();
      setHighlightedIndex((prev) => (prev > 0 ? prev - 1 : maxIndex));
    } else if (event.key === "Enter") {
      event.preventDefault();
      const selected = items[highlightedIndex];
      if (selected) {
        form.setValue(name, selected.id.toString());
        setInputValue(selected.name);
        setOpen(false);
        router.refresh(); // ✅ Refresh on selection via keyboard
      }
    }
  };

  const handleOptionClick = (item: ItemType) => {
    form.setValue(name, item.id.toString());
    setInputValue(item.name);
    setOpen(false);
    router.refresh(); // ✅ Refresh on click selection
  };

  // Clear input when form resets
  React.useMemo(() => {
    const subscription = form.watch(() => {
      const value = form.getValues(name);
      if (!value) {
        setInputValue("");
      }
    });
    return () => subscription.unsubscribe();
  }, [form, name]);

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => (
        <div className={cn("relative ", className)}>
          <FormItem>
            <FormLabel className="text-black">
              {label}
              {isRequired && <span className="text-red-500">*</span>}
            </FormLabel>
            <FormControl>
              <input
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onClick={() => {
                  if (inputValue.trim().length >= 3) setOpen(true);
                }}
                placeholder={placeholder}
                className="w-full p-2 border bg-gray-200 rounded-md text-sm"
              />
            </FormControl>
            <FormMessage />
          </FormItem>

          {open && inputValue.trim().length >= 3 && (
            <ul className="absolute z-50 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto mt-1">
              {items.length > 0 ? (
                items.map((item, index) => {
                  const isHighlighted = index === highlightedIndex;
                  return (
                    <li
                      key={item.id}
                      ref={(el) => {
                        if (isHighlighted && el) {
                          el.scrollIntoView({
                            behavior: "smooth",
                            block: "nearest",
                          });
                        }
                      }}
                      className={cn(
                        "p-2 cursor-pointer hover:bg-gray-100",
                        isHighlighted && "bg-gray-200"
                      )}
                      onClick={() => handleOptionClick(item)}
                    >
                      <div className="flex items-center">
                        {item.name}
                        {fieldValue === item.id.toString() && (
                          <Check className="ml-auto h-4 w-4 text-blue-500" />
                        )}
                      </div>
                    </li>
                  );
                })
              ) : (
                <li className="p-2 text-sm text-gray-500">No results found</li>
              )}
            </ul>
          )}
        </div>
      )}
    />
  );
}
