"use client";

import React, { useState, useMemo, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Pagination from "@/app/_component/Pagination";
import { CSVLink } from "react-csv";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Download } from "lucide-react";

const AllView = ({ dailyPlanningDetails }: any) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  console.log(dailyPlanningDetails.length);

  // const aggregatedData = useMemo(() => {
  //   return dailyPlanningDetails?.reduce((acc: any, detail: any) => {

  //     const carrierKey = `${detail.carrier_id}_${detail.currency}_${detail.receive_date}`;
  //   console.log(carrierKey)

  //     if (!acc[carrierKey]) {
  //       acc[carrierKey] = {
  //         name: detail.carrier?.name || "",
  //         code: detail.carrier?.code || "-",
  //         totalPfStatus: 0,
  //         totalTwoTenError: 0,
  //         totalBatchError: 0,
  //         totalHold: 0,
  //         totalMatchFail: 0,
  //         totalSuccess: 0,
  //         two_ten_hold: 0,
  //         two_ten_manual_match: 0,
  //         two_ten_import_additional: 0,
  //         totalEntry: 0,
  //         totalCorrect: 0,
  //         old: 0,
  //         new: 0,
  //         ute: 0,
  //         receive_date: detail.receive_date || "-",
  //         no_invoices: 0,
  //         amount_of_invoice: 0,
  //         currency: detail.currency || "-",
  //         review_status: 0,
  //       };
  //     }

  //     acc[carrierKey].totalPfStatus += detail.pf_status || 0;
  //     acc[carrierKey].totalTwoTenError += detail.two_ten_error || 0;
  //     acc[carrierKey].totalBatchError += detail.batch_error || 0;
  //     acc[carrierKey].totalHold += detail.hold || 0;
  //     acc[carrierKey].totalMatchFail += detail.two_ten_m_f || 0;
  //     acc[carrierKey].totalSuccess += detail.two_ten_success || 0;
  //     acc[carrierKey].two_ten_hold += detail.two_ten_hold || 0;
  //     acc[carrierKey].two_ten_manual_match += detail.two_ten_manual_match || 0;
  //     acc[carrierKey].two_ten_import_additional +=
  //       detail.two_ten_import_additional || 0;
  //     acc[carrierKey].totalEntry += detail.entry || 0;
  //     acc[carrierKey].totalCorrect += detail.correct || 0;
  //     acc[carrierKey].old += detail.old || 0;
  //     acc[carrierKey].new += detail.new || 0;
  //     acc[carrierKey].ute += detail.ute || 0;
  //     acc[carrierKey].amount_of_invoice =
  //       parseInt(detail.amount_of_invoice) || 0;
  //     acc[carrierKey].no_invoices = detail.no_invoices || 0;
  //     acc[carrierKey].review_status += detail.review_status || 0;

  //     return acc;
  //   }, {});
  // }, [dailyPlanningDetails]);
  const aggregatedData = useMemo(() => {
    const today = new Date().toISOString().split("T")[0]; // get today's date in YYYY-MM-DD format

    const filteredDetails = dailyPlanningDetails?.filter((detail: any) => {
      if (!detail.send_date) return true;
      const sendDate = new Date(detail.send_date).toISOString().split("T")[0];
      return sendDate >= today;
    });

    return dailyPlanningDetails?.reduce((acc: any, detail: any) => {
      const carrierKey = `${detail.carrier_id}_${detail.currency}_${
        detail.receive_date
      }_${detail.shipping_type || "none"}_${detail.division || "none"}`;
      if (!acc[carrierKey]) {
        acc[carrierKey] = {
          name: detail.carrier?.name || "",
          code: detail.carrier?.code || "-",
          shipping_type: detail.shipping_type || "-",
          division: detail.division || "-",
          totalPfStatus: 0,
          totalTwoTenError: 0,
          totalBatchError: 0,
          totalHold: 0,
          totalMatchFail: 0,
          totalSuccess: 0,
          two_ten_hold: 0,
          two_ten_manual_match: 0,
          two_ten_import_additional: 0,
          totalEntry: 0,
          totalCorrect: 0,
          old: 0,
          new: 0,
          ute: 0,
          receive_date: detail.receive_date || "-",
          no_invoices: 0,
          amount_of_invoice: 0,
          currency: detail.currency || "-",
          review_status: 0,
        };
      }

      acc[carrierKey].totalPfStatus += detail.pf_status || 0;
      acc[carrierKey].totalTwoTenError += detail.two_ten_error || 0;
      acc[carrierKey].totalBatchError += detail.batch_error || 0;
      acc[carrierKey].totalHold += detail.hold || 0;
      acc[carrierKey].totalMatchFail += detail.two_ten_m_f || 0;
      acc[carrierKey].totalSuccess += detail.two_ten_success || 0;
      acc[carrierKey].two_ten_hold += detail.two_ten_hold || 0;
      acc[carrierKey].two_ten_manual_match += detail.two_ten_manual_match || 0;
      acc[carrierKey].two_ten_import_additional +=
        detail.two_ten_import_additional || 0;
      acc[carrierKey].totalEntry += detail.entry || 0;
      acc[carrierKey].totalCorrect += detail.correct || 0;
      acc[carrierKey].old += detail.old || 0;
      acc[carrierKey].new += detail.new || 0;
      acc[carrierKey].ute += detail.ute || 0;
      acc[carrierKey].amount_of_invoice +=
        parseInt(detail.amount_of_invoice) || 0;
      acc[carrierKey].no_invoices += detail.no_invoices || 0;
      acc[carrierKey].review_status += detail.review_status || 0;

      return acc;
    }, {});
  }, [dailyPlanningDetails]);

  const filteredCarriers = useMemo(() => {
    return Object.values(aggregatedData).filter(
      (carrier: any) =>
        carrier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        carrier.currency?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [aggregatedData, searchTerm]);

  // Reset to first page when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Calculate pagination
  const totalPages = Math.ceil(filteredCarriers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageData = filteredCarriers.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const totals: any = useMemo(() => {
    return currentPageData.reduce(
      (totals: any, carrier: any) => {
        totals.totalOld += carrier.old;
        totals.totalNew += carrier.new;
        totals.totalUte += carrier.ute;
        totals.totalPfStatus += carrier.totalPfStatus;
        totals.totalTwoTenError += carrier.totalTwoTenError;
        totals.totalBatchError += carrier.totalBatchError;
        totals.totalHold += carrier.totalHold;
        totals.totalMatchFail += carrier.totalMatchFail;
        totals.totalSuccess += carrier.totalSuccess;
        totals.two_ten_hold += carrier.two_ten_hold;
        totals.two_ten_manual_match += carrier.two_ten_manual_match;
        totals.two_ten_import_additional += carrier.two_ten_import_additional;
        totals.totalEntry += carrier.totalEntry;
        totals.totalCorrect += carrier.totalCorrect;
        totals.no_invoices += parseInt(carrier.no_invoices);
        totals.amount_of_invoice += parseInt(carrier.amount_of_invoice);
        totals.review_status += carrier.review_status;
        return totals;
      },
      {
        totalOld: 0,
        totalNew: 0,
        totalUte: 0,
        totalPfStatus: 0,
        totalTwoTenError: 0,
        totalBatchError: 0,
        totalHold: 0,
        totalMatchFail: 0,
        totalSuccess: 0,
        two_ten_hold: 0,
        two_ten_manual_match: 0,
        two_ten_import_additional: 0,
        totalEntry: 0,
        totalCorrect: 0,
        no_invoices: 0,
        amount_of_invoice: 0,
        review_status: 0,
      }
    );
  }, [currentPageData]);

  const csvData = useMemo(() => {
    const headers = [
      "Carrier",
      "Old",
      "New",
      "UTE",
      "No. of Invoices",
      "Amount of Invoice",
      "Total PF Status",
      "Total Review Status",
      "Total 210 Error",
      "Total Batch Error",
      "Total Hold",
      "Total Match Fail",
      "Total Success",
      "Total Two Ten Hold",
      "Total Manual Match",
      "Total Import Additional",
      "Total Entry",
      "Total Correct",
      "Currency",
    ];

    const rows = filteredCarriers.map((carrier: any) => [
      carrier.name,
      carrier.old,
      carrier.new,
      carrier.ute,
      carrier.no_invoices,
      carrier.amount_of_invoice,
      carrier.totalPfStatus,
      carrier.review_status,
      carrier.totalTwoTenError,
      carrier.totalBatchError,
      carrier.totalHold,
      carrier.totalMatchFail,
      carrier.totalSuccess,
      carrier.two_ten_hold,
      carrier.two_ten_manual_match,
      carrier.two_ten_import_additional,
      carrier.totalEntry,
      carrier.totalCorrect,
      carrier.currency,
    ]);

    const totalRow = [
      "Total",
      totals.totalOld,
      totals.totalNew,
      totals.totalUte,
      totals.no_invoices,
      totals.amount_of_invoice,
      totals.totalPfStatus,
      totals.review_status,
      totals.totalTwoTenError,
      totals.totalBatchError,
      totals.totalHold,
      totals.totalMatchFail,
      totals.totalSuccess,
      totals.two_ten_hold,
      totals.two_ten_manual_match,
      totals.two_ten_import_additional,
      totals.totalEntry,
      totals.totalCorrect,
      totals.currency,
    ];

    return [headers, ...rows, totalRow];
  }, [filteredCarriers, totals]);

  return (
    <>
      <div className="">
        {/* <div className="flex justify-between items-center mb-4">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search carriers or currencies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
          <div className="text-sm text-gray-600">
            Showing {startIndex + 1} to {Math.min(endIndex, filteredCarriers.length)} of {filteredCarriers.length} entries
          </div>
        </div> */}

        <div className="rounded-md">
          <Table>
            <TableCaption>All Table Summary</TableCaption>
            <TableHeader>
              <TableRow className="font-normal text-xs">
                <TableHead className="w-[200px] text-black">Carrier</TableHead>
                <TableHead className="text-right text-black">Old</TableHead>
                <TableHead className="text-right text-black">New</TableHead>
                <TableHead className="text-right text-black">UTE</TableHead>
                <TableHead className="text-center text-black">
                  No. Of invoice of statement
                </TableHead>
                <TableHead className="text-center text-black">
                  Amt. Of Invoice of statement
                </TableHead>
                <TableHead className="text-center text-black">Review</TableHead>
                <TableHead className="text-center text-black">
                  Batch Error
                </TableHead>
                <TableHead className="text-center text-black">Hold</TableHead>
                <TableHead className="text-center text-black">Entry</TableHead>
                <TableHead className="text-center text-black">
                  Correct
                </TableHead>
                <TableHead className="text-center text-black">
                  210 Error
                </TableHead>

                <TableHead className="text-center text-black">
                  210 Match Fail
                </TableHead>
                <TableHead className="text-center text-black">
                  210 Success
                </TableHead>
                <TableHead className="text-center text-black">
                  210 Hold
                </TableHead>
                <TableHead className="text-center text-black">
                  210 Manual Match
                </TableHead>
                <TableHead className="text-center text-black">
                  210 Import Additional
                </TableHead>
                <TableHead className="text-center text-black">
                  Currency
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentPageData.map((carrier: any, index: number) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{carrier.name}</TableCell>
                  <TableCell className="text-center">{carrier.old}</TableCell>
                  <TableCell className="text-center">{carrier.new}</TableCell>
                  <TableCell className="text-center">{carrier.ute}</TableCell>
                  <TableCell className="text-center">
                    {carrier.no_invoices}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.amount_of_invoice}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.review_status}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.totalBatchError}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.totalHold}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.totalEntry}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.totalCorrect}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.totalTwoTenError}
                  </TableCell>

                  <TableCell className="text-center">
                    {carrier.totalMatchFail}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.totalSuccess}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.two_ten_hold}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.two_ten_manual_match}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.two_ten_import_additional}
                  </TableCell>
                  <TableCell className="text-center">
                    {carrier.currency}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
            <TableFooter className="text-muted-foreground">
              <TableRow>
                <TableCell className="font-bold">Total</TableCell>
                <TableCell className="text-center">{totals.totalOld}</TableCell>
                <TableCell className="text-center">{totals.totalNew}</TableCell>
                <TableCell className="text-center">{totals.totalUte}</TableCell>
                <TableCell className="text-center">
                  {totals.no_invoices}
                </TableCell>
                <TableCell className="text-center">
                  {totals.amount_of_invoice}
                </TableCell>
                <TableCell className="text-center">
                  {totals.review_status}
                </TableCell>
                <TableCell className="text-center">
                  {totals.totalBatchError}
                </TableCell>
                <TableCell className="text-center">
                  {totals.totalHold}
                </TableCell>
                <TableCell className="text-center">
                  {totals.totalEntry}
                </TableCell>
                <TableCell className="text-center">
                  {totals.totalCorrect}
                </TableCell>
                <TableCell className="text-center">
                  {totals.totalTwoTenError}
                </TableCell>

                <TableCell className="text-center">
                  {totals.totalMatchFail}
                </TableCell>
                <TableCell className="text-center">
                  {totals.totalSuccess}
                </TableCell>
                <TableCell className="text-center">
                  {totals.two_ten_hold}
                </TableCell>
                <TableCell className="text-center">
                  {totals.two_ten_manual_match}
                </TableCell>
                <TableCell className="text-center">
                  {totals.two_ten_import_additional}
                </TableCell>

                <TableCell className="text-center"></TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </div>
      </div>
      {totalPages > 1 && (
        <div className="mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </>
  );
};

export default AllView;
