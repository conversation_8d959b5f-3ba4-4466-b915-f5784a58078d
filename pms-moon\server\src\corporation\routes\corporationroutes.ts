import { Router } from "express";
import { createCorporation } from "../controllers/create";
import { corporationLogin } from "../../utils/login";
import { updateCorporation } from "../controllers/update";
import { deleteCorporation } from "../controllers/delete";
import { viewCorporation } from "../controllers/view";
import { logout } from "../controllers/logout";
import { authenticate } from "../../middleware/authentication";



const router = Router();

router.post("/create-corporation", createCorporation);

router.post("/login",corporationLogin);

router.post("/logout", logout);

router.get("/get-all-corporation", authenticate, viewCorporation);

router.put("/update-corporation/:id", authenticate, updateCorporation);

router.delete("/delete-corporation/:id",authenticate, deleteCorporation );

export default router;
