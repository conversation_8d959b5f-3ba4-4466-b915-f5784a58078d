"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFreightSetupParams = exports.getRolePermissionParams = exports.getRoleParams = exports.getAllRoleParams = exports.getAllPermissionParams = void 0;
const getAllPermissionParams = async () => {
    return {
        include: {
            AuditLog: {
                orderBy: { timestamp: "desc" },
                take: 1,
                select: {
                    tableName: true,
                    recordId: true,
                    recordType: true,
                    action: true,
                    userId: true,
                    timestamp: true,
                },
            },
        },
    };
};
exports.getAllPermissionParams = getAllPermissionParams;
const getAllRoleParams = async (corporation_id) => {
    return {
        where: { corporation_id },
        orderBy: { created_at: "asc" },
    };
};
exports.getAllRoleParams = getAllRoleParams;
const getRoleParams = async (corporation_id) => {
    return {
        select: { role_id: true, name: true, client_id: true },
        where: { corporation_id },
        orderBy: { name: "asc" },
    };
};
exports.getRoleParams = getRoleParams;
const getRolePermissionParams = async (role_id) => {
    return prisma.rolePermission.findMany({
        where: { role_id },
        orderBy: { role: { created_at: "desc" } },
        select: {
            permission: { select: { module: true, permission_id: true } },
        },
    });
};
exports.getRolePermissionParams = getRolePermissionParams;
const getFreightSetupParams = async (corporation_id) => {
    return {
        where: { corporation_id },
        orderBy: { created_at: "asc" },
        include: {
            role: {
                select: {
                    role_permission: {
                        select: { permission: { select: { module: true } } },
                    },
                },
            },
        },
    };
};
exports.getFreightSetupParams = getFreightSetupParams;
//# sourceMappingURL=find.js.map