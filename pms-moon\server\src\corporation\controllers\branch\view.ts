import { handleError } from "../../../utils/helpers";
import { getItem } from "../../../utils/operation";

export const viewBranch = async (req, res) => {
try {
    const data = await prisma.branch.findMany({
        orderBy: {
            id: 'desc',
        },
    });
    if (data) {
        return res.status(200).json(data);
    }

    return res.status(400).json([]);
} catch (error) {
    return handleError(res, error);
}
};