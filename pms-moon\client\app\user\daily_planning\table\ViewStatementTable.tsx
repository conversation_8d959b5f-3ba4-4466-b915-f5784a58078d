"use client";

import { useState, useMemo, useEffect } from "react";
import { CSVLink } from "react-csv";
import {
  Table,
  TableBody,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, FileDown } from "lucide-react";
import { formatDate } from "@/lib/swrFetching";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import UpdateDailyPlanningDetails from "../UpdateDailyplanningDetails";
import DeleteRow from "@/app/_component/DeleteRow";
import { daily_planning_details_routes } from "@/lib/routePath";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import { formSubmit } from "@/lib/helpers";

const ViewStatementTable = ({
  dailyPlanningDetailsStatement,
  permissions,
  userData,
  dailyPlanningDetails,
}: any) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showModal, setShowModal] = useState(false);
  const [selectedCarrier, setSelectedCarrier] = useState<any>(null);
  const [reconcileDate, setReconcileDate] = useState("");
  const [carriers, setCarriers] = useState<any[]>([]);

  // Get the daily planning date from the first item in dailyPlanningDetails
  const dailyPlanningDate = dailyPlanningDetails?.[0]?.daily_planning?.daily_planning_date;
  const formattedMaxDate = dailyPlanningDate ? new Date(dailyPlanningDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0];

  // Get min date based on the type of date being updated
  const getMinDate = (carrier: any, updateType: string) => {
    if (updateType === "reconcile") {
      return carrier.receive_date ? new Date(carrier.receive_date).toISOString().split('T')[0] : undefined;
    } else if (updateType === "review") {
      return carrier.reconcile_date ? new Date(carrier.reconcile_date).toISOString().split('T')[0] : undefined;
    } else if (updateType === "send") {
      return carrier.review_date ? new Date(carrier.review_date).toISOString().split('T')[0] : undefined;
    }
    return undefined;
  };

  const aggregatedData = useMemo(() => {
    const acc: any = {};

    dailyPlanningDetailsStatement?.forEach((item: any) => {
      item.DailyPlanningDetails &&
        item.DailyPlanningDetails?.forEach((detail: any) => {
          const clientId = item.daily_planning?.client_id;
          if (detail.carrier) {
            const carrierKey =
              detail.carrier &&
              `${detail.carrier_id}_${detail.currency}_${detail.receive_date}_${detail.shipping_type || "none"}_${detail.division || "none"}`;

            if (!acc[carrierKey]) {
              acc[carrierKey] = {
                clientId: clientId,
                carrierId: detail.carrier.id,
                daily_planning_id: detail.daily_planning_id,
                daily_planning_details_id: detail.id,
                name: detail.carrier.name,
                shipping_type: detail.shipping_type,
                division: detail.division,
                receive_by: detail.receive_by,
                receive_date: detail.receive_date,
                review_by: detail.review_by,
                review_date: detail.review_date,
                reconcile_by: detail.reconcile_by,
                reconcile_date: detail.reconcile_date,
                send_by: detail.send_by,
                send_date: detail.send_date,
                no_invoices: 0,
                amount_of_invoice: 0,
                currency: detail.currency,
                notes: detail.notes,
                source: detail.source,
              };
            }

            acc[carrierKey].amount_of_invoice +=
              Number.parseFloat(detail.amount_of_invoice) || 0;
            acc[carrierKey].no_invoices += detail.no_invoices ?? 0;
          }
        });
    });

    return acc;
  }, [dailyPlanningDetailsStatement]);

  useEffect(() => {
    setCarriers(Object.values(aggregatedData));
  }, [aggregatedData]);

  const filteredCarriers = useMemo(() => {
    const today = new Date().toISOString().split("T")[0];
    return carriers.filter((carrier: any) => {
      return (
        carrier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        carrier.currency?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
  }, [carriers, searchTerm]);

  const totals: any = useMemo(() => {
    return filteredCarriers.reduce(
      (totals: any, carrier: any) => {
        totals.no_invoices += Number.parseInt(carrier.no_invoices);
        totals.amount_of_invoice += Number.parseInt(carrier.amount_of_invoice);
        return totals;
      },
      {
        no_invoices: 0,
        amount_of_invoice: 0,
      }
    );
  }, [filteredCarriers]);

  const csvData = useMemo(() => {
    const headers = [
      "Carrier",
      "No. of Invoices",
      "Amount of Invoice",
      "Currency",
    ];

    const rows = filteredCarriers.map((carrier: any) => [
      carrier.name,
      carrier.no_invoices,
      carrier.amount_of_invoice,
      carrier.currency,
    ]);

    const totalRow = [
      "Total",
      totals.no_invoices,
      totals.amount_of_invoice,
      totals.currency,
    ];

    return [headers, ...rows, totalRow];
  }, [filteredCarriers, totals]);

  const handleReconcileClick = (carrier: any) => {
    setSelectedCarrier({ ...carrier, updateType: "reconcile" });
    setReconcileDate("");
    setShowModal(true);
  };

  const handleReviewClick = (carrier: any) => {
    if (carrier.reconcile_date) {
      setSelectedCarrier({ ...carrier, updateType: "review" });
      setShowModal(true);
    }
  };

  const handleSendClick = (carrier: any) => {
    if (carrier.reconcile_date && carrier.review_date) {
      setSelectedCarrier({ ...carrier, updateType: "send" });
      setShowModal(true);
    }
  };

  const handleUpdateCarrier = (updatedCarrier: any) => {
    setCarriers(prevCarriers => 
      prevCarriers.map(carrier => 
        carrier.daily_planning_details_id === updatedCarrier.daily_planning_details_id 
          ? updatedCarrier 
          : carrier
      )
    );
  };

  return (
    <div className="">
      {/* <div className="flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search carriers or currencies..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div> */}
     <div className="o border rounded-md">
        <Table>
          <TableHeader className="sticky top-0 z-10 bg-white">
            <TableRow className="bg-muted/50">
              <TableHead className="text-center font-semibold">Sr. No.</TableHead>
              <TableHead className="w-[200px] font-semibold">Carrier</TableHead>
              <TableHead className="w-[150px] font-semibold">
                Transport Type
              </TableHead>
              <TableHead className="text-center font-semibold">
                Division
              </TableHead>
              <TableHead className="text-center font-semibold ">
                Received By
              </TableHead>
              <TableHead className="text-center font-semibold">
                Received Date
              </TableHead>
              <TableHead className="text-center font-semibold">
                Reconcile By
              </TableHead>
              <TableHead className="text-center font-semibold">
                Reconcile Date
              </TableHead>
              <TableHead className="text-center font-semibold">
                Review By
              </TableHead>
              <TableHead className="text-center font-semibold">
                Review Date
              </TableHead>
              <TableHead className="text-center font-semibold">
                Send By
              </TableHead>
              <TableHead className="text-center font-semibold">
                Send Date
              </TableHead>
              <TableHead className="text-center font-semibold">
                No. Of invoice
              </TableHead>
              <TableHead className="text-center font-semibold">
                Amount Of Invoice
              </TableHead>
              <TableHead className="text-center font-semibold">
                Currency
              </TableHead>
              <TableHead className="text-center font-semibold">Notes</TableHead>
              <TableHead className="text-center font-semibold">
                Action
              </TableHead>
            </TableRow>
          </TableHeader>
          </Table>
          <div className="max-h-[450px] overflow-y-auto">
            <Table >
          <TableBody>
            {filteredCarriers.map((carrier: any, index: number) => (
              <TableRow
                key={index}
                className="bg-muted/50 sticky top-0 z-10 bg-white"
              >
                <TableCell className="text-center font-bold">{index + 1}</TableCell>
                <TableCell className="font-medium">{carrier.name}</TableCell>
                <TableCell className="font-medium">
                  {carrier.shipping_type}
                </TableCell>
                <TableCell className="text-center">
                  {carrier.division}
                </TableCell>
                <TableCell className="text-center">
                  {carrier.receive_by ? carrier.receive_by : "-"}
                </TableCell>
                <TableCell className="text-center">
                  <Badge
                    variant={"outline"}
                    className="w-[80px] h-[30px] flex items-center justify-center"
                  >
                    {carrier.receive_date
                      ? formatDate(carrier.receive_date)
                      : "-"}
                  </Badge>
                </TableCell>
                <TableCell className="text-center">
                  {carrier.reconcile_by ? carrier.reconcile_by : "-"}
                </TableCell>
                <TableCell className="text-center">
                  <Badge
                    variant={"outline"}
                    className="w-[80px] h-[30px] text-center flex items-center justify-center"
                    onClick={() => {
                      if (
                        (carrier.source === "current" || carrier.source === "brought_forward") &&
                        (!carrier.reconcile_date ||
                          (carrier.reconcile_date && carrier.review_date === null)) &&
                        (carrier.source === "brought_forward" ? (!carrier.reconcile_date && !carrier.review_date) : true)
                      ) {
                        handleReconcileClick(carrier);
                      }
                    }}
                  >
                    {carrier.reconcile_date
                      ? formatDate(carrier.reconcile_date)
                      : "-"}
                  </Badge>
                </TableCell>
                <TableCell className="text-center">
                  {carrier.review_by ? carrier.review_by : "-"}
                </TableCell>
                <TableCell className="text-center">
                  <Badge
                    variant={"outline"}
                    className="w-[80px] h-[30px] text-center flex items-center justify-center"
                    onClick={() => {
                      if (
                        (carrier.source === "current" || carrier.source === "brought_forward") &&
                        (!carrier.review_date ||
                          (carrier.review_date &&
                            carrier.reconcile_date &&
                            carrier.send_date === null)) &&
                            (carrier.source === "brought_forward" ? !carrier.review_date : true)
                      ) {
                        handleReviewClick(carrier);
                      }
                    }}
                  >
                    {carrier.review_date
                      ? formatDate(carrier.review_date)
                      : "-"}
                  </Badge>
                </TableCell>
                <TableCell className="text-center">
                  {carrier.send_by ? carrier.send_by : "-"}
                </TableCell>
                <TableCell className="text-center">
                  <Badge
                    variant={"outline"}
                    className="w-[77px] h-[30px] text-center flex items-center justify-center"
                    onClick={() => {
                      if (
                        (carrier.source === "current" || carrier.source === "brought_forward") &&
                        carrier.reconcile_date &&
                        carrier.review_date &&
                        !carrier.send_date
                      ) {
                        handleSendClick(carrier);
                      }
                    }}
                  >
                    {carrier.send_date ? formatDate(carrier.send_date) : "-"}
                  </Badge>
                </TableCell>
                <TableCell className="text-center">
                  {carrier.no_invoices}
                </TableCell>
                <TableCell className="text-center">
                  {carrier.amount_of_invoice.toLocaleString()}
                </TableCell>
                <TableCell className="text-center">
                  {carrier.currency}
                </TableCell>
                <TableCell className="text-center">{carrier.notes}</TableCell>
                <TableCell className="text-right  ">
                  <TableCell className="text-right  ">
                    <PermissionWrapper
                      permissions={permissions}
                      requiredPermissions={["update-dailyplanningdetails"]}
                    >
                      <UpdateDailyPlanningDetails
                        data={carrier}
                        type="STATEMENT_TABLE"
                      />
                    </PermissionWrapper>
                    <PermissionWrapper
                      permissions={permissions}
                      requiredPermissions={["delete-dailyplanningdetails"]}
                    >
                      <DeleteRow
                        route={`${daily_planning_details_routes.DELETE_DAILY_PLANNING_DETAILS}/${carrier.daily_planning_details_id}`}
                      />
                    </PermissionWrapper>
                  </TableCell>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell></TableCell>
              <TableCell colSpan={2} className="font-bold">
                Total
              </TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell></TableCell>
              <TableCell className="text-center font-bold">
                {totals.no_invoices}
              </TableCell>
              <TableCell className="text-center font-bold">
                {totals.amount_of_invoice.toLocaleString()}
              </TableCell>
              <TableCell></TableCell>
            </TableRow>
          </TableFooter>
        </Table>
        </div>
      </div>
      {showModal && selectedCarrier && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-[400px]">
            <h2 className="text-lg font-semibold mb-4">
              {selectedCarrier?.updateType === "review"
                ? "Update Review Date"
                : selectedCarrier?.updateType === "send"
                ? "Update Send Date"
                : "Update Reconcile Date"}
            </h2>

            <form
              onSubmit={async (e) => {
                e.preventDefault();
                if (!reconcileDate) {
                  alert("Please select a reconcile date.");
                  return;
                }

                if (new Date(reconcileDate) > new Date(dailyPlanningDate)) {
                  alert(`Date cannot be after ${formattedMaxDate}`);
                  return;
                }

                // Validate date is not before the previous date in sequence
                const minDate = getMinDate(selectedCarrier, selectedCarrier.updateType);
                if (minDate && new Date(reconcileDate) < new Date(minDate)) {
                  alert(`Date cannot be before ${minDate}`);
                  return;
                }

                const updateId = selectedCarrier?.daily_planning_details_id;

                if (!updateId) {
                  alert("Missing ID for update.");
                  console.error(
                    "Missing daily_planning_details_id:",
                    selectedCarrier
                  );
                  return;
                }
                try {
                  const fieldToUpdate =
                    selectedCarrier?.updateType === "review"
                      ? "review_date"
                      : selectedCarrier?.updateType === "send"
                      ? "send_date"
                      : "reconcile_date";

                  const updatedPayload: any = {
                    [fieldToUpdate]: reconcileDate,
                  };
                  if (selectedCarrier?.updateType === "review") {
                    updatedPayload.review_by = userData.username;
                  } else if (selectedCarrier?.updateType === "send") {
                    updatedPayload.send_by = userData.username;
                  } else {
                    updatedPayload.reconcile_by = userData.username;
                  }

                  const response = await formSubmit(
                    `${daily_planning_details_routes.UPDATE_DAILY_PLANNING_DETAILS_STATEMENT}/${selectedCarrier.daily_planning_details_id}`,
                    "PUT",
                    updatedPayload
                  );
                  if (response.success && response.data) {
                    const updatedCarrier = {
                      ...selectedCarrier,
                      ...response.data,
                      carrierId:
                        response.data.carrier?.id ?? selectedCarrier.carrierId,
                      name: response.data.carrier?.name ?? selectedCarrier.name,
                    };
                    handleUpdateCarrier(updatedCarrier);
                    setSelectedCarrier(updatedCarrier);
                    setShowModal(false);
                  } else {
                    console.error("Update failed:", response.message);
                    alert("Failed to update date: " + response.message);
                  }
                } catch (err) {
                  console.error("API error:", err);
                  alert("Something went wrong while updating.");
                }
              }}
            >
              <label className="block mb-2 text-sm font-medium text-gray-700">
                {selectedCarrier?.updateType === "review"
                  ? "Review Date"
                  : selectedCarrier?.updateType === "send"
                  ? "Send Date"
                  : "Reconcile Date"}
              </label>
              <input
                type="date"
                value={reconcileDate}
                onChange={(e) => setReconcileDate(e.target.value)}
                className="w-full mb-4 p-2 border rounded-md"
                max={formattedMaxDate}
                min={getMinDate(selectedCarrier, selectedCarrier.updateType)}
              />
              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowModal(false)}
                >
                  Cancel
                </Button>
                <Button type="submit">Save</Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ViewStatementTable;
