"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticateSuperAdmin = exports.authenticate = void 0;
const helpers_1 = require("../utils/helpers");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const authenticate = async (req, res, next) => {
    try {
        // const cookie = req.headers.authorization(" ")[1];
        const cookie = req.headers.cookie;
        // const cookie = req.headers.cookie;
        if (!cookie) {
            return res
                .status(400)
                .json({ success: false, message: "No cookie found" });
        }
        const [type, token] = cookie?.split("=");
        let decoded;
        if (type && type === "customer") {
            decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET);
            const { id } = decoded;
            req.user_id = id;
            if (req.user_id) {
                const existingSession = await prisma.session.findUnique({
                    where: {
                        user_id: req.user_id,
                    },
                });
                if (!existingSession) {
                    return res.status(400).json({
                        success: false,
                        message: "Session expired or not valid",
                    });
                }
            }
        }
        else {
            decoded = jsonwebtoken_1.default.verify(cookie, process.env.JWT_SECRET);
            const { id } = decoded;
            req.user_id = id;
            if (req.user_id) {
                const existingSession = await prisma.session.findUnique({
                    where: {
                        user_id: req.user_id,
                    },
                });
                if (!existingSession) {
                    return res.status(400).json({
                        success: false,
                        message: "Session expired or not valid",
                    });
                }
            }
        }
        const { corporation_id } = decoded;
        //  ("coo", corporation_id);
        req.corporation_id = corporation_id;
        //  ("user_id in authenticate:", req.user_id);
        //  ("Decoded token:", decoded);
        next();
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.authenticate = authenticate;
const authenticateSuperAdmin = async (req, res, next) => {
    try {
        const cookie = req.headers.authorization?.split(" ")[1];
        //  ("cooke", cookie);
        if (!cookie) {
            return res
                .status(400)
                .json({ success: false, message: "No token found" });
        }
        const decoded = jsonwebtoken_1.default.verify(cookie, process.env.JWT_SECRET);
        const { super_admin_id } = decoded;
        req.super_admin_id = super_admin_id;
        next();
    }
    catch (error) {
        return (0, helpers_1.handleError)(req, error);
    }
};
exports.authenticateSuperAdmin = authenticateSuperAdmin;
//# sourceMappingURL=authentication.js.map