import { time } from "console";
import { permission } from "process";
import { use } from "react";
import { date, z } from "zod";
import { DateTime } from "luxon";

export const LoginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z
    .string()
    .min(1, "Password is required")
    .min(8, "Password must have at least 8 characters"),
});

export const createEmployeeSchema = z
  .object({
    username: z.string().min(1, "Username is required"),
    firstName: z.string().min(1, "FirstName is required"),
    lastName: z.string().min(1, "LastName is required"),
    email: z.string().min(1, "Email is required").email("Invalid email"),
    password: z
      .string()
      .min(1, "Password is required")
      .min(8, "Password must have at least 8 characters"),
    confirmPassword: z
      .string()
      .min(1, "Password is required")
      .min(8, "Password must have at least 8 characters."),
    user_type: z.enum(["HR", "TL", "CSA", "MEMBER"]).optional(),
    country: z.string().optional(),
    state: z.string().optional(),
    city: z.string().optional(),
    address: z.string().optional(),
    role_id: z.string().min(1, "Role is required"),
    level: z.string().min(1, "Title is required"),
    parent_id: z.string().optional(),
    branch: z.string().min(1, "Branch is required"),
    date_of_joining: z
      .string()
      .nonempty("Date of joining is required")
      .refine(
        (val) => {
          const today = new Date();
          const inputDate = new Date(val);
          return inputDate <= today;
        },
        {
          message: "Date of joining cannot be in the future",
        }
      ),
    clients: z.array(z.number()).optional(),
  })

  .superRefine((data, rtx) => {
    if (data.password !== data.confirmPassword) {
      rtx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["confirmPassword"],
        message: "Passwords do not match",
      });
    }

    if (
      data.level !== "5" &&
      (!data.parent_id || data.parent_id.trim() === "")
    ) {
      rtx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["parent_id"],
        message: "Reporting To is required ",
      });
    }
    if (data.level !== "5" && (!data.clients || data.clients.length === 0)) {
      rtx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["clients"],
        message: "At least one client must be selected",
      });
    }
  });
export const updateEmployeeSchema = z
  .object({
    username: z.string().min(1, "Username is required"),
    firstName: z.string().min(1, "firstName is required"),
    lastName: z.string().min(1, "lastName is required"),
    email: z.string().min(1, "Email is required").email("Invalid email"),
    // user_type: z.enum(["HR", "TL", "CSA", "NORMAL_MEMBER"]).optional(),
    role_id: z.string().min(1, "Role is required"),
    level: z.string().min(1, "Title is required"),
    parent_id: z.string().optional(),
    branch: z.string().min(1, "Branch is required"),
    date_of_joining: z
      .string()
      .nonempty("Date of joining is required")
      .refine(
        (val) => {
          const today = new Date();
          const inputDate = new Date(val);
          return inputDate <= today;
        },
        {
          message: "Date of joining cannot be in the future",
        }
      ),
    clients: z.array(z.number()).optional(),
  })
  .superRefine((data, ctx) => {
    if (
      data.level !== "5" &&
      (!data.parent_id || data.parent_id.trim() === "")
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["parent_id"],
        message: "Reporting To is required ",
      });
    }
    if (data.level !== "5" && (!data.clients || data.clients.length === 0)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["clients"],
        message: "Please select at least one client",
      });
    }
  });

export const createClientSchema = z.object({
  associate: z.string().min(1, "Associate is required"),
  client_name: z.string().min(1, "Client name is required"),
  // ownership: z.string().min(1, "Ownership is required"),
  ownership: z.string().min(1, "Ownership is required"),
  branch: z.string().min(1, "branch is required"),
});

export const createCarrierSchema = z.object({
  name: z.string().min(1, "Carrier Name is required"),
  carrier_2nd_name: z.string().min(1, "Carrier Name - 2 is required"),
  carrier_code: z.string().min(1, "VAP ID is required"),
});

export const addWorkTypeSchema = z.object({
  work_type: z.string().min(1, "Work type is required"),
  category: z.string().min(1, "Category is required"),
  does_it_require_planning_number: z.string().optional(),
  is_work_carrier_specific: z.string().optional(),
  is_backlog_regular_required: z.string().optional(),
});

export const addTaskSchema = z.object({
  description: z.string().optional(),
  date: z.any(),
  startTime: z.any(),
  endTime: z.date().optional(),
  clientName: z.string().min(1, "Client name is required").optional(),
  carrierName: z.string().optional(),
  workType: z.string().min(1, "Work type is required"),
  category: z.string().min(1, "Category is required"),
  planningNumber: z.string().optional(),
  duration: z.string(),
  expectedCompletionTime: z.any(),
  actualNumber: z.any(),
  actualCompletionTime: z.any(),
  timerDuration: z.any(),
});

export const addTaskSchemaPlus = z
  .object({
    description: z.string().optional(),
    date: z.string().min(1, "Date is required"),
    startTime: z
      .string()
      .min(1, "Start time is required")
      .regex(
        /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i,
        "Start time must be in hh:mm AM/PM format."
      )
      .transform((value) => value.toUpperCase()), // Ensure AM/PM is uppercase
    endTime: z
      .string()
      .min(1, "End time is required")
      .regex(
        /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i,
        "End time must be in hh:mm AM/PM format."
      )
      .transform((value) => value.toUpperCase()), // Ensure AM/PM is uppercase
    clientName: z.string().min(1, "Client is required"),
    carrierName: z.string().min(1, "Carrier is required"),
    workType: z.string().min(1, "Work type is required"),
    category: z.number().optional(),
    planningNumber: z.string().optional(),
    duration: z.string().optional(),
    expectedCompletionTime: z.any(),
    actualNumber: z.any(),
    actualCompletionTime: z.any(),
    timerDuration: z.any(),
    task_type: z.string().optional(),
  })
  .refine(
    (data) => {
      const today = DateTime.now();
      const selectedDate = DateTime.fromISO(data.date);

      if (selectedDate.hasSame(today, "day")) {
        const currentTime = DateTime.now();

        const startTime = DateTime.fromFormat(data.startTime, "hh:mm a");

        return startTime <= currentTime;
      }
      return true;
    },
    {
      message: "Future start times are not allowed.",
      path: ["startTime"],
    }
  )
  .refine(
    (data) => {
      const startTime = DateTime.fromFormat(data.startTime, "hh:mm a");
      const endTime = DateTime.fromFormat(data.endTime, "hh:mm a");
      return endTime > startTime; // Ensure endTime is strictly greater than startTime
    },
    {
      message: "End time should be greater than the start time.",
      path: ["endTime"],
    }
  )
  .refine(
    (data) => {
      const today = DateTime.now();
      const selectedDate = DateTime.fromISO(data.date);

      if (selectedDate.hasSame(today, "day")) {
        const currentTime = DateTime.now();

        const endTime = DateTime.fromFormat(data.endTime, "hh:mm a");

        return endTime <= currentTime;
      }
      return true;
    },
    {
      message: "Future end times are not allowed.",
      path: ["endTime"],
    }
  );
export const manualAddTaskSchema = z
  .object({
    notes: z.string().min(1, "Notes are required"),
    date: z.string().min(1, "Date is required"),
    startTime: z
      .string()
      .min(1, "Start time is required")
      .regex(
        /^(0[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i,
        "Start time must be in hh:mm AM/PM format."
      )
      .transform((value) => value.toUpperCase()), // Ensure AM/PM is uppercase
    endTime: z
      .string()
      .min(1, "End time is required")
      .regex(
        /^(0[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i,
        "End time must be in hh:mm AM/PM format."
      )
      .transform((value) => value.toUpperCase()), // Ensure AM/PM is uppercase
  })
  .refine(
    (data) => {
      const today = new Date();
      const selectedDate = new Date(data.date);

      if (selectedDate.toDateString() === today.toDateString()) {
        const currentTime = new Date();

        // Parse time strings to Date objects for comparison
        const startTime = convertTimeTo24HourFormat(data.startTime);

        return startTime <= currentTime;
      }
      return true;
    },
    {
      message: "Future start times are not allowed.",
      path: ["startTime"],
    }
  )
  .refine(
    (data) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Remove time part to compare only the date

      const selectedDate = new Date(data.date);
      selectedDate.setHours(0, 0, 0, 0);

      return selectedDate <= today;
    },
    {
      message: "Future dates are not allowed.",
      path: ["date"],
    }
  )
  .refine(
    (data) => {
      const startTime = convertTimeTo24HourFormat(data.startTime);
      const endTime = convertTimeTo24HourFormat(data.endTime);
      return endTime > startTime;
    },
    {
      message: "End time should be greater than the start time.",
      path: ["endTime"],
    }
  )
  .refine(
    (data) => {
      const today = new Date();
      const selectedDate = new Date(data.date);

      if (selectedDate.toDateString() === today.toDateString()) {
        const currentTime = new Date();

        const endTime = convertTimeTo24HourFormat(data.endTime);

        return endTime <= currentTime;
      }
      return true;
    },
    {
      message: "Future end times are not allowed.",
      path: ["endTime"],
    }
  );

// Helper function to convert 12-hour time format to 24-hour format
function convertTimeTo24HourFormat(time: string): Date {
  const date = new Date();
  const [hourMin, modifier] = time.split(" "); // Split time from AM/PM part
  let [hours, minutes] = hourMin.split(":").map(Number); // Split hour and minute

  // Convert to 24-hour format
  if (modifier === "PM" && hours < 12) hours += 12;
  if (modifier === "AM" && hours === 12) hours = 0;

  date.setHours(hours, minutes, 0, 0); // Set hours and minutes
  return date;
}

export const corporationSchema = z.object({
  corporation_id: z.number().int().optional(),
  username: z
    .string()
    .min(1, "Username is required")
    .max(255, "Username is too long"),
  email: z.string().email("Invalid email format").max(255, "Email is too long"),
  password: z.string().min(8, "Password must have at least 8 characters"),
  confirmPassword: z
    .string()
    .min(8, "Password must have at least 8 characters"),
  country: z.string().max(255, "Country is too long"),
  state: z.string().max(255, "State is too long"),
  city: z.string().max(255, "City is too long"),
  address: z.string().max(255, "Address is too long"),
});

export const selectClientSchema = z.object({
  client_name: z.string().max(255, "Name is too long"),
  client_id: z.string(),
});

export const updateCorporationSchema = z.object({
  corporation_id: z.number().int().optional(),
  username: z
    .string()
    .min(1, "Username is required")
    .max(255, "Username is too long"),
  email: z.string().email("Invalid email format").max(255, "Email is too long"),
  country: z.string().max(255, "Country is too long"),
  state: z.string().max(255, "State is too long"),
  city: z.string().max(255, "City is too long"),
  address: z.string().max(255, "Address is too long"),
});

export const workReportSchema = z.object({
  work_report_id: z.number().int().optional(),
  date: z.string().refine((value) => !isNaN(new Date(value).getTime()), {
    message: "Invalid date format",
  }),
  // .transform((value) => new Date(value)),
  id: z.number().int(),
  client_id: z.number().int(),
  carrier_id: z.number().int(),
  work_type_id: z.number().int(),
  category: z.enum(["AUDIT", "ENTRY", "REPORT"]),
  planning_nummbers: z.string().max(255).optional(),
  expected_time: z
    .string()
    .refine((value) => !isNaN(new Date(value).getTime()), {
      message: "Invalid date format for expected time",
    }),
  // .transform((value) => new Date(value)),
  actual_number: z.string().max(255).optional(),
  start_time: z.string().refine((value) => !isNaN(new Date(value).getTime()), {
    message: "Invalid time format for start time",
  }),
  // .transform((value) => new Date(value)),
  finish_time: z.string().refine((value) => !isNaN(new Date(value).getTime()), {
    message: "Invalid time format for finish time",
  }),
  // .transform((value) => new Date(value)),
  time_spent: z.string().refine((value) => !isNaN(new Date(value).getTime()), {
    message: "Invalid time format for time spent",
  }),
  // .transform((value) => new Date(value)),
  notes: z.string().max(255),
});
export const AddRolesFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  permission: z.array(z.number()).optional(),
  action: z.any().optional(),
});

export const AddDailyPlanningSchema = z.object({
  daily_planning_date: z.string().min(1, "Date is required"),
  client_id: z.string().min(1, "Client is required"),
});
export const AddDailyPlanningDetailsSchema = z
  .object({
    type: z.enum(["INVOICE_ENTRY_STATUS", "STATEMENT_TABLE"]),
    carrier: z.string().optional(),
    old: z.string().optional(),
    new: z.string().optional(),
    total: z.string().optional(),
    ute: z.string().optional(),
    receive_date: z.string().optional(),
    no_invoices: z.string().optional(),
    amount_of_invoice: z.union([z.string(), z.number()]).optional(), // Accept both string and number
    currency: z.enum(["USD", "CAD", "KRW"]).optional(),
    shipping_type: z.string().optional(),
    reconcile_date: z.string().optional(),
    division: z.string().optional(),
    send_date: z.string().optional(),
    notes: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.type === "INVOICE_ENTRY_STATUS") {
      // Require fields for "invoice entry status"
      ["carrier", "old", "new", "total", "ute"].forEach((field) => {
        if (!data[field as keyof typeof data]) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: [field],
            message: `${field} is required when planningType is "invoice entry status"`,
          });
        }
      });
    } else if (data.type === "STATEMENT_TABLE") {
      // Require fields for "statement table"
      [
        "receive_date",
        "no_invoices",
        "amount_of_invoice",
        "currency",
        "reconcile_date",
        "shipping_type",
        // "division",
        "carrier",
      ].forEach((field) => {
        if (!data[field as keyof typeof data]) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: [field],
            message: `${field} is required when planningType is "statement table"`,
          });
        }
      });
    }
  });

export const createClientCarrierSchema = z.object({
  carrier_id: z.string().nonempty("Carrier is required"),
  client_id: z.string().nonempty("Client is required"),
  payment_terms: z.string().nonempty("Payment terms are required"),
  client_name: z.string().optional(),
});

export const actualNumberSchema = z.object({
  actual_number: z.string().min(1, { message: "Please enter a actual number" }),
  notes: z.string().optional(),
});

export const createCategorySchema = z.object({
  name: z.string().min(1, "Name is required"),
});

export const createBranchSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

export const createAssociateSchema = z.object({
  name: z.string().min(1, "Name is required"),
});

export const addWorkReportSchema = z.object({
  date: z.string().min(1, "Date is required"),
  username: z.string().min(1, "Username is required"),
  clientname: z.string().min(1, "Client name is required"),
  carriername: z.string().min(1, "Carrier name is required"),
  work_type: z.string().min(1, "Work type is required"),
  category: z.string().min(1, "Category is required"),
  actual_number: z.string().min(1, "Actual number is required"),
  start_time: z.string().min(1, "Start time is required"),
  finish_time: z.string().min(1, "Finish time is required"),
  time_spent: z.string().min(1, "Time spent is required"),
  notes: z.string().min(1, "Notes are required"),
  task_type: z.string().min(1, "Task type is required"),
});

export const updateWorkReportSchema = z
  .object({
    date: z.string().optional(),

    startTime: z
      .string()
      .optional() // Allow the start time to be empty or undefined
      .refine(
        (value) =>
          value === undefined ||
          /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i.test(value), // Regex validation only if value is provided
        "Start time must be in hh:mm AM/PM format."
      )
      .transform((value) => (value ? value.toUpperCase() : value)), // Ensure AM/PM is uppercase if provided

    endTime: z
      .string()
      .optional() // Allow the end time to be empty or undefined
      .refine(
        (value) =>
          value === undefined ||
          /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i.test(value), // Regex validation only if value is provided
        "End time must be in hh:mm AM/PM format."
      )
      .transform((value) => (value ? value.toUpperCase() : value)), // Ensure AM/PM is uppercase if provided
    module: z.string().optional(),
    action: z.string().optional(),
  })
  .refine(
    (data) => {
      const today = new Date();
      const selectedDate = new Date(data.date);

      // Only check future start times if the start time is provided
      if (data.startTime) {
        const currentTime = new Date();
        const startTime = convertTimeTo24HourFormat(data.startTime);

        if (selectedDate.toDateString() === today.toDateString()) {
          return startTime <= currentTime;
        }
      }
      return true; // Skip check if no start time
    },
    {
      message: "Future start times are not allowed.",
      path: ["startTime"],
    }
  )
  .refine(
    (data) => {
      // Only check end time if both startTime and endTime are provided
      if (data.startTime && data.endTime) {
        const startTime = convertTimeTo24HourFormat(data.startTime);
        const endTime = convertTimeTo24HourFormat(data.endTime);
        return endTime > startTime;
      }
      return true; // Skip check if either is missing
    },
    {
      message: "End time should be greater than the start time.",
      path: ["endTime"],
    }
  )
  .refine(
    (data) => {
      // Only check future end times if the end time is provided
      if (data.endTime) {
        const today = new Date();
        const selectedDate = new Date(data.date);
        const currentTime = new Date();

        const endTime = convertTimeTo24HourFormat(data.endTime);

        if (selectedDate.toDateString() === today.toDateString()) {
          return endTime <= currentTime;
        }
      }
      return true; // Skip check if no end time
    },
    {
      message: "Future end times are not allowed.",
      path: ["endTime"],
    }
  );
