"use client";
import { FaPause, FaTimes } from "react-icons/fa";
import React, { useEffect, useRef, useState } from "react";

import useDynamicForm from "@/lib/useDynamicForm";
import { actualNumberSchema, selectClientSchema } from "@/lib/zodSchema";

import { formSubmit, getAllData } from "@/lib/helpers";
import { Button } from "@/components/ui/button";
import { FaStop } from "react-icons/fa";
import { formatTime } from "@/lib/formatTime";
import CryptoJS from "crypto-js";
import { useRouter } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { client_routes, workreport_routes } from "@/lib/routePath";
import FormInput from "@/app/_component/FormInput";
import { Form } from "@/components/ui/form";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { IoMdClose } from "react-icons/io";

const encryptData = (data: any): string => {
  return CryptoJS.AES.encrypt(
    JSON.stringify(data),
    process.env.NEXT_PUBLIC_SECRET_KEY!
  ).toString();
};

const decryptData = (encryptedData: string): any => {
  const bytes = CryptoJS.AES.decrypt(
    encryptedData,
    process.env.NEXT_PUBLIC_SECRET_KEY!
  );
  const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
  return JSON.parse(decryptedData);
};

const generateHash = (data: any): string => {
  return CryptoJS.SHA256(JSON.stringify(data)).toString(CryptoJS.enc.Base64);
};

const storeData = (key: string, data: any): void => {
  const hash = generateHash(data);
  const encryptedData = encryptData(data);
  const dataWithHash = { encryptedData, hash };
  localStorage.setItem(key, JSON.stringify(dataWithHash));
};

const retrieveData = (key: string): any | null => {
  const dataWithHash = JSON.parse(localStorage.getItem(key) || "null");
  if (!dataWithHash) return null;

  const { encryptedData, hash } = dataWithHash;
  const decryptedData = decryptData(encryptedData);

  const calculatedHash = generateHash(decryptedData);
  if (calculatedHash === hash) {
    return decryptedData;
  } else {
    console.error("Data integrity check failed - tampering detected.");
    return null;
  }
};

type Client = {
  client_id: string;
  client_name: string;
};

export function SelectClient({
  setSelectedClient,
  isTimerRunning,
  elapsedTime,
  setElapsedTime,
  setIsTimerRunning,
  setSeletedWorkType,
  setSelectedCarrier,
  seletedWorkType,
  workTypes,
}: {
  setSelectedClient: (value: string) => void;
  isTimerRunning: boolean;
  elapsedTime: number;
  setElapsedTime: React.Dispatch<React.SetStateAction<number>>;
  setIsTimerRunning: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedCarrier: React.Dispatch<React.SetStateAction<string>>;
  setSeletedWorkType: React.Dispatch<React.SetStateAction<boolean>>;
  workTypes: any;
  seletedWorkType: any;
}) {
  const [reportId, setReportId] = useState<string>("");
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const [pausedTasks, setPausedTasks] = useState<{
    [key: string]: { paused: boolean; startTime: number; elapsedTime: number };
  }>({});
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [actualNumber, setActualNumber] = useState<number>(0);
  const [error, setError] = useState<string | null>(null);
  const [closeModal, setCloseModal] = useState<boolean>(false);
  const router = useRouter();

  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleCloseModal = () => {
    setCloseModal(true);
    setIsModalOpen(false);
    form.reset();
  };


  useEffect(()=>{ 
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        handleCloseModal();
      }
    };
    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  })
  
  useEffect(() => {
    if (isModalOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isModalOpen]);
  useEffect(() => {


    if (isTimerRunning) {
      setIsPaused(false);

    }
  }, [isPaused, isTimerRunning, setIsPaused]);

  useEffect(() => {
    const storedData = retrieveData("timerData");
    if (storedData) {
      const { startTime, elapsedTime: storedElapsedTime } = storedData;
      const now = Date.now();
      const timeElapsed = Math.floor((now - startTime) / 1000);
      setElapsedTime(timeElapsed + storedElapsedTime);
      setIsTimerRunning(true);
    }
    // Object.keys(pausedTasks).forEach((reportId) => {
    //   const taskState = pausedTasks[reportId];
    // if (taskState.paused && !isTimerRunning) {
    if (isTimerRunning) {
      const interval = setInterval(() => {
        setElapsedTime((prev) => {
          const updatedTime = prev + 1;
          storeData("timerData", {
            startTime: Date.now(),
            elapsedTime: updatedTime,
          });
          return updatedTime;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isTimerRunning, setElapsedTime]);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     const allWorkReport = await getAllData(
  //       workreport_routes.GETALL_WORKREPORT
  //     );
  //     const unfinished = allWorkReport.find(
  //       (item: any) =>
  //         item.work_status === "STARTED" || item.work_status === "RESUMED"
  //     );
  //     setReportId(unfinished?.id || "");
  //   };
  //   fetchData();
  // }, [isTimerRunning]);

  const handleStopTime = async () => {
    const temp = workTypes.find(
      (workType: any) =>
        parseInt(workType.id) ===
        parseInt(localStorage.getItem("workType") || "0")
    );

    if (temp?.does_it_require_planning_number) {
      setIsModalOpen(true);
    } else {
      if (reportId) {
        const response = await formSubmit(
          `${workreport_routes.UPDATE_WORKREPORT}/${reportId}`,
          "PUT",
          {
            finish_time: new Date(),
            actual_number: JSON.stringify(actualNumber),
          }
        );
        router.refresh();
        if (response.success) {
          setIsTimerRunning(false);
          localStorage.removeItem("timerData");
          setElapsedTime(0);
          setIsModalOpen(false);
          setActualNumber(0);
          localStorage.removeItem("workType");
        }
      }
    }
  };

  const handleModalSubmit = async () => {
    const result = actualNumberSchema.safeParse(actualNumber);

    if (!result.success) {
      setError(result.error.errors[0].message);
      return;
    }
    setError(null);
    if (reportId) {
      const response = await formSubmit(
        `${workreport_routes.UPDATE_WORKREPORT}/${reportId}`,
        "PUT",
        {
          finish_time: new Date(),
          actual_number: JSON.stringify(actualNumber),
        }
      );

      router.refresh();
      if (response.success) {
        setIsTimerRunning(false);
        localStorage.removeItem("timerData");
        setElapsedTime(0);
        setIsModalOpen(false);
        setActualNumber(0);
      }
    }
  };

  const handlePause = async (reportId: string) => {


    if (reportId && !isPaused) {
      const response = await formSubmit(
        `${workreport_routes.UPDATE_WORKREPORT}/${reportId}`,
        "PUT",
        {
          action: "pause",
        }
      );
      //  (response);
      router.refresh();
      if (response.success) {
        const currentTime = Date.now();
        setPausedTasks((prev) => ({
          ...prev,
          [reportId]: {
            paused: true,
            startTime: currentTime,
            elapsedTime: elapsedTime,
          },
        }));
        setIsPaused(true);
        setIsTimerRunning(false);
        localStorage.removeItem("timerData");
        localStorage.removeItem("pausedTasks");
        router.refresh();
      }
    }
  };

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.ctrlKey && event.key === "p") {
        event.preventDefault(); // Prevent the default print behavior
        handlePause(reportId);
      } else if (event.ctrlKey && event.key === "s") {
        event.preventDefault(); // Prevent the default print behavior
        handleStopTime();
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [reportId, handlePause]);

  const onSubmit = async (values: any) => {
    // const result = actualNumberSchema.safeParse(actualNumber);

    // if (!result.success) {
    //   setError(result.error.errors[0].message);
    //   return;
    // }
    // setError(null);
   
    if (reportId) {
      const response = await formSubmit(
        `${workreport_routes.UPDATE_WORKREPORT}/${reportId}`,
        "PUT",
        {
          finish_time: new Date(),
          actual_number: values.actual_number,
          notes: values.notes,
        }
      );



      router.refresh();
      if (response.success) {
        setIsTimerRunning(false);
        localStorage.removeItem("timerData");
        setElapsedTime(0);
        setIsModalOpen(false);
        setActualNumber(0);
        setError(null);
        form.reset();
      }
    }
  };

  const { form } = useDynamicForm(actualNumberSchema, {
    actual_number: "",
    notes: "",
  });

  return (
    <div className="w-full flex mb-2 justify-end">
      {isTimerRunning && (
        <div className="pr-2 text-center fixed top-0 right-2 z-[999] pt-1">
          <Button
            onClick={handleStopTime}
            className="text-xl bg-primary text-white"
            title="Ctrl + S to stop timer"
          >
            <FaStop className="text-red-500" />
            <span className="font-Orbitron"> {formatTime(elapsedTime)} </span>
          </Button>
          {/* {!isPaused ? ( */}
          <Button
            // Click={() => handlePause(c.client_id)}
            onClick={() => handlePause(reportId)}
            className="text-xl ml-2 bg-yellow-400 text-white"
            title="Ctrl + P to pause timer"
          >
            <FaPause />
          </Button>
        </div>
      )}
      {/* 
      {!isTimerRunning && (
        <form>
          <Select
            onValueChange={(value) => {
              const selected = client.find((c) => c.client_id === value);
              if (selected) setSelectedClientName(selected.client_name);
              setSelectedClient(value);
            }}
          >
            <SelectTrigger className="w-40">
              <SelectValue placeholder={"Select a Client"}>
                {selectedClientName || "Select a client"}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Clients</SelectLabel>
                {client.map((c) => (
                  <SelectItem key={c.client_id} value={c.client_id}>
                    {c.client_name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </form>
      )} */}

      <>
        {isModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg bg-">
             
                <button onClick={handleCloseModal} className="text-gray-700 font-serif  w-full flex justify-end">
                <IoMdClose className="text-3xl" />
              </button>
            
              <h3 className="text-lg font-semibold">Enter Actual Number</h3>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(
                    (values) => onSubmit(values),
                    (errors) => console.error("Validation Errors:", errors)
                  )}
                >
                  <FormInput
                    ref={inputRef}
                    type="number"
                    label="Actual Number"
                    form={form}
                    name="actual_number"
                    className="w-full bg-gray-50 rounded-md"
                    isRequired
                  />
                  <FormInput
                    type="text"
                    label="Notes"
                    form={form}
                    name="notes"
                    className="w-full  rounded-md  mt-2"
                  />
                  <SubmitBtn
                    className="px-6  w-full bg-primary/80 hover:bg-primary/90 text-white font-medium rounded-md transition-colors p-2 mt-1 h-[38px]"
                    text="Submit"
                  />
                </form>
              </Form>

              {/* <input
                ref={inputRef}
                type="number"
                value={actualNumber || ""}
                onChange={(e) => {
                  const value = e.target.value;
                  setActualNumber(
                    value === "" || !isNaN(Number(value))
                      ? Number(value)
                      : actualNumber
                  );
                }}
                onKeyDown={(e) => {
                  // e.key === "Enter" && handleModalSubmit();
                  if (
                    e.key === "Enter" &&
                    !isNaN(actualNumber) &&
                    actualNumber !== 0
                  ) {
                    handleModalSubmit();
                  }
                }}
                className="border p-2 mt-2"
                placeholder="Enter actual number"
              /> */}
              {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
              {/* <div className="mt-4 flex justify-end">
                <Button
                  onClick={handleModalSubmit}
                  className="bg-primary text-white"
                >
                  Submit
                </Button>
                <Button
                  onClick={() => setIsModalOpen(false)}
                  className="ml-2 bg-white text-black hover:bg-gray-500 hover:text-white"
                >
                  Cancel
                </Button>
              </div> */}
            </div>
          </div>
        )}
      </>
    </div>
  );
}
