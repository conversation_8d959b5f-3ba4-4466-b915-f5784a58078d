import React from "react";
import AddRoles from "./AddRoles";
import { getAllData, getCookie } from "@/lib/helpers";
import { employee_routes, rolespermission_routes } from "@/lib/routePath";
import ViewRoles from "./ViewRoles";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { PermissionWrapper } from "@/lib/permissionWrapper";

const page = async () => {
  const data = await getAllData(rolespermission_routes.GETALL_ROLES);
  const getAllPermission = await getAllData(
    rolespermission_routes.GETALL_PERMISSION
  );
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];
    //  (userPermissions)
    

  const corporationCookie = await getCookie("corporationtoken");
  //  (corporationCookie)
  

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  return (
    <>
      <>
        <div className="w-full p-2 pl-4">
          <div className="h-9 flex items-center ">
            <AdminNavBar link={"/pms/manage-roles"} name={"Manage Roles"} />
          </div>
          <div className="space-y-2">
            <h1 className="text-2xl">Manage Roles</h1>
            <p className="text-sm text-gray-700">
              Here You Can Manage The Roles
            </p>
          </div>
          <div className="w-full">
            <div className="flex justify-end">
              <PermissionWrapper
                permissions={permissions}
                requiredPermissions={["create-role"]}
              >
                <AddRoles getAllPermission={getAllPermission}  />
              </PermissionWrapper>
            </div>
            <div className="w-full py-4 animate-in fade-in duration-1000 ">
              <PermissionWrapper
                permissions={permissions}
                requiredPermissions={["view-role"]}
              >
                <ViewRoles
                  data={data}
                  permissions={permissions}
                  getAllPermission={getAllPermission}
                />
              </PermissionWrapper>
            </div>
          </div>
        </div>
      </>
    </>
  );
};

export default page;
