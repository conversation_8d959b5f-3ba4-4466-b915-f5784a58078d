import { updateItem } from "../../../utils/operation";


export const updateTrackSheets = async (req, res) => {
    const id = req.params.id;
    const { corporationID } = req;
    const fields = {
        clientId:req.body.clientId,
        company: req.body.company,
        division: req.body.division,
        invoice: Number(req.body.invoice),
        masterInvoice: Number(req.body.masterInvoice),
        bol: Number(req.body.bol),
        invoiceDate: new Date(req.body.invoiceDate),
        receivedDate: new Date(req.body.receivedDate),
        shipmentDate: new Date(req.body.shipmentDate),
        carrierId: Number(req.body.carrierId),
        invoiceStatus: req.body.invoiceStatus,
        currency: req.body.currency,
        qtyShipped: Number(req.body.qtyShipped),
        weightUnitName: req.body.weightUnitName,
        invoiceTotal: Number(req.body.invoiceTotal),
        savings: req.body.savings,
        ftpFileName: req.body.ftpFileName,
        ftpPage: req.body.ftp_page,  
        notes: req.body.notes,
    };

    await updateItem({
        model: "TrackSheets",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "TrackSheet updated successfully",
    });
};
