import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import {
  Sheet,
  <PERSON>etClose,
  SheetContent,
  SheetDescription,
  <PERSON>etHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet";
import { carrier_routes } from "@/lib/routePath";
import { useRouter } from "next/navigation";
import { useState } from "react";

export function SheetDemoCarrier() {
  const [openTab, setOpenTab] = useState(false);
  const [errors, setErrors] = useState<any[]>([]); // Error state can be an array of strings
  const router = useRouter();

  const handleExcelUpload = async (e: any) => {
    const file = e.target.files[0];

    if (!file) {
      toast.error("Please select an Excel file to upload");
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await fetch(carrier_routes.UPLOAD_CARRIER, {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        const responseErrors = Array.isArray(data.errors) ? data.errors : [];
        setErrors(responseErrors);

        if (data.successCount > 0) {
          toast.success(`${data.successCount} new carrier(s) added successfully!`);
        }

        if (responseErrors.length === 0 && data.successCount > 0) {
          setOpenTab(false);
          router.refresh();
        } 
      }
    else {
      const data = await response.json();
      toast.error(data.message || 'Failed to upload Excel file');
    }
    } catch (error) {
      console.error("Error importing Excel:", error);
      toast.error("Error importing Excel file. Please try again.");
    }
    router.refresh();
  };

  return (
    <Sheet open={openTab} onOpenChange={setOpenTab}>
      <SheetTrigger asChild>
        <Button className="mr-2" onClick={() => setOpenTab(true)}>
          Import
        </Button>
      </SheetTrigger>
      <SheetContent className="overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Import Carrier</SheetTitle>
          <SheetDescription></SheetDescription>
        </SheetHeader>

        {/* File Input */}
        <input
          type="file"
          name="file"
          accept=".xlsx,.xls"
          id="excel-upload"
          className="w-96 mb-2"
          hidden
          onChange={handleExcelUpload} // Correctly bound the event
        />
    
        <Button onClick={() => document.getElementById("excel-upload")?.click()}>
          Select Excel File
        </Button>
        <div className="text-red-500 text-sm mt-2">
          {Array.isArray(errors) && errors.map((error, index) => (
            <p key={index} className="mt-2 bg-red-200 rounded-md p-2">
              {error}
            </p>
          )) }
         
          </div>
        

      
      </SheetContent>
    </Sheet>
  );
}
