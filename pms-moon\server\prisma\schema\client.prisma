  model Client {
    id             Int             @id @default(autoincrement())
    corporation_id Int?
    client_name    String          @unique @db.VarChar()
    ownership_id   Int?
    ownership      User?           @relation("OwnershipClients", fields: [ownership_id], references: [id])
    owner_name     String?         @db.VarChar()
    country        String?         @db.VarChar()
    branch_id      Int?
    branch         Branch?         @relation(fields: [branch_id], references: [id], onDelete: Cascade)
    corporation    Corporation?    @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade)
    associateId    Int?
    associate      Associate?      @relation(fields: [associateId], references: [id], onDelete: Cascade)
    created_at     DateTime        @default(now()) @db.Timestamptz(6)
    updated_at     DateTime        @updatedAt @db.Timestamptz()
    WorkReport     WorkReport[]
    ClientCarrier  ClientCarrier[]
    DailyPlanning  DailyPlanning[]
    userClients    UserClients[]
    // customFields removed - now managed through ClientCustomFieldArrangement

    TrackSheets TrackSheets[]

    ClientCustomFieldArrangement ClientCustomFieldArrangement[]
}

model ClientCarrier {
  id             Int         @id @default(autoincrement())
  corporation_id Int
  corporation    Corporation @relation(fields: [corporation_id], references: [corporation_id])
  client_id      Int?
  client         Client?     @relation(fields: [client_id], references: [id], onDelete: Cascade)
  carrier_id     Int?
  carrier        Carrier?    @relation(fields: [carrier_id], references: [id], onDelete: Cascade)
  created_at     DateTime    @default(now())
  updated_at     DateTime    @default(now()) @updatedAt
  payment_terms  String?     @db.VarChar()
}