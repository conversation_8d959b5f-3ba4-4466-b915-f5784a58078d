import { handleError } from "../../../utils/helpers";
import xlsx from "xlsx";

export const exportClientService = async (req, res) => {
  try {
    const { Associate, ClientName, Ownership, Branch } = req.query;

    const searchConditions: any[] = [];

    if (Associate) {
      searchConditions.push({
        associate: {
          name: {
            contains: Associate,
            mode: "insensitive",
          },
        },
      });
    }

    if (ClientName) {
      searchConditions.push({
        client_name: {
          contains: ClientName,
          mode: "insensitive",
        },
      });
    }

    if (Ownership) {
      searchConditions.push({
          user: {
          username: {
            contains: Ownership,
            mode: "insensitive",
          },
        },
      });
    }

    if (Branch) {
      searchConditions.push({
        branch: {
          branch_name: {
            contains: Branch,
            mode: "insensitive",
          },
        },
      });
    }

    const whereClause = {
      AND: [],
    };

    if (searchConditions.length > 0) {
      whereClause.AND.push({
        AND: searchConditions,
      });
    }

    const data = await prisma.client.findMany({
      where: whereClause,
      select: {
        id: true,
        client_name: true,
        ownership_id: true,
        ownership: true,
       
        // user: true,
        // users: {
        //   where: {
        //    level: 3,
        //   },
        // },
        branch_id: true,
        branch: true,
        corporation_id: true,
        associateId: true,
        associate: true,
      },
      orderBy: { id: "desc" },
    });

    const datalength = await prisma.client.count({
      where: whereClause,
    });

    const formattedData = data.map((item) => ({
      
      Client_name: item.client_name || "N/A",
      Associate: item.associate?.name || "N/A",
      Ownership: item.ownership?.username || "N/A",
      Branch: item.branch?.branch_name || "N/A",
      // 'corporation_id':item.corporation_id || "N/A",
    }));

    const ws = xlsx.utils.json_to_sheet(formattedData);
    const wb = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(wb, ws, "client");
    const excelBuffer = xlsx.write(wb, { bookType: "xlsx", type: "buffer" });

    if (data.length > 0) {
      res.setHeader("Content-Disposition", "attachment; filename=client.xlsx");
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      return res.send(excelBuffer);
    }

    return res.status(200).json({ data: [], datalength });
  } catch (error) {
    return handleError(res, error);
  }
};
