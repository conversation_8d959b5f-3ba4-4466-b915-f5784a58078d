"use client";
import React, { useEffect, useState } from "react";
import { AddTask } from "./AddTask";
import { SelectClient } from "./SelectClient";
import { Car } from "lucide-react";
import ViewWorkRecord from "./ViewWorkRecord";
import {
  carrier_routes,
  client_routes,
  employee_routes,
  workreport_routes,
  worktype_routes,
} from "@/lib/routePath";
import { getAllData, getCookie } from "@/lib/helpers";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import Sidebar from "@/components/sidebar/Sidebar";
import { NavBar } from "@/app/_component/NavBar";
import Parent from "./parent";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useSession } from "@/lib/useSession";

const Manage = ({
  WorkData,
  workTypes,
  allClient,
  permissions,
  userData,
  actions,
  params,
  allUser
}: any) => {
  const [isLogoutConfirmationOpen, setIsLogoutConfirmationOpen] =
    useState(false);
  const { checkSessionToken, isSessionValid } = useSession(userData);

  useEffect(() => {
    const validateSession = async () => {
      const sessionValid = await checkSessionToken();

      if (!sessionValid || !isSessionValid) {
        setIsLogoutConfirmationOpen(true);
      }
    };

    validateSession();
  }, []);

  return (
    <>
      {isLogoutConfirmationOpen && (
        <Dialog open={isLogoutConfirmationOpen}>
          <DialogContent>
            <div className="text-center">
              <p className="mb-4">
                You are already logged in on another device.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
      <SidebarProvider>
        <Sidebar {...{ permissions }} profile={userData} />
        {/* <NavBar /> */}
        {/* <main className="pt-1">
          <SidebarTrigger />
        </main> */}

        <Parent
          userData={userData}
          permissions={actions}
          WorkData={WorkData}
          workTypes={workTypes}
          allClient={allClient}
          params={params}
          allUser={allUser}
        />
      </SidebarProvider>
    </>
  );
};

export default Manage;
