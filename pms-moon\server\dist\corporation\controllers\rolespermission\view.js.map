{"version": 3, "file": "view.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/rolespermission/view.ts"], "names": [], "mappings": ";;;AAAA,iCAKgB;AAEhB,uCAAoC;AAEpC,oDAAqD;AACrD,4DAAiE;AAE1D,MAAM,cAAc,GAAG,KAAK,EAAE,GAAG,EAAE,GAAa,EAAE,EAAE;IACzD,IAAI,CAAC;QACH,MAAM,cAAc,GAAG,MAAO,MAAc,CAAC,WAAW,CAAC,QAAQ,CAAC;YAChE,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,IAAI;aACb;YACD,OAAO,EAAC;gBACN,EAAE,EAAC,MAAM;aACV;SACF,CAAC,CAAC;QAEH,MAAM,kBAAkB,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YAC7D,MAAM,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC7B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBACd,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;YAChB,CAAC;YACD,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpB,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;IAC3C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,sBAAsB;SACjD,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AA7BW,QAAA,cAAc,kBA6BzB;AAEK,MAAM,SAAS,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC1C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,IAAA,uBAAgB,EAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAE1D,0DAA0D;QAC1D,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACxC,KAAK,EAAE;gBACL,cAAc,EAAE,GAAG,CAAC,cAAc,EAAE,gDAAgD;aACrF;YACD,OAAO,EAAE;gBACP,UAAU,EAAE,KAAK,EAAE,6CAA6C;aACjE;YACD,OAAO,EAAE;gBACP,WAAW,EAAE,IAAI,EAAE,uCAAuC;gBAC1D,WAAW,EAAE,IAAI,EAAE,uCAAuC;gBAC1D,eAAe,EAAE;oBACf,MAAM,EAAE;wBACN,UAAU,EAAE;4BACV,MAAM,EAAE;gCACN,MAAM,EAAE,IAAI;gCACZ,MAAM,EAAE,IAAI;gCACZ,EAAE,EAAE,IAAI;6BACT;yBACF;qBACF;iBACF,EAAE,0CAA0C;gBAC7C,IAAI,EAAE,IAAI,EAAE,gCAAgC;aAC7C;SACF,CAAC,CAAC;QAEH,wCAAwC;QACxC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gBAAgB;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,4CAA4C;QAC5C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,sBAAsB;SACjD,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AA/CW,QAAA,SAAS,aA+CpB;AAEK,MAAM,oBAAoB,GAAG,KAAK,EACvC,GAAG,EACH,GAAa,EACC,EAAE;IAChB,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,IAAA,oBAAa,EAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAC5D,MAAM,KAAK,GAAG,MAAM,IAAA,iBAAO,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC7C,MAAM,aAAa,GAAG,MAAM,IAAA,iCAAmB,EAAC;YAC9C,GAAG;YACH,GAAG,EAAE,GAAe;YACpB,MAAM,EAAE,iBAAiB;YACzB,cAAc,EAAE,aAAa;SAC9B,CAAC,CAAC;QACH,IAAI,aAAa,EAAE,CAAC;YAClB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,gBAAgB;iBAC1B,CAAC,CAAC;YACL,CAAC;YACD,qBAAqB;YACrB,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,GAAG,CACvC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;gBACvB,MAAM,IAAI,GAAG,MAAM,IAAA,8BAAuB,EAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBACzD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;oBACrC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM;oBAC9B,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa;iBAC7C,CAAC,CAAC,CAAC;gBACJ,gDAAgD;gBAChD,sDAAsD;gBACtD,aAAa;gBACb,oCAAoC;gBACpC,OAAO;gBACP,cAAc;gBACd,yBAAyB;gBACzB,OAAO;gBACP,MAAM;gBACN,OAAO;oBACL,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE,IAAI,CAAC,OAAO;oBACrB,UAAU;oBACV,6BAA6B;oBAC7B,gBAAgB;iBACjB,CAAC;YACJ,CAAC,CAAC,CACH,CAAC;YACF,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,sBAAsB;SACjD,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAvDW,QAAA,oBAAoB,wBAuD/B;AAEK,MAAM,oBAAoB,GAAG,KAAK,EAAE,GAAG,EAAE,GAAa,EAAE,EAAE;IAC/D,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,IAAA,4BAAqB,EAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC/D,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACtD,MAAM,IAAI,GACR,eAAe,EAAE,IAAI,EAAE,eAAe,CAAC,GAAG,CACxC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAC7C,IAAI,EAAE,CAAC;QACV,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAbW,QAAA,oBAAoB,wBAa/B;AAEF,MAAM,OAAO,GAAG,KAAK,EACnB,KAAa,EACb,MAAW,EACX,WAAyC,EACzC,EAAE;IACF,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACnD,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAC5D,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC"}