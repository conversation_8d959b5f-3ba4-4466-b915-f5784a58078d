{"version": 3, "file": "create.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/uploadfile/create.ts"], "names": [], "mappings": ";;;;;;AAKA,oCAoBC;AAxBD,gDAAwB;AACxB,4CAAoB;AACpB,2CAAmD;AAEnD,SAAgB,YAAY,CAAC,IAAY;IACvC,MAAM,MAAM,GAAa,EAAE,CAAC;IAC5B,IAAI,YAAY,GAAG,EAAE,CAAC;IACtB,IAAI,YAAY,GAAG,KAAK,CAAC;IAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACrC,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QAErB,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;YACjB,YAAY,GAAG,CAAC,YAAY,CAAC;QAC/B,CAAC;aAAM,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;YACjC,YAAY,GAAG,EAAE,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,YAAY,IAAI,IAAI,CAAC;QACvB,CAAC;IACH,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,wCAAwC;AAEjC,MAAM,QAAQ,GAAG,CAAC,OAAe,EAAe,EAAE;IACvD,IAAI,CAAC,OAAO;QAAE,OAAO,IAAI,CAAC;IAE1B,IAAI,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;IAErB,kCAAkC;IAClC,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1B,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC;gBACtB,oBAAoB;gBACpB,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,SAAS,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,oBAAoB;gBACpB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC;YACjC,CAAC;QACH,CAAC;IACH,CAAC;IACD,kCAAkC;SAC7B,IAAI,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/B,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEjD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC;gBACtB,oBAAoB;gBACpB,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC;YACjC,CAAC;iBAAM,CAAC;gBACN,oBAAoB;gBACpB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC;YACjC,CAAC;QACH,CAAC;IACH,CAAC;IAED,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG;QAAE,OAAO,IAAI,CAAC;IAEzC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;IAClD,OAAO,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC;AACzD,CAAC,CAAC;AAtCW,QAAA,QAAQ,YAsCnB;AAEK,MAAM,aAAa,GAAG,CAAC,KAAe,EAAqB,EAAE;IAClE,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;IACzB,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;QAC3B,MAAM,IAAI,GAAG,IAAA,gBAAQ,EAAC,OAAO,CAAC,CAAC;QAE/B,OAAO,IAAI;YACT,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YACxE,CAAC,CAAC,IAAI,CAAC;IACX,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AATW,QAAA,aAAa,iBASxB;AAEK,MAAM,UAAU,GAAG,KAAK,EAC7B,UAAe,EACf,GAAY,EACZ,GAAa,EACb,EAAE;IACF,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAE1B,IAAI,CAAC;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;YAChE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;SAC1B,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;YAClD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;gBACd,SAAS,EAAE,oBAAoB,CAAC,SAAS;aAC1C;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,OAAO,EAAE;wBACP,aAAa,EAAE,IAAI;qBACpB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,GAAG,YAAE,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAElD,2CAA2C;QAC3C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAC/C,CAAC;QAED,6CAA6C;QAC7C,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACrC,yBAAyB;QAEzB,MAAM,eAAe,GAAG,CAAC,cAAc,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;QAC3E,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAC3C,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAChC,CAAC;QAEF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wCAAwC,cAAc,CAAC,IAAI,CAChE,IAAI,CACL,EAAE;aACJ,CAAC,CAAC;QACL,CAAC;QAED,MAAM,aAAa,GAAG;YACpB,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC;YACxC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC;YACzC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC;SACtC,CAAC;QAEF,MAAM,OAAO,GAET,EAAE,CAAC;QAEP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACpC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YAElC,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC;YACtD,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC;YAE1D,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,cAAc;gBAAE,SAAS;YAErD,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAC1C,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI;gBACrD,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;YACjC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACtD,CAAC;QAED,MAAM,cAAc,GAGhB;YACF,KAAK,EAAE;gBACL,IAAI,EAAE,0BAAiB,CAAC,kBAAkB;gBAC1C,OAAO,EAAE,aAAa;aACvB;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,0BAAiB,CAAC,WAAW;gBACnC,OAAO,EAAE,MAAM;aAChB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,0BAAiB,CAAC,aAAa;gBACrC,OAAO,EAAE,eAAe;aACzB;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,0BAAiB,CAAC,OAAO;gBAC/B,OAAO,EAAE,SAAS;aACnB;YACD,KAAK,EAAE;gBACL,IAAI,EAAE,0BAAiB,CAAC,KAAK;gBAC7B,OAAO,EAAE,OAAO;aACjB;SACF,CAAC;QAEF,MAAM,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;YAC3C,KAAK,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,qBAAqB,EAAE;SACxE,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1D,KAAK,MAAM,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClE,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;gBACvC,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,IAAG,MAAM,KAAK,QAAQ;wBAAE,KAAK,CAAC,IAAI,CAAC,sBAAsB,MAAM,iBAAiB,OAAO,EAAE,CAAC,CAAC;oBAC3F,wEAAwE;oBACxE,SAAS;gBACX,CAAC;gBAED,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;gBAElC,IAAI,GAAG,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC;oBACnD,KAAK,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;iBAC/C,CAAC,CAAC;gBAEH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACT,GAAG,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;wBAC5C,IAAI,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;qBAC9C,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;oBAClD,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;oBACxB,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;iBACrB,CAAC,CAAC;gBAEH,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAC1D,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,KAAK,YAAY,EAAE,EAAE,CAC/C,CAAC;gBAEF,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,KAAK,CAAC,IAAI,CAAC,sBAAsB,OAAO,EAAE,CAAC,CAAC;oBAC5C,iDAAiD;oBACjD,SAAS;gBACX,CAAC;gBAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACzB,KAAK,CAAC,IAAI,CAAC,6BAA6B,OAAO,EAAE,CAAC,CAAC;oBACnD,iDAAiD;oBACjD,SAAS;gBACX,CAAC;gBAED,MAAM,IAAI,GAAG,IAAA,qBAAa,EAAC,KAAK,CAAC,CAAC;gBAElC,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;oBACvC,IAAI,EAAE;wBACJ,UAAU,EAAE,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;wBACnC,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC;wBAC7B,sBAAsB,EAAE,GAAG,CAAC,EAAE;wBAC9B,CAAC,OAAO,CAAC,EAAE,KAAK,IAAI,IAAI;wBACxB,GAAG,EAAE,IAAI;wBACT,IAAI;wBACJ,MAAM,EAAE,qBAAqB;qBAC9B;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,6BAA6B;YACtC,KAAK;SACN,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,uBAAuB;SAC/B,CAAC,CAAC;IACL,CAAC;YAAS,CAAC;QACT,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC;AACH,CAAC,CAAC;AA9LW,QAAA,UAAU,cA8LrB;AAEK,MAAM,SAAS,GAAG,KAAK,EAC5B,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,CAAC;QACrE,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,MAAM,gBAAgB,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QACvC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;QACxE,MAAM,IAAA,kBAAU,EAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAlBW,QAAA,SAAS,aAkBpB"}