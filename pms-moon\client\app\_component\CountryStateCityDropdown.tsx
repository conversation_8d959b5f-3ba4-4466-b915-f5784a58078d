//! not in use
import React from "react";
import useS<PERSON> from "swr";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import SelectComp from "./SelectComp";
interface CountryStateCityDropdownProps {
  selectState: string;
  selectcity: string;
  setSelectedState: React.Dispatch<React.SetStateAction<string>>;
  setSelectedCity: React.Dispatch<React.SetStateAction<string>>;
  location: { id: string; name: string }[];
  form: any;
}

const CountryStateCityDropdown = ({
  selectState,
  selectcity,
  setSelectedCity,
  setSelectedState,
  location,
  form,
}: CountryStateCityDropdownProps) => {
  const fetcher = (...args: [any]) =>
    fetch(...args, { credentials: "include" }).then((res) => res.json());
  const { data: state, error } = useSWR<any, Error>(
    selectState
      ? `https://api.techlogixit.com/api/location/state/${selectState}`
      : null,
    fetcher
  );

  const cityFetcher = (...args: [any]) =>
    fetch(...args).then((res) => res.json());
  const { data: city, error: cityError } = useSWR<any, Error>(
    selectcity
      ? `https://api.techlogixit.com/api/location/cities/${selectcity}`
      : null,
    cityFetcher
  );
  return (
    <>
      <FormField
        control={form.control}
        name="country"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              Country<span className="text-main-color">*</span>
            </FormLabel>
            <Select
              onValueChange={(value) => {
                setSelectedState(value.split(",")[1]);
                return field.onChange(value);
              }}
              defaultValue={field.value}
            >
              <FormControl>
                <SelectTrigger className="border border-none bg-gray-200 dark:border-gray-700 focus:!outline-rose-400 dark:bg-gray-700">
                  <SelectValue placeholder="Select Country" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {location.map((item: any) => {
                  return (
                    <SelectItem value={`${item.name},${item.id}`} key={item.id}>
                      {item.name}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="state"
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              State<span className="text-main-color">*</span>
            </FormLabel>
            <Select
              onValueChange={(value) => {
                setSelectedCity(value.split(",")[1]);
                return field.onChange(value);
              }}
              defaultValue={field.value}
            >
              <FormControl>
                <SelectTrigger className="border border-none bg-gray-200 dark:border-gray-700 focus:!outline-main-color dark:bg-gray-700">
                  <SelectValue placeholder="Select a state" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {state &&
                  state.map((item: any) => {
                    return (
                      <SelectItem
                        value={`${item.name},${item.id}`}
                        key={item.id}
                      >
                        {item.name}
                      </SelectItem>
                    );
                  })}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      <SelectComp
        form={form}
        label={"City"}
        name={"city"}
        placeholder={"Select City"}
        key={"city"}
        isRequired={true}
      >
        {city &&
          city?.map((item: any) => {
            return (
              <SelectItem value={item.name} key={item.id}>
                {item.name}
              </SelectItem>
            );
          })}
      </SelectComp>
    </>
  );
};

export default CountryStateCityDropdown;
