"use client";
import { IoBulbOutline } from "react-icons/io5";
import { FaRegChartBar } from "react-icons/fa";

const Images = () => {
  return (
    <div className="w-full flex flex-col justify-center relative">
      <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-slate-800 relative">
        <span className="bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
          Oi
        </span>
        <span className="text-slate-700">360</span>
      </h1>

      <div className="rounded-2xl p-6 md:p-8 lg:p-10 ">
        <h2 className="text-2xl md:text-2xl font-bold text-slate-800 mb-4 flex items-center">
          <span className="bg-blue-50 text-blue-600 p-1.5 rounded-lg mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="lucide lucide-activity"
            >
              <path d="M22 12h-4l-3 9L9 3l-3 9H2" />
            </svg>
          </span>
          Ops Insight 360
        </h2>

        <p className="text-slate-600 mb-8 leading-relaxed ml-1">
          Oi360 is an integrated operations intelligence platform designed to
          provide a comprehensive, real-time view of an organization's
          operational data. Unlock actionable insights and drive strategic
          decision-making with our intuitive solution.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
          <div className="bg-gradient-to-br from-blue-50 to-slate-50 p-5 rounded-xl border border-blue-100 shadow-sm">
            <div className="text-blue-600 mb-3 bg-white p-2.5 rounded-lg inline-block shadow-sm">
              <FaRegChartBar className="h-6 w-6" />
            </div>
            <h3 className="font-semibold text-slate-800 text-lg">
              Real-time Analytics
            </h3>
            <p className="text-slate-500 mt-2">
              Monitor your operations with live data updates and comprehensive
              dashboards
            </p>
          </div>

          <div className="bg-gradient-to-br from-amber-50 to-slate-50 p-5 rounded-xl border border-amber-100 shadow-sm">
            <div className="text-amber-500 mb-3 bg-white p-2.5 rounded-lg inline-block shadow-sm">
              <IoBulbOutline className="h-6 w-6" />
            </div>
            <h3 className="font-semibold text-slate-800 text-lg">
              Actionable Insights
            </h3>
            <p className="text-slate-500 mt-2">
              Turn data into strategic decisions
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Images;
