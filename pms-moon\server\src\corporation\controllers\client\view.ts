import { handleError } from "../../../utils/helpers";

export const viewClient = async (req, res) => {
  try {
    const { page, pageSize, associate, ClientName, Ownership, Branch,minimal } =
      req.query;

    const take = Number(pageSize);
    const skip = (Number(page) - 1) * Number(pageSize);

    const searchConditions: any[] = [];

    if (associate) {
      const associateList = associate.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: associateList.map((associate) => ({
          associate: {
            contains: associate,
            mode: "insensitive",
          },
        })),
      });
    }
    if (ClientName) {
      const clientList = ClientName.split(",").map((item) => item.trim());

      searchConditions.push({
        OR: clientList.map((clientName) => ({
          client_name: {
            contains: clientName,
            mode: "insensitive",
          },
        })),
      });
    }

    // if (Client) {
    //   const clientList = Client.split(',').map(item => item.trim());

    //   searchConditions.push({
    //     OR: clientList.map(clientName => ({
    //       client: {
    //         client_name: {
    //           contains: clientName,
    //           mode: "insensitive",
    //         },
    //       },
    //     })),
    //   });
    // }
    if (Ownership) {
      const ownershipList = Ownership.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: ownershipList.map((ownership) => ({
          user: {
            username: {
              contains: ownership,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    if (Branch) {
      const branchList = Branch.split(",").map((item) => item.trim());
      searchConditions.push({
        OR: branchList.map((branch) => ({
          branch: {
            branch_name: {
              contains: branch,
              mode: "insensitive",
            },
          },
        })),
      });
    }

    const whereClause: { AND?: any[] } = { AND: [] };

    if (searchConditions.length > 0) {
      whereClause.AND.push({
        AND: searchConditions,
      });
    }

        const select =
      minimal === "true"
        ? {
            id: true,
            client_name: true,
            associateId: true,
          }
        : {
            id: true,
            client_name: true,
            ownership: true,
            ownership_id: true,
            branch_id: true,
            branch: true,
            corporation_id: true,
            associate: true,
            associateId: true,
          };

    const data = await prisma.client.findMany({
      where: whereClause,
      take: page ? take : undefined,
      skip: page ? skip : undefined,
      select,
      orderBy: { id: "desc" },
    });

    const datalength = await prisma.client.count({
      where: whereClause,
    });

    if (data) {
      return res.status(200).json({ data, datalength });
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};
