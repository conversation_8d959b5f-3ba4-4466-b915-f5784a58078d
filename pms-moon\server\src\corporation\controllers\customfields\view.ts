import { Request, Response } from "express";
import { PrismaClient, FieldType } from "@prisma/client";

const prisma = new PrismaClient();

export interface AuthenticatedRequest extends Request {
  user?: { username: string };
}

// --- GET: Fetch All Custom Fields ---
export const getCustomFields = async (_req: Request, res: Response) => {
  try {
    const fields = await prisma.customField.findMany({
      orderBy: { createdAt: "desc" },
      include: {
        //autoOption: true, // if you want to return associated AUTO options
      },
    });

    res.status(200).json(fields);
  } catch (error) {
    console.error("Error fetching custom fields:", error);
    res.status(500).json({ error: "Server error" });
  }
};