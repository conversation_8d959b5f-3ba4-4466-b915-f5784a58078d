import { FancySelect } from "@/app/_component/FancySelect";
import { useEffect, useState } from "react";
import { clientCustomFields_routes, trackSheets_routes } from "@/lib/routePath";
import { getAllData } from "@/lib/helpers";
import { useSearchParams } from "next/navigation";
import ExportTrackSheet from "./ExportTrackSheet";
import ViewTrackSheet from "./ViewTrackSheet";

const ClientSelectPage = ({ permissions, client }) => {
  const [customFieldsMap, setCustomFieldsMap] = useState<any>({});
  const [selectedClients, setSelectedClients] = useState<any[]>([]);
  const [trackSheetData, setTrackSheetData] = useState<any>({
    data: [],
    datalength: 0,
  });
  const searchParams = useSearchParams();
  const pageSize = parseInt(searchParams.get("pageSize")) || 50;
  const totalPages = Math.ceil((trackSheetData?.datalength || 0) / pageSize);

  const params = new URLSearchParams(searchParams);

  const clientOptions =
    client?.map((c: any) => ({
      value: c.id?.toString(),
      label: c.client_name,
    })) || [];

  const mapCustomFields = (data: any[]) =>
    data.map((row) => ({
      ...row,
      customFields: (row.TrackSheetCustomFieldMapping || []).reduce(
        (acc: any, mapping: any) => {
          acc[mapping.customFieldId] = mapping.value;
          return acc;
        },
        {}
      ),
    }));

  const handleClientChange = (newSelectedClients: any[]) => {
    setSelectedClients(newSelectedClients);
  };

  useEffect(() => {
    const fetchData = async () => {
      if (selectedClients.length > 0) {
        try {
          const response = await getAllData(
            `${clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS}/${selectedClients[0]?.value}`
          );
          const fieldsMap: any = {};
          (response.custom_fields || []).forEach((field: any) => {
            fieldsMap[field.id] = { name: field.name, type: field.type };
          });
          setCustomFieldsMap(fieldsMap);

          if (!params.get("page")) params.set("page", "1");
          if (!params.get("pageSize")) params.set("pageSize", "50");

          const url = `${trackSheets_routes.GETALL_TRACK_SHEETS}/${
            selectedClients[0]?.value
          }?${params.toString()}`;

          const trackSheetResponse = await getAllData(url);
          setTrackSheetData({
            ...trackSheetResponse,
            data: mapCustomFields(trackSheetResponse.data || []),
          });
        } catch (error) {
          setCustomFieldsMap({});
          setTrackSheetData({ data: [], datalength: 0 });
        }
      } else {
        setCustomFieldsMap({});
        setTrackSheetData({ data: [], datalength: 0 });
      }
    };

    fetchData();
  }, [selectedClients, searchParams]);

  return (
    <div>
      <div className="flex justify-between items-center gap-4">
        <div className="flex-1 min-w-0">
          <FancySelect
            frameworks={clientOptions}
            selected={selectedClients}
            setSelected={handleClientChange}
            label="Select Client"
            placeholder="Search clients..."
            className="max-w-xs"
          />
        </div>
        {selectedClients.length > 0 && (
          <ExportTrackSheet filteredTrackSheetData={trackSheetData?.data} customFieldsMap={customFieldsMap} />
        )}
      </div>
      <div className="w-full animate-in fade-in duration-500 rounded-2xl shadow-sm  dark:bg-gray-800 p-3">
        <ViewTrackSheet
          permissions={permissions}
          totalPages={totalPages}
          client={client}
          customFieldsMap={customFieldsMap}
          selectedClients={selectedClients}
          trackSheetData={trackSheetData}
          pageSize={pageSize}
        />
      </div>
    </div>
  );
};

export default ClientSelectPage;
