{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "prisma": {"seed": "node prisma/seed.js", "schema": "./prisma/schema"}, "scripts": {"dev": "nodemon dist/server.js", "build": "tsc", "watch": "tsc --watch", "start": "node dist/index.js", "seed": "node prisma/seed.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.1.0", "are-we-there-yet": "^4.0.2", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "canvas": "^3.1.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^3.5.0", "csv-parser": "^3.1.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "luxon": "^3.5.0", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "multer": "^1.4.5-lts.1", "nanoid": "^5.0.9", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "nodemon": "^3.1.9", "prisma": "^6.1.0", "react-datetime": "^3.3.1", "socket.io": "^4.8.1", "table": "^6.9.0", "ts-node": "^10.9.2", "winston": "^3.17.0", "xlsx": "^0.18.5"}, "devDependencies": {"@prisma/internals": "^6.5.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/node": "^22.10.5", "@types/node-cron": "^3.0.11", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}