"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateClientCarrier = void 0;
const operation_1 = require("../../../utils/operation");
const updateClientCarrier = async (req, res) => {
    const id = req.params.id;
    const { corporation_id } = req;
    const fields = {
        carrier_id: Number(req.body.carrier_id),
        client_id: Number(req.body.client_id),
        corporation_id: corporation_id,
        payment_terms: req.body.payment_terms
    };
    await (0, operation_1.updateItem)({
        model: "clientCarrier",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "Client Carrier setup has been updated",
    });
};
exports.updateClientCarrier = updateClientCarrier;
//# sourceMappingURL=update.js.map