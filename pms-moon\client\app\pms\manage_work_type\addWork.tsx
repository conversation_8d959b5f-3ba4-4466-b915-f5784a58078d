"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import useDynamicForm from "@/lib/useDynamicForm";
import { addWorkTypeSchema } from "@/lib/zodSchema";
import React, { useState } from "react";
import { toast } from "sonner";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { SelectItem } from "@/components/ui/select";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaPlus } from "react-icons/fa";
import DialogHeading from "@/app/_component/DialogHeading";
import { Form } from "@/components/ui/form";
import { formSubmit } from "@/lib/helpers";
import { worktype_routes } from "@/lib/routePath";
import FormRadio from "@/app/_component/FormRadio";
import { useRouter } from "next/navigation";
import TriggerButton from "@/app/_component/TriggerButton";
function AddWork({ allCategory }: any) {
  const router = useRouter();
  const { form } = useDynamicForm(addWorkTypeSchema, {
    work_type: "",
    category: "AUDIT" as "AUDIT" | "ENTRY" | "REPORT",
    does_it_require_planning_number: "",
    is_work_carrier_specific: "",
    is_backlog_regular_required: "",
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  

  
  async function onSubmit(values: any) {
    try {
      const formData = {
        work_type: values.work_type,
        category: values.category,
        is_work_carrier_specific:
          values.is_work_carrier_specific === "true" ? true : false,
        does_it_require_planning_number:
          values.does_it_require_planning_number === "true" ? true : false,
        is_backlog_regular_required:
          values.is_backlog_regular_required === "true" ? true : false,
      };
      const data = await formSubmit(
        worktype_routes.CREATE_WORKTYPE,
        "POST",
        formData
      );

      if (data.success) {
        router.refresh();

        toast.success(data.message);
        setIsDialogOpen(false);
        form.reset()
      } else {
        toast.error(
          data.error || "An error occurred while adding the work."
        );
      }
    } catch (error) {
      toast.error("An error occurred while adding the work.");
      console.error(error);
    }
  }

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger>
          <TriggerButton type="add" text="work type" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[40rem] min-w-[30rem] ">
          <DialogHeading
            title="Add work type"
            description="Please enter your work details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid grid-cols-2 gap-5 items-center">
                <FormInput
                  form={form}
                  label="Work Type"
                  placeholder="Enter Work Type"
                  name="work_type"
                  type="text"
                  isRequired
                />

                <SelectComp
                  form={form}
                  label="Category"
                  placeholder="Select Category"
                  name="category"
                  isRequired

               
                >
                  {allCategory?.map((item: any) => (
                    <SelectItem
                      key={item.id}
                      value={item.id.toString()}
                    >
                      {item.category_name}
                    </SelectItem>
                  ))}
                </SelectComp>
              </div>
              <div className="grid grid-cols-2 gap-5 ">
                <FormRadio
                  className="ml-1"
                  form={form}
                  name="is_work_carrier_specific"
                  label="Is carrier required ?"
                  options={[
                    { value: "true", label: "YES" },
                    { value: "false", label: "NO" },
                  ]}
                />
                <FormRadio
                  className="ml-1"
                  form={form}
                  name="does_it_require_planning_number"
                  label="Actual No ?"
                  options={[
                    { value: "true", label: "YES" },
                    { value: "false", label: "NO" },
                  ]}
                />
                <FormRadio
                  className="ml-1"
                  form={form}
                  name="is_backlog_regular_required"
                  label="Is Backlog/Regular required ?"
                  options={[
                    { value: "true", label: "YES" },
                    { value: "false", label: "NO" },
                  ]}
                />
              </div>

              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Submit"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default AddWork;
