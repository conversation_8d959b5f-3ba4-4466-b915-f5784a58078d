{"version": 3, "file": "rolesPermissionRoutes.js", "sourceRoot": "", "sources": ["../../../../src/corporation/routes/rolesPermission/rolesPermissionRoutes.ts"], "names": [], "mappings": ";;AAAA,qCAAiC;AACjC,uEAAkE;AAClE,qEAAgF;AAChF,iEAIgD;AAChD,qEAAiF;AACjF,qEAAiF;AACjF,yEAAgF;AAEhF,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AAExB,MAAM,CAAC,IAAI,CACT,YAAY,EACZ,6BAAY,EACZ,IAAA,2CAAyB,EAAC,iBAAiB,EAAE,aAAa,CAAC,EAC3D,6BAAoB,CACrB,CAAC;AACF,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,gBAAS,CAAC,CAAC;AACxC,MAAM,CAAC,GAAG,CAAC,sBAAsB,EAAE,qBAAc,CAAC,CAAC;AACnD,MAAM,CAAC,GAAG,CAAC,2BAA2B,EAAE,2BAAoB,CAAC,CAAC;AAC9D,MAAM,CAAC,GAAG,CAAC,mBAAmB,EAAE,6BAAY,EAAE,8BAAqB,CAAC,CAAC;AACrE,MAAM,CAAC,MAAM,CACX,mBAAmB,EACnB,6BAAY,EACZ,IAAA,2CAAyB,EAAC,iBAAiB,EAAE,aAAa,CAAC,EAC3D,8BAAqB,CACtB,CAAC;AACF,sEAAsE;AAEtE,kBAAe,MAAM,CAAC"}