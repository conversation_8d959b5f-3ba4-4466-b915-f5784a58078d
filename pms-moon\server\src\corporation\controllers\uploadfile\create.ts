import { Request, Response, NextFunction } from "express";
import path from "path";
import fs from "fs";
import { DailyPlanningType } from "@prisma/client";

export function parseCSVLine(line: string): string[] {
  const values: string[] = [];
  let currentValue = "";
  let insideQuotes = false;

  for (let i = 0; i < line.length; i++) {
    const char = line[i];

    if (char === '"') {
      insideQuotes = !insideQuotes;
    } else if (char === "," && !insideQuotes) {
      values.push(currentValue.trim());
      currentValue = "";
    } else {
      currentValue += char;
    }
  }

  values.push(currentValue.trim());
  return values;
}

// Hotfix: Handle different date formats

export const parseYMD = (dateStr: string): Date | null => {
  if (!dateStr) return null;

  let year, month, day;

  // Handle YYYY-MM-DD or DD-MM-YYYY
  if (dateStr.includes("-")) {
    const dateParts = dateStr.split("-").map(Number);

    if (dateParts.length === 3) {
      if (dateParts[0] > 31) {
        // Assume YYYY-MM-DD
        [year, month, day] = dateParts;
      } else {
        // Assume DD-MM-YYYY
        [day, month, year] = dateParts;
      }
    }
  }
  // Handle MM/DD/YYYY or DD/MM/YYYY
  else if (dateStr.includes("/")) {
    const dateParts = dateStr.split("/").map(Number);

    if (dateParts.length === 3) {
      if (dateParts[2] > 31) {
        // Assume MM/DD/YYYY
        [month, day, year] = dateParts;
      } else {
        // Assume DD/MM/YYYY
        [day, month, year] = dateParts;
      }
    }
  }

  if (!year || !month || !day) return null;

  const parsedDate = new Date(year, month - 1, day);
  return isNaN(parsedDate.getTime()) ? null : parsedDate;
};

export const calculateAges = (dates: string[]): (number | null)[] => {
  const today = new Date();
  return dates.map((dateStr) => {
    const date = parseYMD(dateStr);

    return date
      ? Math.floor((today.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
      : null;
  });
};

export const processCSV = async (
  dataBuffer: any,
  req: Request,
  res: Response
) => {
  const { id } = req.params;

  try {
    if (!dataBuffer) {
      return res.status(400).send("No file data found.");
    }

    const dailyPlanningDetails = await prisma.dailyPlanning.findFirst({
      where: { id: Number(id) },
    });

    const client = await prisma.dailyPlanning.findFirst({
      where: {
        id: Number(id),
        client_id: dailyPlanningDetails.client_id,
      },
      include: {
        client: {
          include: {
            ClientCarrier: true,
          },
        },
      },
    });

    const text = fs.readFileSync(dataBuffer, "utf-8");

    // Handle different line endings (CRLF, LF)
    const lines = text.split("\n");

    if (lines.length === 0) {
      return res.status(400).send("CSV is empty.");
    }

    // Parse headers considering potential quotes
    const headers = lines[0].split("\t");
    //  (headers, "headers");

    const requiredColumns = ["CARRIER NAME", "INVOICE STATUS", "INVOICE DATE"];
    const missingColumns = requiredColumns.filter(
      (col) => !headers.includes(col)
    );

    if (missingColumns.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Invalid CSV format. Missing columns: ${missingColumns.join(
          ", "
        )}`,
      });
    }

    const columnIndexes = {
      carrier: headers.indexOf("CARRIER NAME"),
      status: headers.indexOf("INVOICE STATUS"),
      date: headers.indexOf("INVOICE DATE"),
    };

    const newData: {
      [key: string]: { [key: string]: { count: number; dates: string[] } };
    } = {};

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split("\t");
      if (values.length === 0) continue;

      const carrier = values[columnIndexes.carrier]?.trim();
      const status = values[columnIndexes.status]?.trim();
      const invoiceDateStr = values[columnIndexes.date]?.trim();

      if (!carrier || !status || !invoiceDateStr) continue;

      newData[carrier] = newData[carrier] || {};
      newData[carrier][status] = newData[carrier][status] || {
        count: 0,
        dates: [],
      };

      newData[carrier][status].count++;
      newData[carrier][status].dates.push(invoiceDateStr);
    }

    const statusMappings: Record<
      string,
      { type: DailyPlanningType; dataKey: string }
    > = {
      Error: {
        type: DailyPlanningType.BATCH_ERROR_STATUS,
        dataKey: "batch_error",
      },
      "Hold/Exception": {
        type: DailyPlanningType.HOLD_STATUS,
        dataKey: "hold",
      },
      "In Review": {
        type: DailyPlanningType.REVIEW_STATUS,
        dataKey: "review_status",
      },
      Correct: {
        type: DailyPlanningType.CORRECT,
        dataKey: "correct",
      },
      Entry: {
        type: DailyPlanningType.ENTRY,
        dataKey: "entry",
      },
    };

    await prisma.dailyPlanningDetails.deleteMany({
      where: { daily_planning_id: Number(id), source: "uploadViaDataReport" },
    });

    const error = [];
    for (const [carrier, statuses] of Object.entries(newData)) {
      for (const [status, { count, dates }] of Object.entries(statuses)) {
        const mapping = statusMappings[status];
        if (!mapping) {
          if(status !== "Posted") error.push(`Unexpected status: ${status} for carrier: ${carrier}`);
          // console.warn(`Unexpected status: ${status} for carrier: ${carrier}`);
          continue;
        }

        const { type, dataKey } = mapping;

        let Dpt = await prisma.dailyPlanningByType.findFirst({
          where: { daily_planning_id: Number(id), type },
        });

        if (!Dpt) {
          Dpt = await prisma.dailyPlanningByType.create({
            data: { daily_planning_id: Number(id), type },
          });
        }

        const carrier_name = await prisma.carrier.findFirst({
          where: { name: carrier },
          select: { id: true },
        });

        const cheackClientCarrier = client.client.ClientCarrier.some(
          (item) => item.carrier_id === carrier_name?.id
        );

        if (!carrier_name) {
          error.push(`Carrier not found: ${carrier}`);
          // console.warn(`Carrier not found: ${carrier}`);
          continue;
        }

        if (!cheackClientCarrier) {
          error.push(`Client carrier not found: ${carrier}`);
          // console.warn(`Carrier not found: ${carrier}`);
          continue;
        }

        const ages = calculateAges(dates);

        await prisma.dailyPlanningDetails.create({
          data: {
            carrier_id: Number(carrier_name.id),
            daily_planning_id: Number(id),
            daily_planning_type_id: Dpt.id,
            [dataKey]: count || null,
            age: ages,
            type,
            source: "uploadViaDataReport",
          },
        });
      }
    }

    return res.status(200).json({
      success: true,
      message: "CSV processed successfully.",
      error,
    });
  } catch (error) {
    console.error("Error processing file:", error);
    return res.status(500).json({
      success: false,
      error: "Error processing CSV.",
    });
  } finally {
    fs.unlinkSync(dataBuffer);
  }
};

export const uploadCSV = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  if (!req.file) {
    res.status(400).json({ success: false, error: "No file uploaded." });
    return;
  }

  try {
    const relativeFilePath = req.file.path;
    const filePath = path.join(__dirname, "../../../../", relativeFilePath);
    await processCSV(filePath, req, res);
  } catch (error) {
    console.error("Error processing file:", error);
    next(error);
  }
};
