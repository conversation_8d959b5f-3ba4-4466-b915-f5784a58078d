
import { Response } from "express";
import { deleteTransaction } from "./utility";
import { checkUserPermission } from "../../../utils/permissions";

export const deleteRolesPermission = async (req, res: Response) => {
  const role_id = req.params.id;
  const hasPermission = await checkUserPermission({
    req: req,
    res: res as Response,
    action: "USER MANAGEMENT",
    permissiontype: "ROLE MANAGEMENT",
  });
  if (hasPermission) {
    await deleteTransaction({
      model: "Roles",
      fieldName: "id",
      id: Number(role_id),
      res,
      successMessage: "Role have been deleted",
      
    });
  }
};
