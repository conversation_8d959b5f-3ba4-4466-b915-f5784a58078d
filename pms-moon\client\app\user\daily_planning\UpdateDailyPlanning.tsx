"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit, getAllData } from "@/lib/helpers";
import {
  carrier_routes,
  client_routes,
  daily_planning,
  location_api,
} from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { AddDailyPlanningSchema, createCarrierSchema } from "@/lib/zodSchema";
import React, { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import TriggerButton from "@/app/_component/TriggerButton";
import { useRouter } from "next/navigation";
import { Client } from "@/app/pms/manage_client/column";

const UpdateDailyPlanning = ({ data, allClient, onSuccess }: any) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const router = useRouter();

  const { form } = useDynamicForm(AddDailyPlanningSchema, {
    daily_planning_date:
      new Date(data?.daily_planning_date).toISOString().split("T")[0] || "",
    client_id: data?.client_id.toString() || "",
  });

  const onSubmit = async (values: any) => {
    try {
      const submittedValues = {
        ...values,
        daily_planning_date: new Date(values.daily_planning_date),
        client_id: parseInt(values.client_id),
      };

      const formData = await formSubmit(
        `${daily_planning.UPDATE_DAILY_PLANNING}/${data.id}`,
        "PUT",
        submittedValues
      );

      if (formData.success) {
        router.refresh();
        setIsDialogOpen(false);
        toast.success(formData.message);
        onSuccess?.();
      } else {
        toast.error(
          formData.message || "An error occurred while adding the carrier."
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
    }
  };
  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger onClick={() => setIsDialogOpen(true)}>
          <TriggerButton type="edit" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Update Daily Planning"
            description="Please enter daily planning details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 gap-5">
                <FormInput
                  label="Date"
                  form={form}
                  name="daily_planning_date"
                  type="date"
                  className="w-full bg-gray-50 rounded-md"
                />
                <SelectComp
                  form={form}
                  label="Client"
                  name="client_id"
                  placeholder="Select Work Type"
                  isRequired
                  className="w-full  rounded-md"
                >
                  {allClient &&
                    allClient.map((clien: any) => (
                      <SelectItem
                        value={clien?.id?.toString()}
                        key={clien.id}
                      >
                        {clien.client_name}
                      </SelectItem>
                    ))}
                </SelectComp>
              </div>

              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Update"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateDailyPlanning;
