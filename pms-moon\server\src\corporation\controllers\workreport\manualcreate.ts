import { createItem } from "../../../utils/operation";
import { WorkStatus } from "./helper";

export const createWorkreportManually = async (req, res) => {
  const userId = req.user_id;

  const {
    date,
    client_id,
    carrier_id,
    work_type_id,
    category,
    actualNumber,
    start_time,
    end_time,
    task_type,
    notes,
  } = req.body;

  

  // Calculate duration in minutes
  const startTime = new Date(start_time);
  const endTime = new Date(end_time);

  const duration = ((endTime as any) - (startTime as any)) / (1000 * 60); // Convert ms to minutes


  const fields = {
    date: new Date(date),
    user_id: userId,
    work_status: WorkStatus.FINISHED,
    start_time: startTime,
    finish_time: endTime,
    time_spent: duration,
    notes: notes,
  };



  await createItem({
    model: "workReport",
    fieldName: "id",
    fields: fields,
    res: res as Response,
    req: req,
    successMessage: "workReport has been created",
  });
};
