import { error, log } from "console";
import { createItem } from "../../../utils/operation";
import xlsx from "xlsx";
import fs from "fs";

export const createCarrier = async (req, res) => {
  const { corporation_id } = req;
  const fields = {
    name: req.body.name,
    carrier_code: req.body.carrier_code,
    carrier_2nd_name: req.body.carrier_2nd_name,
    corporation_id: Number(corporation_id),
  };
  await createItem({
    model: "carrier",
    fieldName: "id",
    fields: fields,
    res: res,
    req: req,
    successMessage: "carrier has been created",
  });
};

export const excelCarrier = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    const allowedExtensions = [".xlsx", ".xls"];
    const fileExtension = req.file.originalname.split(".").pop();
    if (!allowedExtensions.includes(`.${fileExtension}`)) {
      return res
        .status(400)
        .json({ error: "Invalid file type. Only Excel files are allowed." });
    }

    const workbook = xlsx.readFile(req.file.path);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const import_data: any = xlsx.utils.sheet_to_json(worksheet, { header: 1 });

    const header = ["VAP Id", "Carrier Name", "Carrier Name 2"];
    const errors = [];

    if (
      !import_data[0].includes(header[0]) ||
      !import_data[0].includes(header[1]) ||
      !import_data[0].includes(header[2])
    ) {
      errors.push(
        "Invalid file format. Please ensure the file has the correct headers."
      );
      return res
        .status(200)
        .json({
          message:
            "Invalid file format. Please ensure the file has the correct headers.",
          errors,
        });
    }

    const userData = await Promise.all(
      import_data.slice(1).map(async (row: any, index) => {
        const carrier_code = row[0];
        const name = row[1];
        const carrier_2nd_name = row[2];
        const corporationId = row[3];

        const corporation = await prisma.corporation.findFirst({
          where: {
            corporation_id: corporationId,
          },
          select: { corporation_id: true },
        });

        // Skip rows where corporation is not found
        if (!corporation) {
          return { error: `Invalid corporationId at row ${index + 2}` };
        }

        const existingCarrier = await prisma.carrier.findFirst({
          where: {
            OR: [{ carrier_code: String(carrier_code) }, { name: name }],
          },
        });
        if (existingCarrier) {
          if (
            existingCarrier.carrier_code === carrier_code &&
            existingCarrier.name === name
          ) {
            return {
              error: `Carrier Name ${name} and VAP Id ${carrier_code}  already exists.`,
            };
          } else if (existingCarrier.carrier_code === carrier_code) {
            return { error: `VAP Id ${carrier_code} already exists.` };
          } else if (existingCarrier.name === name) {
            return { error: `Carrier Name ${name} already exists.` };
          }
        }

        return {
          corporation_id: Number(corporation.corporation_id),
          carrier_code: String(carrier_code),
          name: name,
          carrier_2nd_name: carrier_2nd_name,
        };
      })
    );

    // const validCarrier = userData.filter((carrier: any) => !carrier.error && carrier.carrier_code && carrier.name && carrier.carrier_2nd_name);

    // if (validCarrier.length === 0) {
    //   errors.push("No valid carrier data found. Ensure all required fields are filled correctly.");
    //   return res.status(200).json({ message: "No valid data to insert", errors });
    // }

    // // Check for existing carriers with the same carrier_code
    // const existingCarriers = await prisma.carrier.findMany({
    //   where: {
    //     carrier_code: {
    //       in: validCarrier.map((carrier: any) => carrier.carrier_code),
    //     },
    //   },
    // });

    // // Filter out existing carriers
    // const newCarriers = validCarrier.filter((carrier: any) => !existingCarriers.some((existingCarrier: any) => existingCarrier.carrier_code === carrier.carrier_code));

    // if (newCarriers.length === 0) {
    //   errors.push("All carrier data already exists. No new records were inserted.");
    //   return res.status(200).json({ message: "No new carrier data to insert", errors });
    // }

    const validCarrier = userData.filter(
      (carrier: any) =>
        !carrier.error &&
        carrier.carrier_code &&
        carrier.name &&
        carrier.carrier_2nd_name
    );
    const errorMessages = userData
      .filter((carrier: any) => carrier.error)
      .map((carrier: any) => carrier.error);
    // Insert the new carriers
    await prisma.carrier.createMany({
      data: validCarrier,
    });

    res.status(200).json({
      message: "Carrier data imported successfully",
      errors: errorMessages,
      successCount: validCarrier.length,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: "Internal Server Error" });
  } finally {
    fs.unlinkSync(req.file.path);
  }
};
