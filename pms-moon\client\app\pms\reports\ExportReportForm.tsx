"use client";

import React from "react";
import { Form } from "@/components/ui/form";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import { SelectItem } from "@/components/ui/select";
import useDynamicForm from "@/lib/useDynamicForm";
import { z } from "zod";
import SubmitBtn from "@/app/_component/SubmitBtn";

const exportReportSchema = z.object({
  username: z.string().optional(),
  category: z.string().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
});

interface ExportReportFormProps {
  categories: Array<{ id: string; category_name: string }>;
  users: Array<{ id: string; username: string }>;
  onExport: (values: any) => void;
}

const ExportReportForm: React.FC<ExportReportFormProps> = ({
  categories,
  users,
  onExport,
}) => {
  const { form } = useDynamicForm(exportReportSchema, {
    username: "",
    category: "",
    fromDate: "",
    toDate: "",
  });

  const handleSubmit = async (values: any) => {
    const queryParams = new URLSearchParams();
    
    if (values.username) queryParams.append("Username", values.username);
    if (values.category) queryParams.append("Category", values.category);
    if (values.fromDate) queryParams.append("fDate", values.fromDate);
    if (values.toDate) queryParams.append("tDate", values.toDate);

    const queryString = queryParams.toString();
    const url = `/api/customizeReport/export-customize-report?${queryString}`;

    try {
      const response = await fetch(url);
      if (response.ok) {
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = 'Work_Report.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        console.error('Export failed');
      }
    } catch (error) {
      console.error('Export error:', error);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <SelectComp
              form={form}
              label="Username"
              name="username"
              placeholder="Select Username"
            >
              {users.map((user) => (
                <SelectItem key={user.id} value={user.username}>
                  {user.username}
                </SelectItem>
              ))}
            </SelectComp>

            <SelectComp
              form={form}
              label="Category"
              name="category"
              placeholder="Select Category"
            >
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.category_name}>
                  {category.category_name}
                </SelectItem>
              ))}
            </SelectComp>

            <FormInput
              form={form}
              label="From Date"
              name="fromDate"
              type="date"
              placeholder="Select From Date"
            />

            <FormInput
              form={form}
              label="To Date"
              name="toDate"
              type="date"
              placeholder="Select To Date"
            />
          </div>

          <SubmitBtn
            className="w-full bg-primary text-secondary hover:bg-primary/90"
            text="Export Report"
          />
        </form>
      </Form>
    </div>
  );
};

export default ExportReportForm; 