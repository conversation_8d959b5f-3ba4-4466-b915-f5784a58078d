"use client";

import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";

import { formSubmit, getAllData } from "@/lib/helpers";
import { carrier_routes, location_api, search_routes } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createCarrierSchema } from "@/lib/zodSchema";
import React, { useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaPlus } from "react-icons/fa";
import DialogHeading from "@/app/_component/DialogHeading";
import { useRouter } from "next/navigation";
import TriggerButton from "@/app/_component/TriggerButton";
import * as XLSX from "xlsx";
import { SheetDemoCarrier } from "./ImportCarrier";
import * as ExcelJS from "exceljs";

function AddCarrier({ data ,params}: any) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
  
  const router = useRouter();

  const exportParams = new URLSearchParams(params);

  exportParams.delete("pageSize");
  exportParams.delete("page");

  const { form } = useDynamicForm(createCarrierSchema, {
    name: "",
    carrier_2nd_name: "",
    carrier_code:"" ,
  });

  async function onSubmit(values: any) {
    try {
      const formData = {
        name: values.name.toUpperCase().trim(),
        carrier_2nd_name: values.carrier_2nd_name.toUpperCase().trim(),
        carrier_code: values.carrier_code.toUpperCase(),
      };

      
      const data = await formSubmit(
        carrier_routes.CREATE_CARRIER,
        "POST",
        formData
      );
    

      if (data.success) {
        toast.success(data.message);
        form.reset();
        router.refresh();
        setIsDialogOpen(false);
        form.reset()
      } else {
        toast.error(
          data.error || "An error occurred while adding the carrier."
        );
      }
    } catch (error) {
      toast.error("An error occurred while adding the carrier.");
      console.error(error);
    }
  }

  const handleExportCarrier = async () => {
    // try {
    //   const response = await fetch(`${carrier_routes.EXCEL_CARRIER}?${exportParams.toString()}`, {
    //     method: "GET",
    //   });
    //   if (response.ok) {
    //     const blob = await response.blob();
    //     if (!blob || blob.size === 0) {
    //       throw new Error("Error: Received an empty or invalid file.");
    //     }
    //     const link = document.createElement("a");
    //     link.href = URL.createObjectURL(blob);
    //     link.download = "carrier.xlsx";
    //     link.click();
    //     toast.success("Carriers Exported Successfully");
    //   } else {
    //     throw new Error(`Unexpected response: ${response.statusText}`);
    //   }
    // } catch (error) {
    //   console.error("Error exporting carriers:", error);
    //   toast.error(`Error exporting carriers: ${error.message}`);
    // }
    try {
      const apiUrl = `${search_routes.GET_SEARCH}/Carrier?${exportParams.toString()}`
      const allCarrier = await getAllData(apiUrl);

      if (
        allCarrier &&
        allCarrier.data &&
        Array.isArray(allCarrier.data) &&
        allCarrier.data.length > 0
      )
        {
          const carrierExcelData = allCarrier.data;
          const workbook = new ExcelJS.Workbook();
          const worksheet = workbook.addWorksheet("Carrier");

          // Add headers
          const headers = [
            "VAP Id",
            "Carrier Name",
            "Carrier Name 2",
          ];
          worksheet.addRow(headers);

          carrierExcelData.forEach((item) => {
            const formattedRow = [
              item.carrier_code || "N/A",
              item.name || "N/A",
              item.carrier_2nd_name || "N/A",
            ];
            worksheet.addRow(formattedRow);
          });

          const fileBuffer = await workbook.xlsx.writeBuffer();

          const blob = new Blob([fileBuffer], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          });

          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = "Carrier.xlsx";
          link.click();
          toast.success("Carriers Exported Successfully");
        } else {
          throw new Error("Error: No valid data found for export.");
        }
    } catch (error) {
      console.error("Error exporting carriers:", error);
      toast.error(`Error exporting carriers: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };
  

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <Button onClick={handleExportCarrier} className="mr-2">
          Export
        </Button>
        <SheetDemoCarrier />

        <DialogTrigger>
          <TriggerButton type="add" text="carrier" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Add Carrier"
            description="Please Enter Carrier Details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid grid-cols-2 gap-2">
                <FormInput
                  form={form}
                  label="Carrier Name"
                  name="name"
                  placeholder="Enter Carrier Name"
                  type="text"
                  isRequired
                />

                <FormInput
                  form={form}
                  label="VAP ID"
                  name="carrier_code"
                  placeholder="Enter VAP ID"
                  type="text"
                  isRequired
                />
                {/* <SelectComp
                  form={form}
                  label="Country"
                  name="country"
                  placeholder="Select Country"
                  isRequired
                >
                  {countries.map((item) => (
                    <SelectItem value={item.name} key={item.id}>
                      {item.name}
                    </SelectItem>
                  ))}
                </SelectComp> */}
                <FormInput
                  form={form}
                  label="Carrier Name - 2"
                  name="carrier_2nd_name"
                  placeholder="Enter Carrier Name - 2"
                  type="text"
                  isRequired
                />
              </div>

              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Submit"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default AddCarrier;
