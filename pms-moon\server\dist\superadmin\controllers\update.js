"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateSuperAdmin = void 0;
const operation_1 = require("../../utils/operation");
const updateSuperAdmin = async (req, res) => {
    const id = req.params.id;
    const fields = {
        username: req.body.username,
        email: req.body.email,
    };
    await (0, operation_1.updateItem)({
        model: "superAdmin",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "Super Admin has been updated",
    });
};
exports.updateSuperAdmin = updateSuperAdmin;
//# sourceMappingURL=update.js.map