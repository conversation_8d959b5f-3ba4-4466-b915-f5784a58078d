{"version": 3, "file": "createTwoTen.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/uploadfile/createTwoTen.ts"], "names": [], "mappings": ";;;;;;AACA,gDAAwB;AACxB,4CAAoB;AACpB,qCAAuD;AAEhD,MAAM,gBAAgB,GAAG,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC7D,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,IAAI,CAAC;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACrD,CAAC;QAED,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;YAChE,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;SAC1B,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC;YAClD,KAAK,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC;gBACd,SAAS,EAAE,oBAAoB,CAAC,SAAS;aAC1C;YACD,OAAO,EAAE;gBACP,MAAM,EAAE;oBACN,OAAO,EAAE;wBACP,aAAa,EAAE,IAAI;qBACpB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,GAAG,YAAE,CAAC,YAAY,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAErC,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAC9D,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAC3C,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAChC,CAAC;QAEF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,wCAAwC,cAAc,CAAC,IAAI,CAChE,IAAI,CACL,EAAE;aACJ,CAAC,CAAC;QACL,CAAC;QAED,MAAM,aAAa,GAAG;YACpB,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC;YACnC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;YACjC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC;SAC7C,CAAC;QAEF,MAAM,OAAO,GAET,EAAE,CAAC;QAEP,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACpC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAS;YAElC,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC;YACtD,MAAM,MAAM,GAAG,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC;YAEjE,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,cAAc;gBAAE,SAAS;YAErD,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAC1C,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI;gBACrD,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC;YACjC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACtD,CAAC;QAED,iCAAiC;QAEjC,MAAM,cAAc,GAAG;YACrB,OAAO,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,iBAAiB,EAAE;YAChE,cAAc,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,aAAa,EAAE;YAC/D,IAAI,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE;YACvD,mBAAmB,EAAE;gBACnB,IAAI,EAAE,2BAA2B;gBACjC,OAAO,EAAE,2BAA2B;aACrC;YACD,KAAK,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,eAAe,EAAE;YAC1D,cAAc,EAAE;gBACd,IAAI,EAAE,sBAAsB;gBAC5B,OAAO,EAAE,sBAAsB;aAChC;SACF,CAAC;QACF,MAAM,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC;YAC3C,KAAK,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,cAAc,EAAE;SACjE,CAAC,CAAC;QACH,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,KAAK,MAAM,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1D,KAAK,MAAM,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAClE,MAAM,OAAO,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC;gBACvC,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,SAAS;gBACX,CAAC;gBAED,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;gBAGlC,IAAI,GAAG,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC;oBACnD,KAAK,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;iBAC/C,CAAC,CAAC;gBAEH,IAAI,CAAC,GAAG,EAAE,CAAC;oBACT,GAAG,GAAG,MAAM,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;wBAC5C,IAAI,EAAE,EAAE,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE;qBAC9C,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;oBAClD,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;oBACxB,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;iBACrB,CAAC,CAAC;gBAEH,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,KAAK,CAAC,IAAI,CAAC,sBAAsB,OAAO,EAAE,CAAC,CAAC;oBAC5C,SAAS;gBACX,CAAC;gBAED,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAC1D,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,UAAU,KAAK,YAAY,EAAE,EAAE,CAC/C,CAAC;gBAEF,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACzB,KAAK,CAAC,IAAI,CAAC,6BAA6B,OAAO,EAAE,CAAC,CAAC;oBACnD,iDAAiD;oBACjD,SAAS;gBACX,CAAC;gBAED,MAAM,IAAI,GAAG,IAAA,sBAAa,EAAC,KAAK,CAAC,CAAC;gBAElC,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;oBACvC,IAAI,EAAE;wBACJ,UAAU,EAAE,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;wBACnC,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC;wBAC7B,sBAAsB,EAAE,GAAG,CAAC,EAAE;wBAC9B,CAAC,OAAO,CAAC,EAAE,KAAK,IAAI,IAAI;wBACxB,GAAG,EAAE,IAAI;wBACT,IAAI;wBACJ,MAAM,EAAE,cAAc;qBACvB;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,6BAA6B;YACtC,KAAK;SACN,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,uBAAuB;SAC/B,CAAC,CAAC;IACL,CAAC;YAAS,CAAC;QACT,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC;AACH,CAAC,CAAC;AAzKW,QAAA,gBAAgB,oBAyK3B;AACK,MAAM,eAAe,GAAG,KAAK,EAClC,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,CAAC;QACrE,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,MAAM,gBAAgB,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QACvC,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC;QACxE,MAAM,IAAA,wBAAgB,EAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAlBW,QAAA,eAAe,mBAkB1B"}