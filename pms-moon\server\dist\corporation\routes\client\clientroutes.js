"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/client/create");
const view_1 = require("../../controllers/client/view");
const update_1 = require("../../controllers/client/update");
const delete_1 = require("../../controllers/client/delete");
const authentication_1 = require("../../../middleware/authentication");
const checkPermission_1 = require("../../../middleware/checkPermission");
const multer_1 = __importDefault(require("multer"));
const exportClientService_1 = require("../../controllers/client/exportClientService");
const router = (0, express_1.Router)();
const DIR = "./src/public";
const storage = multer_1.default.diskStorage({
    destination: function (req, file, cb) {
        cb(null, DIR);
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + "-" + file.originalname);
    },
});
const upload = (0, multer_1.default)({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 },
}).single("file");
router.post("/excelClient", upload, create_1.excelClient); //client
router.post("/create-client", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CLIENT MANAGEMENT", "create-client"), create_1.createClient);
router.get("/get-all-client", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CLIENT MANAGEMENT", "view-client"), view_1.viewClient);
router.put("/update-client/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CLIENT MANAGEMENT", "update-client"), update_1.updateClient);
router.delete("/delete-client/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CLIENT MANAGEMENT", "delete-client"), delete_1.deleteClient);
router.get("/export-client", exportClientService_1.exportClientService);
exports.default = router;
//# sourceMappingURL=clientroutes.js.map