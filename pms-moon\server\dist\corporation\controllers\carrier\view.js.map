{"version": 3, "file": "view.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/carrier/view.ts"], "names": [], "mappings": ";;;AAAA,oDAAqD;AAE9C,MAAM,WAAW,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC5C,IAAI,CAAC;QACH,MAAM,EACJ,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,KAAK,EACL,YAAY,EACZ,oBAAoB,GACrB,GAAG,GAAG,CAAC,KAAK,CAAC;QAEd,MAAM,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEnD,MAAM,gBAAgB,GAAU,EAAE,CAAC;QAEnC,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAEtE,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;oBACpC,IAAI,EAAE;wBACJ,QAAQ,EAAE,WAAW;wBACrB,IAAI,EAAE,aAAa;qBACpB;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,yEAAyE;QAEzE,4BAA4B;QAC5B,4CAA4C;QAC5C,uBAAuB;QACvB,gCAAgC;QAChC,+BAA+B;QAC/B,WAAW;QACX,WAAW;QACX,QAAQ;QACR,IAAI;QAEJ,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAChE,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;oBAChC,YAAY,EAAE;wBACZ,QAAQ,EAAE,OAAO;wBACjB,IAAI,EAAE,aAAa;qBACpB;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QAED,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACvE,gBAAgB,CAAC,IAAI,CAAC;gBACpB,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;oBAChC,gBAAgB,EAAE;wBAChB,QAAQ,EAAE,OAAO;wBACjB,IAAI,EAAE,aAAa;qBACpB;iBACF,CAAC,CAAC;aACJ,CAAC,CAAC;QACL,CAAC;QACD,MAAM,WAAW,GAAG;YAClB,GAAG,EAAE,EAAE;SACR,CAAC;QAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC;gBACnB,GAAG,EAAE,gBAAgB;aACtB,CAAC,CAAC;QACL,CAAC;QAED,MAAM,MAAM,GACV,oBAAoB,KAAK,MAAM;YAC7B,CAAC,CAAC;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,IAAI;aACX;YACH,CAAC,CAAC;gBACE,EAAE,EAAE,IAAI;gBACR,IAAI,EAAE,IAAI;gBACV,YAAY,EAAE,IAAI;gBAClB,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE,IAAI;gBACb,gBAAgB,EAAE,IAAI;gBACtB,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI;gBAChB,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,IAAI;aACjB,CAAC;QAER,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;YACzC,KAAK,EAAE,WAAW;YAClB,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YAC7B,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;YAC7B,MAAM;YACN,OAAO,EAAE;gBACP,EAAE,EAAE,MAAM;aACX;SACF,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC5C,KAAK,EAAE,WAAW;SACnB,CAAC,CAAC;QAEH,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;QACpD,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AApHW,QAAA,WAAW,eAoHtB;AAEK,MAAM,mBAAmB,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACpD,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC/C,KAAK,EAAE;gBACL,SAAS,EAAE,MAAM,CAAC,EAAE,CAAC;aACtB;YACD,OAAO,EAAE;gBACP,OAAO,EAAE,IAAI;aACd;SACF,CAAC,CAAC;QACH,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QAED,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAnBW,QAAA,mBAAmB,uBAmB9B"}