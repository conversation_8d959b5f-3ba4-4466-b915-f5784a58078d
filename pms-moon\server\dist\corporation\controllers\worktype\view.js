"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewWorktype = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewWorktype = async (req, res) => {
    try {
        const data = await prisma.workType.findMany({
            select: {
                category: true,
                work_type: true,
                is_work_carrier_specific: true,
                does_it_require_planning_number: true,
                is_backlog_regular_required: true,
                id: true,
                category_id: true,
            },
            orderBy: {
                id: "desc",
            },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewWorktype = viewWorktype;
//# sourceMappingURL=view.js.map