"use client";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";
import { saveAs } from "file-saver";

const invoiceData = [
  {
    ID: 3904872,
    "INVOICE #": "BX0212047",
    "MASTER INVOICE #": "BX0212047",
    "BOL #": "1Z4X30216863516299",
    "PO #": "",
    "REF # 1": "",
    "REF # 2": "",
    "QA #": 600,
    "INVOICE DATE": "03/09/2024",
    "RECEIVED DATE": "04/09/2024",
    "POSTING DATE": "07/10/2024",
    "SHIPMENT DATE": "03/09/2024",
    "DELIVERY DATE": "",
    "INPUT DATE": "26/09/2024",
    "REVIEWED DATE": "04/10/2024",
    "ORCA WEEKLY REPORT DATE": "07/10/2024",
    "ORCA WEEKLY REPORT": "CARTERS WEEKLY PAYMENT REPORT OCT 07, 2024",
    "EFT-CHQ DATE": "",
    "ORCA RFF #": "41-30635",
    "VENDOR #": 102021,
    "CARRIER NAME": "DELMAR INTERNATIONAL INC",
    "INVOICE STATUS": "Posted",
    "PMT STATUS": "Ok To Pay",
    "MANUAL/EDI": "Manual",
    "INVOICE TYPE": "Freight",
    DIVISION: "CANADA",
    SHIPPER: "IDENTITY SYSTEMS, INC",
    "SH ADDRESS": "1324 STIMMEL RD",
    "SH CITY": "COLUMBUS",
    "SH STATE": "OH",
    "SH COUNTRY": "US",
    "SH ZIP": "43223",
    "CN BT #": "",
    "CN ST #": "DC46",
    CONSIGNEE: "THE GENUINE CANADIAN CORP",
    "CN ADDRESS": "225 PINEBUSH RD",
    "CN CITY": "CAMBRIDGE",
    "CN STATE": "ON",
    "CN COUNTRY": "CA",
    "CN ZIP": "N1T1B9",
    "PORT LOADING": "",
    "PORT DISCHARGE": "TL",
    MODE: "TL",
    "SHIP TYPE": "Customs",
    "EQUIPMENT TYPE": "Inbound",
    "TRANSPORT TYPE": "STANDARD",
    DIRECTION: "CAD",
    "SERVICE LEVEL": "1",
    CURRENCY: "KGS",
    "QTY SHIPPED": "",
    "REVIEW USER": "",
  },
  {
    ID: 3984681,
    "INVOICE #": "BX0233799",
    "MASTER INVOICE #": "BX0233799",
    "BOL #": "4X3021LHJTV",
    "PO #": "",
    "REF # 1": "",
    "REF # 2": "",
    "QA #": 600,
    "INVOICE DATE": "25/09/2024",
    "RECEIVED DATE": "26/09/2024",
    "POSTING DATE": "21/10/2024",
    "SHIPMENT DATE": "25/09/2024",
    "DELIVERY DATE": "",
    "INPUT DATE": "12/10/2024",
    "REVIEWED DATE": "16/10/2024",
    "ORCA WEEKLY REPORT DATE": "21/10/2024",
    "ORCA WEEKLY REPORT": "CARTERS WEEKLY PAYMENT REPORT OCT 21, 2024",
    "EFT-CHQ DATE": "",
    "ORCA RFF #": "43-30738",
    "VENDOR #": 102021,
    "CARRIER NAME": "DELMAR INTERNATIONAL INC",
    "INVOICE STATUS": "Posted",
    "PMT STATUS": "Ok To Pay",
    "MANUAL/EDI": "Manual",
    "INVOICE TYPE": "Freight",
    DIVISION: "CANADA",
    SHIPPER: "IDENTITY SYSTEMS, INC",
    "SH ADDRESS": "1324 STIMMEL RD",
    "SH CITY": "COLUMBUS",
    "SH STATE": "OH",
    "SH COUNTRY": "US",
    "SH ZIP": "43223",
    "CN BT #": "",
    "CN ST #": "DC46",
    CONSIGNEE: "THE GENUINE CANADIAN CORP",
    "CN ADDRESS": "225 PINEBUSH RD",
    "CN CITY": "CAMBRIDGE",
    "CN STATE": "ON",
    "CN COUNTRY": "CA",
    "CN ZIP": "N1T1B9",
    "PORT LOADING": "",
    "PORT DISCHARGE": "TL",
    MODE: "TL",
    "SHIP TYPE": "Customs",
    "EQUIPMENT TYPE": "Inbound",
    "TRANSPORT TYPE": "STANDARD",
    DIRECTION: "CAD",
    "SERVICE LEVEL": "1",
    CURRENCY: "KGS",
    "QTY SHIPPED": "",
    "REVIEW USER": "",
  },
];

const generateCSV = () => {
  const headers = Object.keys(invoiceData[0]);
  const csvRows = [];

  csvRows.push(headers.join(","));

  invoiceData.forEach((row: any) => {
    const values = headers.map((header) => row[header] || "");
    csvRows.push(values.join(","));
  });

  return csvRows.join("\n");
};

const SampleFile = () => {
  const handleDownload = () => {
    const csvContent = generateCSV();
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", "invoice_data.csv");
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <>
      <Button className="bg-primary text-secondary" onClick={handleDownload}>
        DOWNLOAD TEMPLATE
      </Button>
    </>
  );
};

export default SampleFile;
