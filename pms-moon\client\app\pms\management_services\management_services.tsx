"use client";

import React from "react";
import NavLink from "@/app/_component/NavLink";

const ManagementServices = ({
  managementRoutes,
}: {
  managementRoutes: any[];
}) => {
  console.log("Management Routes in Component:", managementRoutes); // Debugging management routes passed to the component

  return (
    <div className="p-4">
      <h1 className="text-2xl">Management Services</h1>
      <div className="space-y-2">
        {managementRoutes.length > 0 ? (
          managementRoutes.map((route) => (
            <NavLink
              key={route.path}
              path={route.path}
              label={route.label}
              icon={route.icon}
            />
          ))
        ) : (
          <p>No management services available for you.</p>
        )}
      </div>
    </div>
  );
};

export default ManagementServices;