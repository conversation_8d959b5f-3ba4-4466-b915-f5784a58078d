"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCarrier = void 0;
const operation_1 = require("../../../utils/operation");
const updateCarrier = async (req, res) => {
    const id = req.params.id;
    const { corporation_id } = req;
    const fields = {
        name: req.body.name,
        carrier_code: req.body.carrier_code,
        carrier_2nd_name: req.body.carrier_2nd_name,
        corporation_id: Number(corporation_id)
    };
    await (0, operation_1.updateItem)({
        model: "carrier",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "carrier has been updated",
    });
};
exports.updateCarrier = updateCarrier;
//# sourceMappingURL=update.js.map