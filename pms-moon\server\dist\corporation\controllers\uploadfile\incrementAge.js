"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.incrementAge = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const incrementAge = async () => {
    const dpd = await prisma.dailyPlanningDetails.findMany();
    for (const dp of dpd) {
        const updateAge = dp.age.map((age) => {
            if (age === null)
                return null;
            return age + 1;
        });
        await prisma.dailyPlanningDetails.update({
            where: { id: dp.id },
            data: { age: updateAge },
        });
    }
};
exports.incrementAge = incrementAge;
node_cron_1.default.schedule("0 0 * * *", async () => {
    try {
        await (0, exports.incrementAge)();
    }
    catch (error) {
        console.log("Error running incrementAge cron job:", error);
        throw error;
    }
}, { scheduled: true, timezone: "Asia/Kolkata" });
//# sourceMappingURL=incrementAge.js.map