import { Router } from "express";
import { createSuperAdmin } from "../controllers/create";
import { updateSuperAdmin } from "../controllers/update";
import { superAdminLogin } from "../../utils/login";
import { deleteSuperAdmin } from "../controllers/delete";
import { viewSuperAdmin } from "../controllers/view";
import { logoutSuperAdmin } from "../controllers/logout";
import { authenticateSuperAdmin } from "../../middleware/authentication";


const router = Router();

router.post("/create-superadmin", createSuperAdmin);

router.post("/login",superAdminLogin);

router.post("/logout", logoutSuperAdmin);

router.get("/get-all-superadmin",authenticateSuperAdmin, viewSuperAdmin);

router.post("/update-superadmin/:id",authenticateSuperAdmin, updateSuperAdmin);

router.delete("/delete-superadmin/:id",authenticateSuperAdmin, deleteSuperAdmin );

export default router;
