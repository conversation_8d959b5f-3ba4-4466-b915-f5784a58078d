import React from "react";
import CreateEmployee from "@/app/pms/manage_employee/createEmployee";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  branch_routes,
  client_routes,
  employee_routes,
  rolespermission_routes,
  usertitle_routes,
} from "@/lib/routePath";
import ViewEmployee from "./ViewEmployee";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { PermissionWrapper } from "@/lib/permissionWrapper";

const EmployeePage = async ({
  searchParams,
}: {
  searchParams: {
    pageSize?: string;
    page?: string;
    Username: string;
    "First Name"?: string;
    "Last Name"?: string;
    Email: string;
    Role: string;
    "Reporting To"?: string;
    Title: string;
  };
}) => {
  const {
    page = "1",
    pageSize = "50",
    Username,
    ["First Name"]: FirstName,
    ["Last Name"]: LastName,
    Email,
    Role,
    ["Reporting To"]: Manager,
    Title,
  } = searchParams;

  const params = new URLSearchParams();

  if (pageSize) params.append("pageSize", pageSize);
  if (page) params.append("page", page);
  if (Username) params.append("Username", Username);
  if (FirstName) params.append("FirstName", FirstName);
  if (LastName) params.append("LastName", LastName);
  if (Email) params.append("Email", Email);
  if (Role) params.append("Role", Role);
  if (Manager) params.append("Manager", Manager);
  if (Title) params.append("Title", Title);
  // params.append("includeRelations", "role,usertitle");

  const allUsers = await getAllData(employee_routes.GETALL_USERS);
  const apiUrl = `${employee_routes.GETALL_USERS}?${params?.toString()}`;
  const allEmployee = await getAllData(apiUrl);
  const alluserTitle = await getAllData(usertitle_routes.GETALL_USERTITLE);
  const allRoles = await getAllData(rolespermission_routes.GETALL_ROLES);
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const allBranch = await getAllData(branch_routes.GETALL_BRANCH);
  const allClient = await getAllData(client_routes.GETALL_CLIENT);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");

  // If corporationCookie doesn't exist, manually remove 'allow_all' from permissions
  // let permissions = userPermissions;
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;
  // Only add 'allow_all' if corporationCookie exists
  return (
    <>
      <div className="w-full p-2 pl-4">
        <div className="h-9 flex items-center">
          <AdminNavBar link={"/pms/manage_employee"} name={"Manage User"} />
        </div>
        <div className="space-y-2">
          <h1 className="text-2xl">Manage User</h1>
          <p className="text-sm text-gray-700">Here You Can Manage User</p>
        </div>
        <div className="w-full">
          <div className="flex justify-end">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["create-user"]}
            >
              <CreateEmployee
                allRoles={allRoles}
                data={allEmployee?.data}
                usertitle={alluserTitle}
                allUsers={allUsers?.data}
                allBranch={allBranch}
                allClient={allClient?.data}
              />
            </PermissionWrapper>
          </div>
          <div className="w-full py-4 animate-in fade-in duration-1000 ">
            <PermissionWrapper
              permissions={permissions}
              requiredPermissions={["view-user"]}
            >
              <ViewEmployee
                datas={allEmployee}
                data={allEmployee?.data}
                permissions={permissions}
                allRoles={allRoles}
                usertitle={alluserTitle}
                allEmployee={allEmployee}
                allUsers={allUsers?.data}
                allBranch={allBranch}
                allClient={allClient?.data}
              />
            </PermissionWrapper>
          </div>
        </div>
      </div>
    </>
  );
};

export default EmployeePage;
