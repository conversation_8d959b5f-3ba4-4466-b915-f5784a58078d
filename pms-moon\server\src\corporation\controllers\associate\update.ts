import { updateItem } from "../../../utils/operation";

export const updateAssociate = async (req, res) => {
  const id = req.params.id;
  const { corporation_id } = req;
  const fields = {
    name: req.body.name,
    corporation_id: Number(corporation_id),
  };
  await updateItem({
    model: "associate",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res: res,
    req: req,
    successMessage: "Associate has been updated",
  });
};