"use client";

import { TrendingUp } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>, CartesianGrid, XAxis, <PERSON>Axi<PERSON> } from "recharts";
import { useState, useEffect } from "react";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

const generateDummyData = (year) => {
  const csaNames = ["CSA 1", "CSA 2", "CSA 3", "CSA 4", "CSA 5"];
  
  // Use year as a seed for different but consistent data per year
  const seed = parseInt(year);
  
  return csaNames.map((csa, index) => ({
    name: csa,
    workDone: Math.floor((Math.sin(seed + index) + 1) * 75) + 50, // Generate different but consistent values for each year
    pendingWork: Math.floor((Math.cos(seed + index) + 1) * 50) + 20,
  }));
};

const chartConfig = {
  workDone: {
    label: "Work Done",
    color: "#BCBCBD",
  },
  pendingWork: {
    label: "Pending Work",
    color: "#7A7A7C",
  },
};

// Generate array of years from 2020 to current year
const years = Array.from(
  { length: new Date().getFullYear() - 2020 + 1 },
  (_, i) => (2020 + i).toString()
);

export function DashBoardChart() {
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [chartData, setChartData] = useState([]);

  useEffect(() => {
    setChartData(generateDummyData(selectedYear));
  }, [selectedYear]);

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Yearly Work Comparison</CardTitle>
            <CardDescription>
              Annual work done vs. pending work for different CSAs
            </CardDescription>
          </div>
          <Select value={selectedYear} onValueChange={setSelectedYear}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Select year" />
            </SelectTrigger>
            <SelectContent>
              {years.map((year) => (
                <SelectItem key={year} value={year}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart width={1000} height={500} data={chartData} barGap={8}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="name"
              scale="band"
              tickLine={false}
              tickMargin={10}
              axisLine={true}
            />
            <YAxis
              tickLine={false}
              tickMargin={10}
              axisLine={true}
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar
              dataKey="workDone"
              name="Work Done"
              fill={chartConfig.workDone.color}
              radius={[4, 4, 0, 0]}
            />
            <Bar
              dataKey="pendingWork"
              name="Pending Work"
              fill={chartConfig.pendingWork.color}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          {selectedYear} CSA Performance
        </div>
        <div className="leading-none text-muted-foreground">
          Total work done and pending work for each CSA in {selectedYear}
        </div>
      </CardFooter>
    </Card>
  );
}