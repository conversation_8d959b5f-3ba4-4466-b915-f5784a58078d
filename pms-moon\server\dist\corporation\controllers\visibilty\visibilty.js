"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserVisbilty = void 0;
const helpers_1 = require("../../../utils/helpers");
const visibiltyHelper_1 = require("./visibiltyHelper");
const getUserVisbilty = async (req, res) => {
    try {
        const table = req.params;
        const userId = req.user_id;
        const data = await (0, visibiltyHelper_1.getVisibility)(7, table.table); // Get depth value (number)
        return res.status(200).json(data);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getUserVisbilty = getUserVisbilty;
//# sourceMappingURL=visibilty.js.map