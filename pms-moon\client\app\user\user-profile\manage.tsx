"use client";
import React, { useEffect, useState } from "react";
import { Car } from "lucide-react";
import {
  carrier_routes,
  client_routes,
  employee_routes,
  workreport_routes,
  worktype_routes,
} from "@/lib/routePath";
import { getAllData, getCookie } from "@/lib/helpers";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import Sidebar from "@/components/sidebar/Sidebar";
import { NavBar } from "@/app/_component/NavBar";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import UserDetail from "./UserDetail";
import { useSession } from "@/lib/useSession";

const Manage = ({
  hrUser,
  currentUserWorkReport,
  resumedWorkPercentage,
  resumedWork,
  completedWork,
  permissions,
  userData,
  allEmployee,
  userlevel,
  userType,
}: any) => {
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

 const [isLogoutConfirmationOpen, setIsLogoutConfirmationOpen] = useState(false);
  const { checkSessionToken, isSessionValid } = useSession(userData);
  
  useEffect(() => {
    const validateSession = async () => {
      const sessionValid = await checkSessionToken();

      if (!sessionValid || !isSessionValid) {
        setIsLogoutConfirmationOpen(true);
      }
    };

    validateSession();
  }, []); 

  return (
    <>
      {isLogoutConfirmationOpen && (
        <Dialog open={isLogoutConfirmationOpen}>
          <DialogContent>
            <div className="text-center">
              <p className="mb-4">
                You are already logged in on another device.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
      <SidebarProvider>
        <div className="flex  w-full ">
          <Sidebar {...{ permissions }} profile={userData} />
          <div className="  w-full ">
            <div className="pl-4 p-2">
              <BreadCrumbs
                breadcrumblist={[
                  { link: "/user/user-profile", name: "User Profile" },
                ]}
              />
              {/* <UserNavBar link="/user/user-profile" name="User Profile" />{" "} */}
            </div>
            <UserDetail
            hrUser={hrUser}
              userData={userData}
              completedWork={completedWork}
              resumedWork={resumedWork}
              resumedWorkPercentage={resumedWorkPercentage}
              userType={userType}
              allEmployee={allEmployee}
              userlevel={userlevel}
            />
          </div>
        </div>
      </SidebarProvider>
    </>
  );
};

export default Manage;
