"use client";
import * as React from "react";
import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Command as CommandPrimitive } from "cmdk";
import { Label } from "@/components/ui/label";
import { FormMessage } from "@/components/ui/form";

type Framework = {
  value: string;
  label: string;
};

type FancyMultiSelectProps = {
  frameworks: Framework[];
  selected: Framework[];
  setSelected: React.Dispatch<React.SetStateAction<Framework[]>>;
  label?: string;
  isAllSelected?: boolean;
  isCorporation?: boolean;
  adminPlaceholder?: string;
  disabled?: boolean;
  showSelectAll?: boolean;
  onSelectAll?: (checked: boolean) => void;
  placeholder?: string;
  errorMessage?: string;
};

export function FancyMultiSelects({
  frameworks,
  selected,
  setSelected,
  label,
  isAllSelected = false,
  isCorporation = false,
  adminPlaceholder = "",
  disabled = false,
  showSelectAll = false,
  onSelectAll,
  placeholder = "Search...",
  errorMessage,
}: FancyMultiSelectProps) {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");

  const handleUnselect = React.useCallback(
    (framework: Framework) => {
      setSelected((prev) => prev.filter((s) => s.value !== framework.value));
    },
    [setSelected]
  );

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      const input = inputRef.current;
      if (input) {
        if (e.key === "Delete" || e.key === "Backspace") {
          if (input.value === "") {
            setSelected((prev) => {
              const newSelected = [...prev];
              newSelected.pop();
              return newSelected;
            });
          }
        }
        if (e.key === "Escape") {
          input.blur();
        }
      }
    },
    [setSelected]
  );

  const selectables = frameworks.filter(
    (framework) => !selected.some((s) => s.value === framework.value)
  );

  return (
    <div className="space-y-1">
      <div className="flex justify-between items-center">
        <Label className="md:text-base text-gray-800">{label}</Label>
        {showSelectAll && (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id={`selectAll-${label}`}
              checked={isAllSelected}
              onChange={(e) => onSelectAll?.(e.target.checked)}
              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
            />
            <label
              htmlFor={`selectAll-${label}`}
              className="text-sm text-muted-foreground"
            >
              Select All
            </label>
          </div>
        )}
      </div>
      <Command
        onKeyDown={handleKeyDown}
        className={`overflow-visible bg-transparent ${
          disabled ? "pointer-events-none" : ""
        }`}
      >
        <div
          className={`group rounded-md border px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 bg-gray-200 ${
            disabled ? "cursor-not-allowed" : ""
          } ${errorMessage ? "border-destructive" : "border-input"}`}
        >
          <div className="flex flex-wrap gap-1">
            {isAllSelected && isCorporation ? (
              <Badge variant="secondary">{adminPlaceholder}</Badge>
            ) : (
              selected.map((framework) => {
                return (
                  <Badge key={framework.value} variant="secondary" className={disabled ? "bg-slate-200" : ""}>
                    {framework.label}
                    <button
                      className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleUnselect(framework);
                        }
                      }}
                      onMouseDown={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                      }}
                      onClick={() => handleUnselect(framework)}
                    >
                      <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                    </button>
                  </Badge>
                );
              })
            )}
            <CommandPrimitive.Input
              ref={inputRef}
              value={inputValue}
              onValueChange={setInputValue}
              onBlur={() => setOpen(false)}
              onFocus={() => setOpen(true)}
              placeholder={
                selected.length > 0
                  ? ""
                  : isAllSelected && isCorporation
                  ? ""
                  : isCorporation
                  ? placeholder
                  : ""
              }
              className="ml-2 flex-1 bg-transparent outline-none placeholder:text-muted-foreground bg-gray-200"
              disabled={disabled}
            />
          </div>
        </div>
        <div className="relative mt-2">
          <CommandList>
            {open && selectables.length > 0 ? (
              <div className="absolute top-0 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in">
                <CommandGroup className="h-48 overflow-auto cbar">
                  {selectables.map((framework) => {
                    return (
                      <CommandItem
                        key={framework.value}
                        onMouseDown={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onSelect={(value) => {
                          setInputValue("");
                          setSelected((prev) => [...prev, framework]);
                        }}
                        className={"cursor-pointer"}
                      >
                        {framework.label}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              </div>
            ) : null}
          </CommandList>
          {errorMessage && (
            <FormMessage className="px-1 text-xs text-destructive">
              {errorMessage}
            </FormMessage>
          )}
        </div>
      </Command>
    </div>
  );
}
