"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteDailyPlanningDetails = void 0;
const operation_1 = require("../../../utils/operation");
const deleteDailyPlanningDetails = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "dailyPlanningDetails",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "Daily planning details has been deleted",
    });
};
exports.deleteDailyPlanningDetails = deleteDailyPlanningDetails;
//# sourceMappingURL=delete.js.map