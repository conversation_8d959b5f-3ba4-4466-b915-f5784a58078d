import jwt from "jsonwebtoken";
import { handleError } from "./helpers";
import bcrypt from "bcrypt";
import { Request, Response } from "express";

export const login = async (
  model: string,
  params: any,
  tokenname: string,
  password: string,
  req: Request,
  res: Response,
  JWT: string,
  fieldName: string,
  fieldName2?: string
) => {
  try {
    const userParams = { ...params };
    const existinguser = await (prisma as any)[model].findUnique(userParams);

    if (!existinguser) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid credential" });
    }

    const isPasswordValid = await bcrypt.compare(
      password,
      existinguser.password
    );

    if (!isPasswordValid) {
      return res
        .status(400)
        .json({ success: false, message: "Incorrect Password" });
    }

    // // Clear all sessions and cookies for this user
    // await prisma.session.deleteMany({
    //   where: { user_id: existinguser.user_id },
    // });

    // // Clear the current cookie for this session (this will log out the user on the current device too)
    // res.clearCookie(tokenname, {
    //   httpOnly: true,
    //   // secure: process.env.NODE_ENV === "production",
    //   // sameSite: "strict", // Uncomment if needed for production
    // });

    const token = jwt.sign(
      {
        [fieldName]: existinguser[fieldName],
        username: existinguser.username,
        [fieldName2]: existinguser[fieldName2],
      },
      JWT,
      { expiresIn: "1d" }
    );

    // // Create a new session for the user
    // await prisma.session.create({
    //   data: {
    //     user_id: existinguser.user_id,
    //     session_token: token,
    //   },
    // });

    // // Set the new token in the response cookies for the current session
    return res
      .cookie(tokenname, token, {
        httpOnly: true,
        maxAge: 1 * 24 * 60 * 60 * 1000,
      })
      .status(200)
      .json({ success: true, message: "Login successful", token });
  } catch (error) {
    return handleError(res, error);
  }
};

export const login1 = async (
  model: string,
  params: any,
  tokenname: string,
  password: string,
  req: Request,
  res: Response,
  JWT: string,
  fieldName: string,
  fieldName2?: string
) => {
  try {
    const userParams = { ...params };
    const existinguser = await (prisma as any)[model].findUnique(userParams);

    if (!existinguser) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid credential" });
    }

    const isPasswordValid = await bcrypt.compare(
      password,
      existinguser.password
    );

    if (!isPasswordValid) {
      return res
        .status(400)
        .json({ success: false, message: "Incorrect Password" });
    }

    await prisma.session.deleteMany({
      where: { user_id: existinguser.id },
    });

    const token = jwt.sign(
      {
        [fieldName]: existinguser[fieldName],
        username: existinguser.username,
        [fieldName2]: existinguser[fieldName2],
      },
      JWT,
      { expiresIn: "1d" }
    );

    // Create a new session for the user
    await prisma.session.create({
      data: {
        user_id: existinguser.id,
        session_token: token,
      },
    });

    // Set the new token in the response cookies for the current session
    return res
      .cookie(tokenname, token, {
        httpOnly: true,
        maxAge: 1 * 24 * 60 * 60 * 1000, // 1 day
      })
      .status(200)
      .json({ success: true, message: "Login successful", token });
  } catch (error) {
    return handleError(res, error);
  }
};

export const superAdminLogin = async (req: any, res: Response) => {
  const { username } = req.body;
  const params = {
    where: { username: username },
  };
  const password = req.body.password;

  await login(
    "superAdmin",
    params,
    "superadmintoken",
    password,
    req,
    res,
    process.env.JWT_SECRET,
    "id"
  );
};

export const corporationLogin = async (req: any, res: Response) => {
  const { username } = req.body;
  const params = {
    where: { username: username },
  };
  const password = req.body.password;

  await login(
    "corporation",
    params,
    "corporationtoken",
    password,
    req,
    res,
    process.env.JWT_SECRET,
    "corporation_id"
  );
};

export const userLogin = async (req: any, res: Response) => {
  const { username } = req.body;
  const params = {
    where: { username: username },
  };

  const password = req.body.password;
  await login1(
    "user",
    params,
    "token",
    password,
    req,
    res,
    process.env.JWT_SECRET,
    "id",
    "corporation_id"
  );
};
