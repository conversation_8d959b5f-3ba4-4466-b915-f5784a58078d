"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getData = exports.updateTransaction = exports.deleteTransaction = exports.createTransaction = void 0;
const helpers_1 = require("../../../utils/helpers");
const createTransaction = async (model, fieldName, fields, res, req, logging_relationship, successMessage) => {
    try {
        const { corporation_id, user_id } = req;
        const user_role = corporation_id && user_id ? "FREIGHT_ADMIN_USER" : "FREIGHT_ADMIN";
        const userId = corporation_id && user_id ? user_id : corporation_id;
        // Validate required fields
        // const validationError = validateFields(res, ["name"], fields);
        // if (validationError) return validationError;
        // Check for existing role
        const criteria = {
            name: fields.name.toUpperCase(),
            corporation_id: corporation_id,
        };
        // const existingRowError = await checkExistingRow({
        //   model,
        //   criteria,
        //   res,
        //   errorMessage: "Role already exists",
        // });
        // if (existingRowError) return existingRowError;
        const createdRole = await prisma.$transaction(async (tx) => {
            // Create new role
            const newRole = await tx[model].create({
                data: {
                    // ...fields,
                    corporation_id: fields.corporation_id,
                    name: fields.name.toUpperCase(),
                },
            });
            // Create role permissions
            await Promise.all(fields.permissions.map((permission_id) => {
                return tx.rolePermission.create({
                    data: {
                        permission_id: Number(permission_id),
                        role_id: newRole.id,
                    },
                });
            }));
            return newRole;
        });
        return res.status(200).json({
            success: true,
            message: successMessage,
            createdItem: createdRole,
        });
    }
    catch (error) {
        console.error(error); // Log error for debugging
    }
};
exports.createTransaction = createTransaction;
const deleteTransaction = async ({ model, fieldName, id, res, successMessage, }) => {
    try {
        await prisma.$transaction(async (tx) => {
            await tx.rolePermission.deleteMany({
                where: { [fieldName]: id },
            });
            await tx[model].delete({
                where: { [fieldName]: id },
            });
        });
        return res.status(200).json({
            success: true,
            message: successMessage,
        });
    }
    catch (error) {
        console.log(error);
        return res.status(500).json({
            success: false,
            message: "An error occurred while deleting the role and permissions",
        });
    }
};
exports.deleteTransaction = deleteTransaction;
const updateTransaction = async ({ model, fieldName, id, data, res, req, logging_relationship, successMessage, }) => {
    try {
        const { corporation_id, user_id } = req;
        const user_role = corporation_id && user_id ? "FREIGHT_ADMIN_USER" : "FREIGHT_ADMIN";
        const userId = corporation_id && user_id ? user_id : corporation_id;
        // Fetch the current role using findUnique helper function
        const currentRole = await findUnique(model, { where: { [fieldName]: id } }, res);
        if (!currentRole) {
            return res.status(404).json({
                success: false,
                message: "Role not found",
            });
        }
        // Check if the name has changed
        if (data.name && data.name !== currentRole.name) {
            const criteria = {
                corporation_id: corporation_id,
                name: data.name,
            };
            const errorMessage = `${model} already exists`;
            const existingRowError = await (0, helpers_1.checkExistingRow)({
                model,
                criteria,
                res,
                errorMessage,
            });
            if (existingRowError)
                return existingRowError;
        }
        await prisma.$transaction(async (tx) => {
            await tx[model].update({
                where: {
                    [fieldName]: id,
                },
                data: {
                    name: data.name,
                    client_id: JSON.stringify(data.client_id),
                },
            });
            await tx.rolePermission.deleteMany({
                where: { role_id: Number(id) },
            });
            await Promise.all(data.permission.map(async (permission) => {
                await tx.rolePermission.create({
                    data: {
                        permission_id: Number(permission),
                        role_id: Number(id),
                    },
                });
            }));
        });
        return res.status(200).json({
            success: true,
            message: successMessage,
        });
    }
    catch (error) {
        console.error(error);
        return res.status(500).json({
            success: false,
            message: "Something went wrong",
        });
    }
};
exports.updateTransaction = updateTransaction;
const findUnique = async (model, criteria, res) => {
    try {
        const result = await prisma[model].findUnique(criteria);
        if (!result) {
            return null;
        }
        return result;
    }
    catch (error) {
        //  (error)
    }
};
const getData = async (model, params) => {
    try {
        const data = await prisma[model].findMany(params);
        return data;
    }
    catch (error) {
        throw error;
    }
};
exports.getData = getData;
//# sourceMappingURL=utility.js.map