// "use client";

// import { AgGridReact } from "ag-grid-react";
// import { usePathname, useRouter, useSearchParams } from "next/navigation";
// import UpdateWorkReport from "./UpdateWorkReport";
// import { PermissionWrapper } from "@/lib/permissionWrapper";
// import DeleteRow from "@/app/_component/DeleteRow";
// import { formatDate, formatDuration, formatTimeZone } from "@/lib/swrFetching";
// import { workreport_routes } from "@/lib/routePath";
// import { useState, useEffect, useRef } from "react";
// import { AllCommunityModule, ModuleRegistry } from "ag-grid-community";
// import { getAllData } from "@/lib/helpers";
// import Pagination from "@/app/_component/Pagination";
// import { Button } from "@/components/ui/button";
// import { AiOutlineLoading3Quarters } from "react-icons/ai";
// import { Calendar } from "lucide-react";
// import {
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from "@/components/ui/popover";
// import { Calendar as CalendarComponent } from "@/components/ui/calendar";
// import { format } from "date-fns";
// import { cn } from "@/lib/utils";
// import ExportReport from "./ExportReport";
// import { FaSearch } from "react-icons/fa";
// import { Input } from "@/components/ui/input";
// import {
//   DropdownMenu,
//   DropdownMenuCheckboxItem,
//   DropdownMenuContent,
//   DropdownMenuTrigger,
// } from "@/components/ui/dropdown-menu";

// ModuleRegistry.registerModules([AllCommunityModule]);

// const grid = ({ permissions,data }) => {
//   console.log(data);
  
//   const [rowData, setRowData] = useState(data);
//   const [totalRows, setTotalRows] = useState(0);
//   const [fromDate, setFromDate] = useState(undefined);
//   const [toDate, setToDate] = useState(undefined);
//   const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
//   const [page, setPage] = useState(1);
//   const [pageSize, setPageSize] = useState(50);

//   const searchParams = useSearchParams();
//   const pathname = usePathname();
//   const { replace } = useRouter();
//   const gridRef = useRef(null);
//   const [filterModel, setFilterModel] = useState(null);
//   const [totals, setTotals] = useState<{
//       actualNumber: number;
//       timeSpent: number;
//     }>({
//       actualNumber: 0,
//       timeSpent: 0,
//     });

//   const handleSearchChange = (event, columnName) => {
//     const value = event.target.value.trim();
//     const updatedParams = new URLSearchParams(searchParams);

//     if (value) {
//       updatedParams.set(columnName, value);
//       updatedParams.set("page", "1"); // Reset to page 1 on search
//     } else {
//       updatedParams.delete(columnName);
//     }

//     replace(`${pathname}?${updatedParams.toString()}`);
//   };

//   const handleColumnSelection = (columnKey, columnHeader) => {
//     setSelectedColumns((prevSelectedColumns) => {
//       const updatedParams = new URLSearchParams(searchParams);
//       let updatedColumns;

//       if (prevSelectedColumns.includes(columnKey)) {
//         updatedColumns = prevSelectedColumns.filter((col) => col !== columnKey);
//         if (columnHeader === "Date") {
//           updatedParams.delete("fDate");
//           updatedParams.delete("tDate");
//         } else {
//           updatedParams.delete(columnHeader);
//         }
//       } else {
//         updatedColumns = [...prevSelectedColumns, columnKey];
//         updatedParams.set(columnHeader, "");
//       }

//       replace(`${pathname}?${updatedParams.toString()}`);
//       return updatedColumns;
//     });
//   };

//   // const showData = async () => {
//   //   try {
     

   

//   //     const response = await getAllData(
//   //       `${workreport_routes.GETALL_WORKREPORT}`
//   //     );

//   //     setRowData(response.data);
//   //     setTotalRows(response.totalRows);
//   //   } catch (error) {
//   //     console.error("Error fetching work reports", error);
//   //   }
//   // };

//   // useEffect(() => {
//   //   showData();
//   // }, [filterModel, page, pageSize, fromDate, toDate]);

//   const resetDateFilters = () => {
//     setFromDate(undefined);
//     setToDate(undefined);
//   };

//   const columnDefs = [
//     {
//       field: "date",
//       headerName: "Date",
//       valueFormatter: (params) => (params.value ? formatDate(params.value) : "No Date"),
//       width: 100,
//     },
//     {
//       field: "user",
//       headerName: "Username",
//       valueGetter: (params) => params.data?.user?.username || "No Username",
//       width: 120,
//     },
//     {
//       field: "client_name",
//       headerName: "Client",
//       valueGetter: (params) => params.data?.client?.client_name || "No Client Name",
//       width: 120,
//     },
//     {
//       field: "carriername",
//       headerName: "Carrier",
//       valueGetter: (params) => params.data?.carrier?.name || "No Carrier",
//       width: 120,
//     },
//     {
//       field: "work_type",
//       headerName: "Work",
//       valueGetter: (params) => params.data?.work_type?.work_type || "No Work Type",
//       width: 120,
//     },
//     {
//       field: "category",
//       headerName: "Category",
//       valueGetter: (params) => params.data?.category?.category_name || "No Category",
//       width: 120,
//     },
//     {
//       field: "task_type",
//       headerName: "Type",
//       valueGetter: (params) => params.data?.task_type || "No Task Type",
//       width: 120,
//     },
//     {
//       field: "actual_number",
//       headerName: "ActualNumber",
//       valueGetter: (params) => params.data?.actual_number || "No Actual Number",
//       width: 120,
//     },
//     {
//       field: "start_time",
//       headerName: "Start Time",
//       valueGetter: (params) => formatTimeZone(params.data?.start_time || ""),
//       width: 120,
//     },
//     {
//       field: "finish_time",
//       headerName: "Finish Time",
//       valueGetter: (params) => formatTimeZone(params.data?.finish_time || ""),
//       width: 120,
//     },
//     {
//       field: "time_spent",
//       headerName: "Time Spent",
//       valueGetter: (params) => params.data?.time_spent || "No Time Spent",
//       width: 120,
//     },
//     {
//       field: "notes",
//       headerName: "Notes",
//       valueGetter: (params) => params.data?.notes || "No Notes",
//       width: 120,
//     },
//     {
//       field: "action",
//       headerName: "Action",
//       cellRenderer: (params) => (
//         <div className="flex items-center">
//           <PermissionWrapper permissions={permissions} requiredPermissions={["update-workReport"]}>
//             <UpdateWorkReport workReport={params?.data} />
//           </PermissionWrapper>
//           <PermissionWrapper permissions={permissions} requiredPermissions={["delete-workReport"]}>
//             <DeleteRow
//               route={`${workreport_routes.DELETE_WORKREPORT}/${params.data?.id}`}
//             />
//           </PermissionWrapper>
//         </div>
//       ),
//       width: 120,
//     },
//   ];

//   const filterColumns = selectedColumns.length
//     ? selectedColumns
//         .map((columnKey) => columnDefs.find((col) => col.field === columnKey || col.field === columnKey))
//         .filter(Boolean)
//     : [];

//     const totalview = (
//         // <div className="fixed w-1/2 ">
//         <div className="flex top-14 justify-start text-center gap-1 w-full">
//           <div className="mb-3 w-[300px]  border-dashed border  tracking-widest border-gray-500 rounded-lg text-center p-2  items-center space-x-6">
//             <p className="text-s">
//               Time Spent:{" "}
//               <span className="font-normal ">
//                 {formatDuration(totals.timeSpent)}
//               </span>
//             </p>
//           </div>
//           <div className="mb-3 w-[300px]  border-dashed border  tracking-widest border-gray-500 rounded-lg text-center p-2  items-center space-x-6">
//             <p className="text-s">Actual Number: {totals.actualNumber}</p>
//           </div>
//         </div>
//         // </div>
//       );

//   return (
//     <div className="w-full h-screen flex flex-col">
//       <div className="flex justify-between mb-2">
//         <div className="flex gap-2">
//           <Popover>
//             <PopoverTrigger asChild>
//               <Button variant="outline" className={cn("w-[170px] text-left", !fromDate && "text-muted-foreground")}>
//                 <Calendar className="mr-2 h-4 w-4" />
//                 {fromDate ? format(fromDate, "PPP") : "From date"}
//               </Button>
//             </PopoverTrigger>
//             <PopoverContent className="w-auto p-0" align="start">
//               <CalendarComponent mode="single" selected={fromDate} onSelect={setFromDate} initialFocus />
//             </PopoverContent>
//           </Popover>

//           <Popover>
//             <PopoverTrigger asChild>
//               <Button variant="outline" className={cn("w-[170px] text-left", !toDate && "text-muted-foreground")}>
//                 <Calendar className="mr-2 h-4 w-4" />
//                 {toDate ? format(toDate, "PPP") : "To date"}
//               </Button>
//             </PopoverTrigger>
//             <PopoverContent className="w-auto p-0" align="start">
//               <CalendarComponent mode="single" selected={toDate} onSelect={setToDate} initialFocus />
//             </PopoverContent>
//           </Popover>

//           {(fromDate || toDate) && (
//             <Button variant="ghost" onClick={resetDateFilters} className="bg-gray-100">
//               Clear dates
//             </Button>
//           )}
//         </div>

//         <div className="flex gap-4 items-start">
//           <DropdownMenu>
//             <DropdownMenuTrigger asChild>
//               <div className="p-2 border-2 rounded-md cursor-pointer">
//                 <FaSearch />
//               </div>
//             </DropdownMenuTrigger>
//             <DropdownMenuContent className="bg-white dark:bg-gray-900">
//               <div className="px-3 py-2">
//                 <p className="font-semibold text-sm">Select Columns</p>
//                 {columnDefs
//                   .filter((col) => col.field && !["action", "Sr No.", "time_spent"].includes(col.field))
//                   .map((col, idx) => (
//                     <DropdownMenuCheckboxItem
//                       key={idx}
//                       checked={selectedColumns.includes(col.field)}
//                       onCheckedChange={() => handleColumnSelection(col.field, col.headerName)}
//                     >
//                       {col.headerName}
//                     </DropdownMenuCheckboxItem>
//                   ))}
//               </div>
//             </DropdownMenuContent>
//           </DropdownMenu>

//           {filterColumns.length > 0 && (
//             <div className="flex gap-2 flex-wrap">
//               {filterColumns.map((colDef, idx) => {
//                 if (colDef.field === "date") return null;

//                 const key = colDef.headerName;
//                 const value = searchParams.get(key) || "";

//                 return (
//                   <Input
//                     key={idx}
//                     placeholder={`Search in ${key}`}
//                     defaultValue={value}
//                     onChange={(e) => handleSearchChange(e, key)}
//                     className="w-[200px] dark:bg-gray-700 !outline-main-color"
//                   />
//                 );
//               })}
//             </div>
//           )}
          
//           {data.data && (
//         <div>
//           {/*   className="mt-4 bg-gray-100 border border-gray-300 rounded-lg text-center w-60 py-2 flex flex-col justify-center items-center" */}
//           {totalview}
//         </div>
//       )}
// {/* 
//           <ExportReport
//             filterModel={filterModel}
//             permissions={permissions}
//             fromDate={fromDate}
//             toDate={toDate}
//           /> */}
//         </div>
//       </div>

//       <AgGridReact
//         ref={gridRef}
//         rowData={data?.data}
//         columnDefs={columnDefs}
//         onFilterChanged={() => {
//           const model = gridRef.current.api.getFilterModel();
//           setFilterModel(model);
//         }}
//       />
      
//     </div>
//   );
// };

// export default grid;
