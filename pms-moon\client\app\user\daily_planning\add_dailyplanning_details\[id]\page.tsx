import { NavBar } from "@/app/_component/NavBar";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import React from "react";

import AddDailyPlanningDetails from "./AddDailyPlanningDetails";
import Sidebar from "@/components/sidebar/Sidebar";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  carrier_routes,
  daily_planning,
  employee_routes,
} from "@/lib/routePath";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import { useParams } from "next/navigation";

const page = async ({ params }: { params: { id: string } }) => {
  const { id } = params;

  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions = userData?.role?.role_permission || []
  const corporationCookie = await getCookie("corporationtoken");

  // If corporationCookie doesn't exist, manually remove 'allow_all' from permissions
  // let permissions = userPermissions;
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;
  const data = await getAllData(
    `${daily_planning.GET_DAILY_PLANNING_BY_ID}/${id}`
  );
  const carrierByClient = await getAllData(
    `${carrier_routes.GET_CARRIER_BY_CLIENT}/${data.client?.id}`
  );
  return (
    <>
      <SidebarProvider>
        <Sidebar permissions={permissions} profile={userData} />
        {/* <NavBar /> */}

        <main className="pt-1">
          <SidebarTrigger />
        </main>
        <div className="p-1 mt-8 w-full">
          <div className="absolute top-0">
            <BreadCrumbs
              breadcrumblist={[
                { link: "/user/daily_planning", name: "Daily Planning" },
                {
                  link: `/user/daily_planning/add_dailyplanning_details/${id}`,
                  name: "Add Daily Planning Details",
                },
              ]}
            />
          </div>
          <AddDailyPlanningDetails carrierByClient={carrierByClient} userData={userData} dailyPlanning= {data} />
        </div>
      </SidebarProvider>
    </>
  );
};

export default page;
