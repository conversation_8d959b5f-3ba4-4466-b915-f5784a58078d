import React, { useEffect, useRef } from "react";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  entriesPerPage?: number;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  entriesPerPage, 
}) => {
  const visiblePages = 4;
  const prevEntriesPerPage = useRef(entriesPerPage);

  useEffect(() => {
    if (prevEntriesPerPage.current !== entriesPerPage) {
      onPageChange(1); // Reset to first page when entriesPerPage changes
    }
    prevEntriesPerPage.current = entriesPerPage;
  }, [entriesPerPage, onPageChange]);

  const startPage = Math.max(1, currentPage - Math.floor(visiblePages / 2));
  const endPage = Math.min(
    totalPages,
    currentPage + Math.floor(visiblePages / 2)
  );

  const adjustedStartPage = Math.max(1, endPage - visiblePages + 1);
  const adjustedEndPage = Math.min(
    totalPages,
    adjustedStartPage + visiblePages - 1
  );

  const pageNumbers = [];
  for (let i = adjustedStartPage; i <= adjustedEndPage; i++) {
    pageNumbers.push(i);
  }

  return (
    <div className="flex items-center justify-center space-x-2 p-4">
      <span className="text-gray-600">
        Page {currentPage} of {totalPages}
      </span>
      <span className="mx-2">|</span>

      {adjustedStartPage > 1 && (
        <>
          <span
            onClick={() => onPageChange(1)}
            className="cursor-pointer text-gray-600 hover:font-semibold hover:text-slate-900"
          >
            {1}
          </span>
          <span className="px-3 text-gray-500">...</span>
        </>
      )}

      {pageNumbers.map((num) => (
        <span
          key={num}
          onClick={() => onPageChange(num)}
          role="button"
          aria-label={`Go to page ${num}`}
          className={`transition cursor-pointer px-3 py-2 rounded-md text-center ${
            num === currentPage
              ? "font-bold text-slate-900"
              : "text-gray-600 hover:font-semibold hover:text-slate-900 "
          }`}
        >
          {num}
        </span>
      ))}

      {adjustedEndPage < totalPages && (
        <>
          <span className="px-3 text-gray-500">...</span>
          <span
            onClick={() => onPageChange(totalPages)}
            className="cursor-pointer text-gray-600  hover:text-slate-900 hover:font-semibold"
          >
            {totalPages}
          </span>
        </>
      )}
    </div>
  );
};

export default Pagination;
