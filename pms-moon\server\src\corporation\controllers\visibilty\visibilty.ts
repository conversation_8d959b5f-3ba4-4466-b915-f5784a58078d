import { handleError } from "../../../utils/helpers";
import { getVisibility } from "./visibiltyHelper";

export const getUserVisbilty = async (req, res) => {
  try {
    const table = req.params;

    const userId = req.user_id;
    const data = await getVisibility(7, table.table); // Get depth value (number)


    return res.status(200).json(data);
  } catch (error) {
    return handleError(res, error);
  }
};
