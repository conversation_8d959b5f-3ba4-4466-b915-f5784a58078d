"use client";
import Logout from "@/app/(auth)/logout/Logout";
import { RiUserFill } from "react-icons/ri";
import { BiSolidCategory } from "react-icons/bi";
import {
  FaUser,
  FaHome,
  FaUserPlus,
  FaUserCircle,
  FaTruck,
  FaBuilding,
  FaBriefcase,
  FaCalendar,
  FaShieldAlt,
  FaFileSignature,
  FaBars,
} from "react-icons/fa";
import { MdTaskAlt } from "react-icons/md";
import NavLink from "@/app/_component/NavLink";
import { TbFileSpreadsheet, TbReportSearch } from "react-icons/tb";
import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { BsFillFileEarmarkSpreadsheetFill } from "react-icons/bs";

const SideBar = ({
  className,
  permissions,
  profile,
}: {
  className?: string;
  permissions: any[];
  profile: any;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const path = usePathname();

  const firstName = profile?.firstName || "Admin";
  const lastName = profile?.lastName || "";
  const userName = `${firstName} ${lastName}`;
  const userInitial =
    firstName.charAt(0).toUpperCase() + lastName.charAt(0).toUpperCase();

  const Routes = [
    {
      label: "User Profile",
      path: "/user/user-profile",
      permission: { module: "TRACKER", action: "TRACKER" },
      icon: <FaUser />,
    },
    {
      label: "Dashboard",
      path: "/pms/dashboard",
      permission: { module: "DASHBOARD", action: "view-dashboard" },
      icon: <FaHome />,
    },
    {
      label: "TaskSphere",
      path: "/user/tracker",
      permission: { module: "TRACKER", action: "update-tracker" },
      icon: <MdTaskAlt />,
    },
    {
      label: "TaskRadar",
      path: "/user/daily_planning",
      permission: { module: "DAILY PLANNING" },
      icon: <FaCalendar />,
    },
    {
      label: "TrackSheet",
      path: "/user/trackSheets",
      permission: { module: "TrackSheet" },
      icon: <BsFillFileEarmarkSpreadsheetFill   />,
    }
  ];

const hasAllowAllPermission = permissions.includes("allow_all" as any);

const permissionsRoutes = hasAllowAllPermission
  ? Routes.filter(
      (route) =>
        route.permission.module !== "TRACKER" &&
        route.permission.module !== "DAILY PLANNING" &&
        route.permission.module !== "TrackSheet"
    )
  : Routes.filter((route) => {
      return permissions.some((permission) => {
        if (typeof permission === "string") {
          return permission === route.permission.module;
        }
        if (typeof permission.permission === "object") {
          return permission.permission.module === route.permission.module;
        }
        return false;
      });
    });

  const iconClasses = "min-w-[20px] min-h-[20px] w-5 h-5 text-slate-200";

  // Store permissions in session storage
  useEffect(() => {
    sessionStorage.setItem(
      "permissions",
      JSON.stringify(permissions.map((perm) => perm.permission?.module))
    );
  }, [permissions]);

  // Add/remove body class on toggle
  useEffect(() => {
    document.body.classList.toggle("sidebar-expanded", isOpen);
    document.body.classList.toggle("sidebar-collapsed", !isOpen);
  }, [isOpen]);

  return (
    <div className="sticky top-0 left-0 z-[50] h-screen">
      <div
        className={cn(
          `transition-all duration-300`,
          isOpen ? "w-52" : "w-16",
          "h-screen bg-slate-950 text-white flex flex-col rounded-r-lg overflow-visible relative"
        )}
      >
        {/* Sidebar Header */}
        <div
          className={`flex items-center py-4 px-3 ${
            isOpen ? "justify-start" : "justify-center"
          }`}
        >
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="text-white focus:outline-none"
          >
            <FaBars className="w-6 h-6" />
          </button>
          {isOpen && (
            <div className="text-white font-bold ml-3 text-xl">Oi360</div>
          )}
        </div>

        {/* Sidebar Content */}
        <div className="flex-grow flex flex-col space-y-2 overflow-visible">
          {permissionsRoutes.map((route) => (
            <div key={route.path}>
              <NavLink
                key={route.path}
                path={route.path}
                open={isOpen}
                icon={<div className={iconClasses}>{route.icon}</div>}
                label={route.label}
              />
            </div>
          ))}

          {/* Management Section */}
          <div className="mt-4">
            <NavLink
              path="/pms/management_services"
              open={isOpen}
              icon={
                <div className={iconClasses}>
                  <FaBriefcase />
                </div>
              }
              label="Management"
            />
          </div>
        </div>

        {/* Sidebar Footer */}
        <div className="mt-auto">
          <div
            className={`flex items-center space-x-2 ${
              isOpen ? "justify-center" : "pl-2"
            }`}
            title="User Profile"
          >
            <div className="bg-blue-500 text-white rounded-full w-10 h-10 flex items-center justify-center text-xl font-bold">
              {userInitial}
            </div>
            {isOpen && (
              <span className="text-white text-lg font-semibold capitalize">
                {userName}
              </span>
            )}
          </div>
          <div className="p-4">
            <Logout isOpen={isOpen} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SideBar;