"use client";
import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface Permission {
  id: number;
  module: string;
  action: string;
}

interface RolesSelectedProps {
  role: number[]; // Array of selected permission IDs
  getAllPermission: Record<string, Permission[]>; // Permissions grouped by action
}

export const RolesSelected: React.FC<RolesSelectedProps> = ({
  role,
  getAllPermission,
}) => {
  // Flatten permissions into a single array
  const allPermissions: Permission[] = Object.values(getAllPermission).flat();

  // Filter only selected permissions
  const selectedPermissions = allPermissions.filter((permission) =>
    role?.includes(permission.id)
  );

  // Group selected permissions by their respective modules
  const groupedByModule = selectedPermissions.reduce<Record<string, Permission[]>>(
    (acc, permission) => {
      const modules = permission.module;
      if (!acc[modules]) {
        acc[modules] = [];
      }
      acc[modules].push(permission);
      return acc;
    },
    {}
  );

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="text-xs font-bold">MODULE</TableHead>
          <TableHead className="text-xs font-bold">ACTIONS</TableHead>
        </TableRow>
      </TableHeader>

      <TableBody className="overflow-y-auto">
        {Object.entries(groupedByModule).map(([module, actions]) => (
          <TableRow key={module}>
            <TableCell className="text-xs py-2">{module}</TableCell>
            <TableCell className="text-xs py-2 flex flex-wrap gap-2">
              {actions.map((action) => (
                <span
                  key={action.id}
                  className="bg-gray-500/20 p-1 rounded-md capitalize"
                >
                  {action.action}
                </span>
              ))}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};
