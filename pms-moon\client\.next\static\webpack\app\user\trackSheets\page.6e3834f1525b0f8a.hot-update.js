"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/createTrackSheet.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-minus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice type is required\"),\n        billToClient: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        currency: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        financialNotes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.string()).optional().default([]),\n        otherDocuments: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        notes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        mistake: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData, activeView, setActiveView, permissions } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [manualMatchingData, setManualMatchingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: \"\",\n                    receivedDate: \"\",\n                    shipmentDate: \"\",\n                    carrierName: \"\",\n                    invoiceStatus: \"ENTRY\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    billToClient: \"\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    financialNotes: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    otherDocuments: \"\",\n                    notes: \"\",\n                    mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    const fetchManualMatchingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.manualMatchingMapping_routes.GET_MANUAL_MATCHING_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setManualMatchingData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching manual matching mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        fetchLegrandData();\n        fetchManualMatchingData();\n    }, [\n        fetchLegrandData,\n        fetchManualMatchingData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n            handleManualMatchingAutoFill(entryIndex, divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    };\n    const handleManualMatchingAutoFill = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, division)=>{\n        var _formValues_entries, _clientOptions_find;\n        if (!division || !manualMatchingData.length) {\n            return;\n        }\n        const formValues = form.getValues();\n        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        if (entryClientName !== \"LEGRAND\") {\n            return;\n        }\n        const matchingEntry = manualMatchingData.find((mapping)=>mapping.division === division);\n        if (matchingEntry && matchingEntry.ManualShipment) {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), matchingEntry.ManualShipment);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    }, [\n        form,\n        manualMatchingData,\n        clientOptions\n    ]);\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFieldArray)({\n        control: form.control,\n        name: \"entries\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                const date = new Date(receivedDate);\n                receivedDateStr = date.toISOString().split(\"T\")[0];\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n                if (name.includes(\"division\")) {\n                    const entryMatch = name.match(/entries\\.(\\d+)\\.division/);\n                    if (entryMatch) {\n                        var _formValues_entries, _clientOptions_find;\n                        const entryIndex = parseInt(entryMatch[1], 10);\n                        const formValues = form.getValues();\n                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                        if (entryClientName === \"LEGRAND\") {\n                            var _formValues_entries_entryIndex, _formValues_entries1;\n                            const divisionValue = (_formValues_entries1 = formValues.entries) === null || _formValues_entries1 === void 0 ? void 0 : (_formValues_entries_entryIndex = _formValues_entries1[entryIndex]) === null || _formValues_entries_entryIndex === void 0 ? void 0 : _formValues_entries_entryIndex.division;\n                            if (divisionValue) {\n                                handleManualMatchingAutoFill(entryIndex, divisionValue);\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames,\n        handleManualMatchingAutoFill,\n        clientOptions\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    const addNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newIndex = fields.length;\n        append({\n            clientId: initialClientId,\n            company: \"\",\n            division: \"\",\n            invoice: \"\",\n            masterInvoice: \"\",\n            bol: \"\",\n            invoiceDate: \"\",\n            receivedDate: \"\",\n            shipmentDate: \"\",\n            carrierName: \"\",\n            invoiceStatus: \"ENTRY\",\n            manualMatching: \"\",\n            invoiceType: \"\",\n            billToClient: \"\",\n            currency: \"\",\n            qtyShipped: \"\",\n            weightUnitName: \"\",\n            quantityBilledText: \"\",\n            invoiceTotal: \"\",\n            savings: \"\",\n            financialNotes: \"\",\n            ftpFileName: \"\",\n            ftpPage: \"\",\n            docAvailable: [],\n            otherDocuments: \"\",\n            notes: \"\",\n            mistake: \"\",\n            legrandAlias: \"\",\n            legrandCompanyName: \"\",\n            legrandAddress: \"\",\n            legrandZipcode: \"\",\n            shipperAlias: \"\",\n            shipperAddress: \"\",\n            shipperZipcode: \"\",\n            consigneeAlias: \"\",\n            consigneeAddress: \"\",\n            consigneeZipcode: \"\",\n            billtoAlias: \"\",\n            billtoAddress: \"\",\n            billtoZipcode: \"\",\n            customFields: []\n        });\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(newIndex, initialClientId);\n            handleCustomFieldsFetch(newIndex, initialClientId);\n            if (companyFieldRefs.current[newIndex]) {\n                var _companyFieldRefs_current_newIndex, _companyFieldRefs_current_newIndex1, _companyFieldRefs_current_newIndex2;\n                const inputElement = ((_companyFieldRefs_current_newIndex = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex === void 0 ? void 0 : _companyFieldRefs_current_newIndex.querySelector(\"input\")) || ((_companyFieldRefs_current_newIndex1 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex1 === void 0 ? void 0 : _companyFieldRefs_current_newIndex1.querySelector(\"button\")) || ((_companyFieldRefs_current_newIndex2 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex2 === void 0 ? void 0 : _companyFieldRefs_current_newIndex2.querySelector(\"select\"));\n                if (inputElement) {\n                    inputElement.focus();\n                    try {\n                        inputElement.click();\n                    } catch (e) {}\n                }\n            }\n            updateFilenames();\n        }, 200);\n    }, [\n        append,\n        fields.length,\n        updateFilenames,\n        initialClientId,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch\n    ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.shiftKey && e.key === \"Enter\") {\n            e.preventDefault();\n            addNewEntry();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit,\n        addNewEntry\n    ]);\n    const removeEntry = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"You must have at least one entry\");\n        }\n    };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 pl-3 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"view\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"View TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1079,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"create\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"Create TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1090,\n                                columnNumber: 13\n                            }, undefined),\n                            activeView === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-4 ml-4 flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                                    ...selectionForm,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    form: selectionForm,\n                                                    name: \"associateId\",\n                                                    label: \"Select Associate\",\n                                                    placeholder: \"Search Associate...\",\n                                                    isRequired: true,\n                                                    options: associateOptions || [],\n                                                    onValueChange: (value)=>{\n                                                        setInitialAssociateId(value);\n                                                        if (value && initialClientId) {\n                                                            validateClientForAssociate(value, initialClientId);\n                                                        } else {\n                                                            setInitialClientId(\"\");\n                                                            selectionForm.setValue(\"clientId\", \"\");\n                                                        }\n                                                        setShowFullForm(false);\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1109,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    form: selectionForm,\n                                                    name: \"clientId\",\n                                                    label: \"Select Client\",\n                                                    placeholder: \"Search Client...\",\n                                                    isRequired: true,\n                                                    disabled: !initialAssociateId,\n                                                    options: clientOptions || [],\n                                                    onValueChange: (value)=>{\n                                                        setInitialClientId(value);\n                                                        if (showFullForm) {\n                                                            clearEntrySpecificClients();\n                                                        }\n                                                        if (value && initialAssociateId) {\n                                                            setTimeout(()=>{\n                                                                handleInitialSelection(initialAssociateId, value);\n                                                            }, 100);\n                                                        } else {\n                                                            setShowFullForm(false);\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1131,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1130,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1107,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1106,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1104,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1078,\n                        columnNumber: 11\n                    }, undefined),\n                    activeView === \"view\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            permissions: permissions,\n                            client: client\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1165,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1164,\n                        columnNumber: 13\n                    }, undefined) : /* Form Section - Only show when both associate and client are selected */ showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                        ...form,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: form.handleSubmit(onSubmit),\n                            onKeyDown: handleFormKeyDown,\n                            className: \"space-y-3\",\n                            children: [\n                                fields.map((field, index)=>{\n                                    var _missingFields_index;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1181,\n                                                            columnNumber: 27\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"Entry #\",\n                                                                index + 1\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1184,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1180,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1179,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1197,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"Client Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1198,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1196,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"mt-2\",\n                                                                                form: form,\n                                                                                label: \"FTP File Name\",\n                                                                                name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                                type: \"text\",\n                                                                                isRequired: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1205,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1204,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP Page\",\n                                                                            name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1214,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"mt-0\",\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".carrierName\"),\n                                                                                label: \"Select Carrier\",\n                                                                                placeholder: \"Search Carrier\",\n                                                                                isRequired: true,\n                                                                                options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                    const currentEntries = form.getValues(\"entries\") || [];\n                                                                                    const isSelectedInOtherEntries = currentEntries.some((entry, entryIndex)=>entryIndex !== index && entry.carrierName === carrier.value);\n                                                                                    return !isSelectedInOtherEntries;\n                                                                                })) || [],\n                                                                                onValueChange: ()=>{\n                                                                                    setTimeout(()=>updateFilenames(), 100);\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1222,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1221,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                                                            children: [\n                                                                                                \"Billed to \",\n                                                                                                entryClientName || \"Client\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1264,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4 h-10\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            name: \"entries.\".concat(index, \".billToClient\"),\n                                                                                                            value: \"yes\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1269,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"Yes\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1275,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1268,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            name: \"entries.\".concat(index, \".billToClient\"),\n                                                                                                            value: \"no\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1278,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"No\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1284,\n                                                                                                            columnNumber: 43\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1277,\n                                                                                                    columnNumber: 41\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1267,\n                                                                                            columnNumber: 39\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1263,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1247,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1203,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                (()=>{\n                                                                    var _formValues_entries, _clientOptions_find;\n                                                                    const formValues = form.getValues();\n                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                    return entryClientName === \"LEGRAND\";\n                                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Shipper\",\n                                                                                fieldPrefix: \"shipper\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1307,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Consignee\",\n                                                                                fieldPrefix: \"consignee\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1316,\n                                                                                columnNumber: 35\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Bill-to\",\n                                                                                fieldPrefix: \"billto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1325,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1306,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1305,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            ref: (el)=>{\n                                                                                companyFieldRefs.current[index] = el;\n                                                                            },\n                                                                            className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Company\",\n                                                                                name: \"entries.\".concat(index, \".company\"),\n                                                                                type: \"text\",\n                                                                                disable: (()=>{\n                                                                                    var _formValues_entries, _clientOptions_find;\n                                                                                    const formValues = form.getValues();\n                                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                    return entryClientName === \"LEGRAND\";\n                                                                                })()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1346,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1340,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                const isLegrand = entryClientName === \"LEGRAND\";\n                                                                                return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    form: form,\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    label: \"Division\",\n                                                                                    placeholder: \"Search Division\",\n                                                                                    disabled: false,\n                                                                                    options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index),\n                                                                                    onValueChange: (value)=>{\n                                                                                        handleManualMatchingAutoFill(index, value);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1386,\n                                                                                    columnNumber: 37\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    form: form,\n                                                                                    label: \"Division\",\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    type: \"text\",\n                                                                                    placeholder: \"Enter Division\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1404,\n                                                                                    columnNumber: 37\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1368,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1339,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1195,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1193,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1421,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Document Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1422,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1420,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Master Invoice\",\n                                                                        name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                        type: \"text\",\n                                                                        onBlur: (e)=>{\n                                                                            const masterInvoiceValue = e.target.value;\n                                                                            if (masterInvoiceValue) {\n                                                                                form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1427,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice\",\n                                                                        name: \"entries.\".concat(index, \".invoice\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1442,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"BOL\",\n                                                                        name: \"entries.\".concat(index, \".bol\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1449,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1426,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Received Date\",\n                                                                        name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1457,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Date\",\n                                                                        name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1465,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Shipment Date\",\n                                                                        name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                        type: \"text\",\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1473,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1456,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1419,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1486,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Financial & Shipment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1487,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1485,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Total\",\n                                                                        name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                        type: \"number\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1492,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".currency\"),\n                                                                        label: \"Currency\",\n                                                                        placeholder: \"Search currency\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"USD\",\n                                                                                label: \"USD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CAD\",\n                                                                                label: \"CAD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"EUR\",\n                                                                                label: \"EUR\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1499,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Savings\",\n                                                                        name: \"entries.\".concat(index, \".savings\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1511,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes\",\n                                                                        name: \"entries.\".concat(index, \".financialNotes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1517,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1491,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Shipped\",\n                                                                        name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                        type: \"number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1525,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Weight Unit\",\n                                                                        name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1531,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                        label: \"Invoice Type\",\n                                                                        placeholder: \"Search Invoice Type\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"FREIGHT\",\n                                                                                label: \"FREIGHT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"ADDITIONAL\",\n                                                                                label: \"ADDITIONAL\"\n                                                                            },\n                                                                            {\n                                                                                value: \"BALANCED DUE\",\n                                                                                label: \"BALANCED DUE\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CREDIT\",\n                                                                                label: \"CREDIT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"REVISED\",\n                                                                                label: \"REVISED\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1538,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Billed Text\",\n                                                                        name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1555,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1524,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Status\",\n                                                                        name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                        type: \"text\",\n                                                                        disable: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1563,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1570,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1571,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1572,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1562,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1484,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1579,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Additional Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1580,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1578,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: [\n                                                                    (()=>{\n                                                                        var _formValues_entries, _clientOptions_find;\n                                                                        const formValues = form.getValues();\n                                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                        const isLegrand = entryClientName === \"LEGRAND\";\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: isLegrand ? \"Manual or Matching (Auto-filled)\" : \"Manual or Matching\",\n                                                                            name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true,\n                                                                            disable: isLegrand,\n                                                                            placeholder: isLegrand ? \"Auto-filled based on division\" : \"Enter manual or matching\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1597,\n                                                                            columnNumber: 33\n                                                                        }, undefined);\n                                                                    })(),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes (Remarks)\",\n                                                                        name: \"entries.\".concat(index, \".notes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1616,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Mistake\",\n                                                                        name: \"entries.\".concat(index, \".mistake\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1622,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Documents Available\",\n                                                                            name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                            options: [\n                                                                                {\n                                                                                    label: \"Invoice\",\n                                                                                    value: \"Invoice\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"BOL\",\n                                                                                    value: \"Bol\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"POD\",\n                                                                                    value: \"Pod\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Packages List\",\n                                                                                    value: \"Packages List\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Other Documents\",\n                                                                                    value: \"Other Documents\"\n                                                                                }\n                                                                            ],\n                                                                            className: \"flex-row gap-2 text-xs\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1629,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1628,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1584,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            (()=>{\n                                                                var _formValues_entries;\n                                                                const formValues = form.getValues();\n                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                const docAvailable = (entry === null || entry === void 0 ? void 0 : entry.docAvailable) || [];\n                                                                const hasOtherDocuments = docAvailable.includes(\"Other Documents\");\n                                                                return hasOtherDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Specify Other Documents\",\n                                                                        name: \"entries.\".concat(index, \".otherDocuments\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"Enter other document types...\",\n                                                                        className: \"max-w-md\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1660,\n                                                                        columnNumber: 33\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1659,\n                                                                    columnNumber: 31\n                                                                }, undefined) : null;\n                                                            })()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1577,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    (()=>{\n                                                        var _formValues_entries;\n                                                        const formValues = form.getValues();\n                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                        const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                        return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-3 border-t border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1687,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                \"Custom Fields (\",\n                                                                                customFields.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1688,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1686,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                    children: customFields.map((cf, cfIdx)=>{\n                                                                        const fieldType = cf.type || \"TEXT\";\n                                                                        const isAutoField = fieldType === \"AUTO\";\n                                                                        const autoOption = cf.autoOption;\n                                                                        let inputType = \"text\";\n                                                                        if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                            inputType = \"date\";\n                                                                        } else if (fieldType === \"NUMBER\") {\n                                                                            inputType = \"number\";\n                                                                        }\n                                                                        const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: fieldLabel,\n                                                                            name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                            type: inputType,\n                                                                            className: \"w-full\",\n                                                                            disable: isAutoField\n                                                                        }, cf.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1713,\n                                                                            columnNumber: 37\n                                                                        }, undefined);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1692,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1682,\n                                                            columnNumber: 29\n                                                        }, undefined) : null;\n                                                    })(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                                tabIndex: -1,\n                                                                                role: \"button\",\n                                                                                \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                                children: \"!\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1735,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1734,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                            side: \"top\",\n                                                                            align: \"center\",\n                                                                            className: \"z-[9999]\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm max-w-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium mb-1\",\n                                                                                        children: [\n                                                                                            \"Entry #\",\n                                                                                            index + 1,\n                                                                                            \" Filename\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1756,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined),\n                                                                                    filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"font-medium text-green-600 mb-2\",\n                                                                                                children: \"Filename Generated\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1761,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                                children: generatedFilenames[index]\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1764,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1760,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"font-medium text-orange-600 mb-1\",\n                                                                                                children: \"Please fill the form to generate filename\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1770,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs text-gray-600 mb-2\",\n                                                                                                children: \"Missing fields:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1774,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                className: \"list-disc list-inside space-y-1\",\n                                                                                                children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                        className: \"text-xs\",\n                                                                                                        children: field\n                                                                                                    }, fieldIndex, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                        lineNumber: 1780,\n                                                                                                        columnNumber: 45\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1777,\n                                                                                                columnNumber: 39\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1769,\n                                                                                        columnNumber: 37\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1755,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1750,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1733,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200\",\n                                                                    onClick: ()=>removeEntry(index),\n                                                                    disabled: fields.length <= 1,\n                                                                    tabIndex: -1,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-red-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1804,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1795,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                index === fields.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                className: \"h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200\",\n                                                                                onClick: addNewEntry,\n                                                                                tabIndex: -1,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1817,\n                                                                                    columnNumber: 37\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1809,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1808,\n                                                                            columnNumber: 33\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                            side: \"top\",\n                                                                            align: \"center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"Add New Entry (Shift+Enter)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1821,\n                                                                                columnNumber: 35\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1820,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1807,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1731,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1730,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, field.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1177,\n                                        columnNumber: 21\n                                    }, undefined);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            type: \"submit\",\n                                            className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                            children: \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1836,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1835,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1834,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1171,\n                            columnNumber: 17\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1170,\n                        columnNumber: 15\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                lineNumber: 1076,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n            lineNumber: 1075,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n        lineNumber: 1074,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"/0hgUI3kB+RCVJ/+JE4ikcrJex0=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFieldArray\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet);\nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx\n"));

/***/ })

});