"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateClient = void 0;
const operation_1 = require("../../../utils/operation");
const updateClient = async (req, res) => {
    const id = req.params.id;
    const { corporation_id } = req;
    const fields = {
        associateId: req.body.associateId,
        client_name: req.body.client_name,
        ownership_id: Number(req.body.ownership),
        branch_id: req.body.branch_id,
        corporation_id: Number(corporation_id)
    };
    await (0, operation_1.updateItem)({
        model: "client",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "client has been updated",
    });
};
exports.updateClient = updateClient;
//# sourceMappingURL=update.js.map