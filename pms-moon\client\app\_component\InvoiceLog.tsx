import React, { useState } from "react";
import ActivityLogEntry from "./ActivityLogEntry";
import { FaHistory } from "react-icons/fa";
import InvoiceLogEntry from "./InvoiceLogEntry";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import CloseDialog from "./CloseDialog";

interface ActivityLogProps {
  auditLogs: any;
}
const InvoiceLog: React.FC<ActivityLogProps> = ({ auditLogs }) => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Dialog open={open} onOpenChange={setOpen} >
        <DialogTrigger>
          <FaHistory className="h-5 w-5 text-main-color outline-none" />
        </DialogTrigger>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Are you absolutely sure?</DialogTitle>
            <CloseDialog setOpen={setOpen}  handleClick={()=>setOpen(false)}/>
            <DialogDescription>
              <div className="h-full py-4">
                <div className="">
                  {Array.isArray(auditLogs) &&
                    auditLogs.map((log: any, index: number) => (
                      <InvoiceLogEntry log={log} key={index} />
                    ))}
                </div>
              </div>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default InvoiceLog;
