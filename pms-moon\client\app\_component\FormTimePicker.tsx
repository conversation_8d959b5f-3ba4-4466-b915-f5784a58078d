
"use client"

import * as React from "react"
import { Clock } from "lucide-react"
import { format } from "date-fns"
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"

type FormTimePickerProps = {
  form: any
  name: string
  label: string
  placeholder?: string
  className?: string
  isRequired?: boolean
  isEntryPage?: boolean
  disable?: boolean
  onBlur?: (time: string) => void
}

const FormTimePicker = ({
  form,
  name,
  label,
  placeholder = "Select time",
  className,
  isRequired,
  isEntryPage,
  disable,
  onBlur,
}: FormTimePickerProps) => {
  const [open, setOpen] = React.useState(false)

  // Generate time slots in 10-minute intervals
  const timeSlots = React.useMemo(() => {
    const slots = []
    const totalMinutes = 24 * 60
    for (let i = 0; i < totalMinutes; i += 10) {
      const hours = Math.floor(i / 60)
      const minutes = i % 60
      const date = new Date()
      date.setHours(hours)
      date.setMinutes(minutes)
      slots.push(date)
    }
    return slots
  }, [])

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field }) => {
        const handleTimeChange = (time: string) => {
          field.onChange(time)
          if (onBlur) onBlur(time)
          setOpen(false)
        }

        const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          const value = e.target.value
          field.onChange(value)

          // Validate time format (12-hour with AM/PM)
          const timeRegex = /^(0?[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$/i
          if (timeRegex.test(value) && onBlur) {
            onBlur(value)
          }
        }

        return (
          <FormItem className={cn(isEntryPage ? " space-y-0.5 -mt-1" : "space-y-0.5 -mt-1", className)}>
            <FormLabel
              className={`${
                isEntryPage ? "md:text-xs" : "md:text-base"
              } text-gray-800 dark:text-gray-300 whitespace-nowrap cursor-text`}
            >
              {label}
              {isRequired && <span className="text-red-500">*</span>}
            </FormLabel>
            <FormControl>
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <div className="relative">
                    <Input
                      value={field.value || ""}
                      onChange={handleInputChange}
                      placeholder={placeholder}
                      disabled={disable}
                      className={cn(
                        "bg-gray-200 dark:bg-gray-700 border-none dark:border-gray-700",
                        "placeholder:text-gray-400 dark:placeholder:text-gray-100/50",
                        "outline-none focus:!outline-main-color pr-10",
                        isEntryPage ? "h-7 text-xs" : "",
                      )}
                    />
                    <Clock className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                  </div>
                </PopoverTrigger>
                {/* <PopoverContent className="w-[280px] p-0" align="start">
                  <ScrollArea className="h-[300px]">
                    <div className="grid grid-cols-4 gap-1 p-3">
                      {timeSlots.map((time, i) => (
                        <Button
                          key={i}
                          variant="ghost"
                          className="text-xs"
                          onClick={() => handleTimeChange(format(time, "h:mm aa"))}
                        >
                          {format(time, "h:mm aa")}
                        </Button>
                      ))}
                    </div>
                  </ScrollArea>
                </PopoverContent> */}
              </Popover>
            </FormControl>
            <FormMessage className={`${isEntryPage ? "text-xs tracking-wider" : "tracking-wider"}`} />
          </FormItem>
        )
      }}
    />
  )
}

export default FormTimePicker


