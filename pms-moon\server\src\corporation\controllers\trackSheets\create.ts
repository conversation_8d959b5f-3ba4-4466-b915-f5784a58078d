import { handleError } from "../../../utils/helpers";

export const trackSheetService = async (trackSheetData: any) => {
  try {
    const createdTrackSheet = await prisma.trackSheets.create({
      data: trackSheetData,
    });
    return createdTrackSheet;
  } catch (error) {
    throw error;
  }
};

export const customFieldsService = async (trackSheetId, customFieldsData) => {
  try {
    const mappings = [];

    for (const customField of customFieldsData) {
      const mapping = await prisma.trackSheetCustomFieldMapping.create({
        data: {
          tracksheetId: trackSheetId,
          customFieldId: customField.id,
          value: customField.value,
        },
      });
      mappings.push(mapping);
    }
    return mappings;
  } catch (error) {
    throw error;
  }
};

export const createTrackSheets = async (req, res) => {
  try {
    const { entries } = req.body;
console.log("data: ", req.body);
    for(const entry of entries) {
      if (entry.invoice) {
        const existingInvoice = await prisma.trackSheets.findFirst({
          where: {
            invoice: entry.invoice,
          },
        });
        if (existingInvoice) {
          return res.status(400).json({
            success: false,
            message: "Invoice already exists",
          });
        } 
      }
    }
    const createdTrackSheets = [];

    for (const entry of req.body.entries) {
      const { customFields, ...entryFields } = entry;

      const preparedTrackSheetData = {
        ...entryFields,
        clientId: Number(req.body.clientId),
        carrierId: entryFields.carrierId ? Number(entryFields.carrierId) : null,
        qtyShipped: entryFields.qtyShipped
          ? Number(entryFields.qtyShipped)
          : null,
        invoiceTotal: entryFields.invoiceTotal
          ? Number(entryFields.invoiceTotal)
          : null,
        invoiceDate: new Date(entryFields.invoiceDate),
        receivedDate: new Date(entryFields.receivedDate),
        shipmentDate: new Date(entryFields.shipmentDate),
        billToClient: entryFields.billToClient || null,
        docAvailable: Array.isArray(entryFields.docAvailable)
          ? entryFields.docAvailable.join(",")
          : entryFields.docAvailable || null,
      };

      Object.keys(preparedTrackSheetData).forEach((key) => {
        if (preparedTrackSheetData[key] === undefined) {
          delete preparedTrackSheetData[key];
        }
      });

      const createdTrackSheet = await trackSheetService(preparedTrackSheetData);
      createdTrackSheets.push(createdTrackSheet);

      if (
        customFields &&
        Array.isArray(customFields) &&
        customFields.length > 0
      ) {
        await customFieldsService(createdTrackSheet.id, customFields);
      }
    }

    return res.status(201).json({
      success: true,
      message: `${createdTrackSheets.length} TrackSheet(s) created successfully`,
      data: createdTrackSheets,
    });
  } catch (error) {
    console.error("Error creating tracksheet:", error);
    return handleError(res, error);
  }
};