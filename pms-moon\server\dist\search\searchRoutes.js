"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../middleware/authentication");
const genericSearchController_1 = require("./genericSearchController");
const router = (0, express_1.Router)();
router.get("/:model", authentication_1.authenticate, genericSearchController_1.genericSearchController);
exports.default = router;
//# sourceMappingURL=searchRoutes.js.map