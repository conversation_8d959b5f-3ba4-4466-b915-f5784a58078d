import { handleError } from "../../../utils/helpers";

export const viewClientCarrier = async (req, res) => {
  try {
    const data = await prisma.clientCarrier.findMany({
      include: {
        client: true,
        carrier: true,
      },
      orderBy: {
        id:'desc'
      }
    }
    );
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewClientCarrierById = async (req, res) => {
  const { id } = req.params;
  try {
    const data = await prisma.clientCarrier.findMany({
      where: {
        client_id : Number(id),
      },
      include: {
        client: true,
        carrier: true,
      },
      orderBy:{
        id:'desc'
      }
    }
    );
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(400).json([]);
  } catch (error) {
    return handleError(res, error);
  }
};
