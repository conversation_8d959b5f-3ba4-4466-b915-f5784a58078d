import React, { useEffect, useState } from "react";

import { AddTask } from "./AddTask";
import { SelectClient } from "./SelectClient";
import { Car } from "lucide-react";
import ViewWorkRecord from "./ViewWorkRecord";
import {
  carrier_routes,
  client_routes,
  employee_routes,
  workreport_routes,
  worktype_routes,
} from "@/lib/routePath";
import { getAllData, getCookie } from "@/lib/helpers";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import Sidebar from "@/components/sidebar/Sidebar";
import { NavBar } from "@/app/_component/NavBar";
import Parent from "./parent";
import Manage from "./manage";
import { useSearchParams } from "next/navigation";

const Page = async ({
  searchParams,
}: {
  searchParams: {
    pageSize?: string;
    page?: string;
    fDate?: string;
    tDate: string;
    Username: string;
    Client: string;
    Carrier: string;
    "Work Type": string;
    Category: string;
    Type: string;
    // StartTime: string;
    // EndTime: string;
    Workstatus: string;
    // TimeSpent: string;
    "Actual No": string;
    Notes: string;
  };
}) => {
  const {
    page = "1",
    pageSize = "50",
    fDate,
    tDate,
    Username,
    Client,
    Carrier,
    ["Work Type"]: WorkType,
    Category,
    Type,
    // StartTime,
    // EndTime,
    Workstatus,
    // TimeSpent,
    "Actual No": ActualNumber,
    Notes,
  } = searchParams;

  const params = new URLSearchParams();

  if (pageSize) params.append("pageSize", pageSize);
  if (page) params.append("page", page);
  if (Username) params.append("Username", Username);
  if (Client) params.append("Client", Client);
  if (Carrier) params.append("Carrier", Carrier);
  if (WorkType) params.append("WorkType", WorkType);
  if (Category) params.append("Category", Category);
  if (Type) params.append("Type", Type);
  // if (StartTime) params.append("StartTime", StartTime);
  // if (EndTime) params.append("EndTime", EndTime);
  if (Workstatus) params.append("Workstatus", Workstatus);
  // if (TimeSpent) params.append("TimeSpent", TimeSpent);
  if (ActualNumber) params.append("ActualNumber", ActualNumber);
  if (Notes) params.append("Notes", Notes);
  if (fDate && tDate) {
    params.append("fDate", fDate);
    params.append("tDate", tDate);
  }

  const apiUrl = `${
    workreport_routes.GET_CURRENT_USER_WORKREPORT
  }?${params.toString()}`;

  // Start measuring time
  console.time("Time taken for: " + apiUrl);
  const WorkData = await getAllData(apiUrl);
  const pageSizes = 100;
  const pages = 1;
  console.timeEnd("Time taken for: " + apiUrl);

  // End measuring time and log the result
  const workTypes = await getAllData(worktype_routes.GETALL_WORKTYPE);
  const allClient = await getAllData(
    `${client_routes.GETALL_CLIENT}?pageSize=${pageSizes}&page=${pages}`
  );
  const allUser = await getAllData(employee_routes.GETALL_USERS);
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions = userData?.role?.role_permission || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  const actions = permissions?.map((item) => item?.permission?.action);

  return (
    <>
      <Manage
        permissions={permissions}
        userData={userData}
        WorkData={WorkData}
        workTypes={workTypes}
        allClient={allClient?.data}
        actions={actions}
        params={params}
        allUser={allUser?.data}
      />
    </>
  );
};

export default Page;
