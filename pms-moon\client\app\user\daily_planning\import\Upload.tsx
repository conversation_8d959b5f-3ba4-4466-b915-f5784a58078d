import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import React from "react";
import SampleFile from "./SampleFile";
import UploadFile from "./UploadFile";

const Upload = () => {
  return (
    <div className="pt-4">
      
      <div className="p-4">
        <h2 className="text-3xl">Upload data</h2>
        <p className="text-gray-700">Here you can upload your data</p>
      </div>
      {/* <div className="w-full flex justify-end p-2">
        <SampleFile />
      </div> */}
      <div className="p-4">
        
        <UploadFile />
      </div>
    </div>
  );
};

export default Upload;
