import { <PERSON><PERSON> } from "@/components/ui/button";
import { Plus, Download } from "lucide-react";
import { toast } from "sonner";
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>ontent,
  <PERSON><PERSON>D<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { formSubmit } from "@/lib/helpers";
import {
  daily_planning_details_routes,
  employee_routes,
} from "@/lib/routePath";
import { useParams, useRouter } from "next/navigation";
import { useState } from "react";
import * as XLSX from "xlsx";

export function Import({ selectType, userData }: any) {
  const [errors, setErrors] = useState([]);
  const { id } = useParams();
  const [openTab, setOpenTab] = useState(false);
  const router = useRouter();
  const handleExcelUpload = async (e: any) => {
    const file = e.target.files[0];

    if (!file) {
      toast.error("Please select an Excel file to upload");
      return;
    }

    // Clear previous errors
    setErrors([]);

    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", selectType);
    formData.append("username", userData.username);
    try {
      const response = await fetch(
        `${daily_planning_details_routes.EXCEL_DAILY_PLANNING_DETAILS}/${id}`,
        { method: "POST", body: formData }
      );

      const data = await response.json();
      
      if (response.ok) {
        if (data.errors && data.errors.length > 0) {
          setErrors(data.errors);
        } else {
          toast.success(data.message);
          router.push("/user/daily_planning");
        }
      } else {
        setErrors([data.error || "Failed to upload Excel file"]);
      }
      router.refresh();
    } catch (error) {
      console.error("Error importing Excel:", error);
      setErrors(["Error importing Excel file"]);
    }
  };

  const handleDownloadTemplate = async () => {
    try {
      const templateFileName = selectType === 'INVOICE_ENTRY_STATUS' 
        ? 'invoice_template.xls' 
        : 'statement_template.xls';

      const response = await fetch(`/${templateFileName}`);
      const arrayBuffer = await response.arrayBuffer();

      const wb = XLSX.read(arrayBuffer, { type: "array" });

      const ws = wb.Sheets[wb.SheetNames[0]];

      const data = XLSX.utils.sheet_to_json(ws, { header: 1 });

      if (data.length > 1) {
        const secondRow: string[] = data[1] as string[];

        secondRow[0] = selectType;

        XLSX.utils.sheet_add_aoa(ws, [secondRow], { origin: "A2" });

        const file = XLSX.write(wb, { bookType: "xlsx", type: "binary" });

        const blob = new Blob([s2ab(file)], {
          type: "application/octet-stream",
        });
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = templateFileName;
        link.click();
      }
    } catch (error) {
      console.error("Error modifying the template:", error);
      setErrors(["Error modifying the template"]);
    }
  };

  const s2ab = (s: string) => {
    const buf = new ArrayBuffer(s.length);
    const view = new Uint8Array(buf);
    for (let i = 0; i < s.length; i++) {
      view[i] = s.charCodeAt(i) & 0xff;
    }
    return buf;
  };

  return (
    <Sheet open={openTab} onOpenChange={(open) => {
      setOpenTab(open);
      if (!open) {
        setErrors([]); 
      }
    }}>
      <SheetTrigger asChild>
        <Button
          variant="outline"
          className="ml-2 py-1.5 gap-2 bg-white border hover:to-main-color-foreground px-3 justify-center text-black uppercase font-semibold rounded-md flex items-center text-sm absolute top-[4.3rem] right-60"
          onClick={() => {
            setOpenTab(true);
            setErrors([]); 
          }}
        >
          <Plus className="w-5 h-5 font-extrabold" />
          Import
        </Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Import Daily planning details</SheetTitle>
          <SheetDescription></SheetDescription>
        </SheetHeader>
        <Button
          variant="secondary"
          className="mb-4 flex gap-2"
          onClick={() => {
            setErrors([]); 
            handleDownloadTemplate();
          }}
        >
          <Download className="w-5 h-5" />
          Download Template
        </Button>

        <input
          type="file"
          name="file"
          accept=".xlsx,.xls"
          id="excel-upload"
          className="w-96 mb-2"
          hidden
          onChange={handleExcelUpload}
        />
        <Button
          onClick={() => {
            setErrors([]); 
            document.getElementById("excel-upload")?.click();
          }}
        >
          Select Excel File
        </Button>

        {errors.length > 0 && (
          <div className="text-red-500 text-sm mt-2">
            {errors.map((error, index) => (
              <p key={index} className="mt-2 bg-red-200 rounded-md p-2">
                {error}
              </p>
            ))}
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
