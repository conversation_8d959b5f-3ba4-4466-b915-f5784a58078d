"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteBranch = void 0;
const operation_1 = require("../../../utils/operation");
const deleteBranch = async (req, res) => {
    const { id } = req.params;
    await (0, operation_1.deleteItem)({
        model: "branch",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "branch has been deleted",
    });
};
exports.deleteBranch = deleteBranch;
//# sourceMappingURL=delete.js.map