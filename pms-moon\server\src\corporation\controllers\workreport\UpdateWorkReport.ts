import { TaskType } from "@prisma/client";
import { updateItem } from "../../../utils/operation";
import { time } from "node:console";

export const updateWorkReports = async (req, res) => {
  const id = Number(req.params.id);
  const userId = req.user_id;
  const startTime = new Date(req.body.start_time);
  const endTime = new Date(req.body.end_time);

  const duration = ((endTime as any) - (startTime as any)) / (1000 * 60); // Convert ms to minutes
  const workReport = await prisma.workReport.findUnique({
    where: {
      id: Number(id),
    },
    select: { date: true },
  });
  const existingWorkReport = await prisma.workReport.findFirst({
    where: {
      user_id: userId,
      date: new Date(workReport.date),
      NOT: { id },
      OR: [
        {
          AND: [
            { start_time: { lt: endTime } }, // Existing start_time is before new endTime
            { finish_time: { gt: startTime } }, // Existing finish_time is after new startTime
          ],
        },
      ],
    },
  });

  if (existingWorkReport) {
    return res.status(400).json({
      success: false,
      message: "You already have an entry for this time.",
    });
  }

  const fields = {
    start_time: startTime,
    finish_time: endTime,
    time_spent: duration,
  };

  await updateItem({
    model: "WorkReport",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "workreport has been updated",
  });
};
