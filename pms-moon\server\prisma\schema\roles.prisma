model Roles {
  id              Int              @id @default(autoincrement())
  name            String           @db.Var<PERSON>har()
  corporation_id  Int
  client_id       Int?
  created_at      DateTime         @default(now()) @db.Timestamptz(6)
  updated_at      DateTime         @updatedAt @db.Timestamptz()
  corporation     Corporation      @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade, onUpdate: NoAction)
  permissions     Permissions[]
  role_permission RolePermission[]
  User            User[]
}

model Permissions {
  id              Int              @id @default(autoincrement())
  module          String           @db.VarChar()
  action          String           @db.VarChar()
  created_at      DateTime         @default(now()) @db.Timestamptz(6)
  updated_at      DateTime         @updatedAt @db.Timestamptz()
  roles           Roles[]
  role_permission RolePermission[]
}

model RolePermission {
  id            Int         @id @default(autoincrement())
  role_id       Int
  permission_id Int
  role          Roles       @relation(fields: [role_id], references: [id], onDelete: Cascade)
  permission    Permissions @relation(fields: [permission_id], references: [id])
  created_at    DateTime    @default(now())
  updated_at    DateTime    @default(now()) @updatedAt
}
