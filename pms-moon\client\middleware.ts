import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { getAllData } from "./lib/helpers";
import { employee_routes } from "./lib/routePath";

const authentication = async (req: NextRequest) => {
  const superadmintoken = cookies().get("superadmintoken");
  const corporationtoken = cookies().get("corporationtoken");
  const usertoken = cookies().get("token");

  const { pathname } = req.nextUrl;

  // Redirect authenticated users from "/" or "/admin" based on their token
  // if (pathname === "/" || pathname === "/admin") {
  //   if (superadmintoken) {
  //     return NextResponse.redirect(
  //       new URL("/superadmin", req.nextUrl.origin)
  //     );
  //   }
  //   if (corporationtoken ) {
  //     return NextResponse.redirect(
  //       new URL("/pms/dashboard", req.nextUrl.origin)
  //     );
  //   }
  //   if (usertoken) {
  //     return NextResponse.redirect(
  //       new URL("/user/tracker", req.nextUrl.origin)
  //     );
  //   }
  // }

  if (req.nextUrl.pathname.startsWith("/superadmin")) {
    if (superadmintoken) {
      return NextResponse.next();
    }
    return NextResponse.redirect(
      new URL("/superadmin-login", req.nextUrl.origin)
    );
  }
  if (corporationtoken) {
    return NextResponse.next();
  }
  if (req.nextUrl.pathname.startsWith("/pms")) {
    if (usertoken) {
      const userData = await getAllData(employee_routes.GETCURRENT_USER);
      const userPermissions =
        userData?.role?.role_permission.map(
          (item: any) => item.permission.module
        ) || [];

      const userAllowedRoutes: { [key: string]: string[] } = {
        "/pms/manage_carrier": ["CARRIER MANAGEMENT"],
        "/pms/manage_client": ["CLIENT MANAGEMENT"],
        "/pms/manage_employee": ["USER MANAGEMENT"],
        "/pms/manage_work_report": ["WORK REPORT"],
        "/pms/manage_work_type": ["WORKTYPE MANAGEMENT"],
        "/pms/manage-roles": ["ROLE MANAGEMENT"],
        "/pms/dashboard": ["DASHBOARD"],
        "/pms/manage_category": ["CATEGORY MANAGEMENT"],
        "/pms/manage_client/manage_client_carrier": [
          "CLIENT-CARRIER MANAGEMENT",
        ],
        "/pms/manage_branch": ["BRANCH MANAGEMENT"],
      };

      const { pathname } = req.nextUrl;

      // Extract the base path by removing the last segment if it's an ID
      const basePath = pathname.replace(/\/\d+$/, ""); // Remove trailing /{id} if exists
      const basePathWithoutEdit = basePath.replace(/\/edit$/, ""); // Remove /edit if exists

      // Find required permissions
      const requiredPermissions =
        userAllowedRoutes[basePath] ||
        userAllowedRoutes[basePathWithoutEdit] ||
        null;

      if (requiredPermissions) {
        const hasPermission = requiredPermissions.some((perm) =>
          userPermissions.includes(perm)
        );
        

        if (!hasPermission) {
          return NextResponse.redirect(
            new URL("/not-authorized", req.nextUrl.origin)
          );
        }
      }

      return NextResponse.next();
    }

    if (!corporationtoken) {
      return NextResponse.redirect(new URL("/login", req.url));
    }
  }

  if (req.nextUrl.pathname.startsWith("/user")) {
    if (usertoken) {
      const userData = await getAllData(employee_routes.GETCURRENT_USER);
    
      const userPermissions =
        userData?.role?.role_permission.map(
          (item: any) => item.permission.action
        ) || [];

      const userAllowedRoutes: { [key: string]: string[] } = {
        "/user/tracker": ["TRACKER"],
        "/user/daily_planning": ["DAILY PLANNING"],
        "/user/daily_planning/add_dailyplanning_details": [
          "add-dailyPlanningDetails",
        ],
        "/user/daily_planning/import": ["upload-dailyPlanningDetails"],
      };

      const { pathname } = req.nextUrl;

      // Extract the base path by removing dynamic ID or "/edit"
      const basePath = pathname.replace(/\/\d+$/, ""); // Remove trailing /{id} if exists
      const basePathWithoutEdit = basePath.replace(/\/edit$/, ""); // Remove /edit if exists

      // Find required permissions
      const requiredPermissions =
        userAllowedRoutes[basePath] ||
        userAllowedRoutes[basePathWithoutEdit] ||
        null;

      if (requiredPermissions) {
        const hasPermission = requiredPermissions.some((perm) =>
          userPermissions.includes(perm)
        );

        if (!hasPermission) {
          return NextResponse.redirect(
            new URL("/not-authorized", req.nextUrl.origin)
          );
        }
      }

      return NextResponse.next();
    }

    return NextResponse.redirect(new URL("/", req.url));
  }

  return NextResponse.next();
};

export const config = {
  matcher: ["/","/admin","/superadmin/:path*", "/pms/:path*", "/user/:path*"],
};

export default authentication;
