"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateWorktype = void 0;
const operation_1 = require("../../../utils/operation");
const updateWorktype = async (req, res) => {
    const id = req.params.id;
    const fields = {
        work_type: req.body.work_type,
        category_id: Number(req.body.category),
        is_work_carrier_specific: req.body.is_work_carrier_specific,
        does_it_require_planning_number: req.body.does_it_require_planning_number,
        is_backlog_regular_required: req.body.is_backlog_regular_required,
    };
    await (0, operation_1.updateItem)({
        model: "workType",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "work Type has been updated",
    });
};
exports.updateWorktype = updateWorktype;
//# sourceMappingURL=update.js.map