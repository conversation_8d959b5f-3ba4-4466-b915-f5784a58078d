"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewCategory = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewCategory = async (req, res) => {
    try {
        const data = await prisma.category.findMany({
            orderBy: {
                id: "desc",
            },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewCategory = viewCategory;
//# sourceMappingURL=view.js.map