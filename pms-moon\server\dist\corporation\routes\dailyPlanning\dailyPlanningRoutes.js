"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const create_1 = require("../../controllers/dailyPlanning/create");
const view_1 = require("../../controllers/dailyPlanning/view");
const update_1 = require("../../controllers/dailyPlanning/update");
const delete_1 = require("../../controllers/dailyPlanning/delete");
const router = (0, express_1.Router)();
router.post("/create-dailyplanning", authentication_1.authenticate, create_1.createDailyPlanning);
router.get("/get-all-dailyplanning", authentication_1.authenticate, view_1.viewDailyPlanning);
router.get("/get-specific-dailyplanning", authentication_1.authenticate, view_1.viewSpecificDailyPlanning);
router.get("/get-dailyplanning-by-id/:id", view_1.viewDailyPlanningById);
router.put("/update-dailyplanning/:id", authentication_1.authenticate, update_1.updateDailyPlanning);
router.delete("/delete-dailyplanning/:id", delete_1.deleteDailyPlanning);
exports.default = router;
//# sourceMappingURL=dailyPlanningRoutes.js.map