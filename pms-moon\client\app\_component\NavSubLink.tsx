"use client";
import { usePathname, useRouter } from "next/navigation";
import React from "react";

type LinkProps = {
  label: string;
  path: string;
  open?: boolean;
};

const NavSubLink = ({ label, path, open }: LinkProps) => {
    const { push } = useRouter();
    const pathname = usePathname();
  return (
   <>
  
  <div
      className={
        pathname === path
          ? `${
              open
                ? "flex py-4 p-2 px-2 items-center justify-center w-full gap-2 cursor-pointer"
                : "flex flex-col  text-center space-y-1 py-1  gap-2 items-center justify-center cursor-pointer  px-2"
            }     `
          : `${
              open
                ? "flex py-4 p-2 px-5 items-center justify-center w-full gap-2 cursor-pointer"
                : "flex flex-col  text-center space-y-1 py-1 items-center justify-center gap-2 cursor-pointer px-2"
            }`
      }
      onClick={() => push(path)}
    >
      <p
        className={
          pathname === path
            ? `${
                open ? "text-md" : "text-sm"
              } w-full  text-main-color text-[0.85rem] capitalize  tracking-wider text-sm`
            : `${
                open ? "text-md" : "text-sm"
              } w-full  text-gray-600 capitalize tracking-wider  group-hover:text-main-color dark:text-white text-[0.85rem]`
        }
      >
        {label}
      </p>
    </div>

   </>
  )
}

export default NavSubLink
