import { createItem } from "../../../utils/operation";

export const createBranch = async (req, res) => {
  const { corporation_id } = req;
  const fields = {
    branch_name: req.body.name,
    corporation_id: Number(corporation_id),
  };
  await createItem({
    model: "Branch",
    fieldName: "id",
    fields: fields,
    res: res as Response,
    req: req,
    successMessage: "branch has been created",
  });
};