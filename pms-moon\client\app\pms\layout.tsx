import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/sonner";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";

import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { getAllData, getCookie } from "@/lib/helpers";
import { employee_routes } from "@/lib/routePath";
import Sidebar from "@/components/sidebar/Sidebar";


const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "Dashboard",
};

export default async function  AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const currentUser = await getAllData(employee_routes.GETCURRENT_USER)
  
  const userPermissions = currentUser?.role?.role_permission || [];
//  (currentUser)
  const adminCookie = await getCookie("corporationtoken");
  const permission = adminCookie ? ["allow_all"] : userPermissions;
  //  (adminCookie)
  //  (permission)
  return (
    <SidebarProvider>
      <Sidebar permissions={permission} profile={currentUser} />
      {/* <main className="pt-1">
        <SidebarTrigger />
      </main> */}
      <div className={`${inter.className} w-full rounded-xl}`}>
        {children}
        <Toaster position="top-right" richColors />
      </div>
    </SidebarProvider>
  );
}
