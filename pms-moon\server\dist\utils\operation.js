"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createManyItemsCustomFields = exports.createItemCustomFields = exports.createManyItems = exports.updateTransaction = exports.createTransactions = exports.getItem = exports.deleteItem = exports.updateItem = exports.createItem = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const helpers_1 = require("./helpers");
const prismaClient_1 = __importDefault(require("./prismaClient"));
const createItem = async ({ model, fieldName, fields, res, req, successMessage, }) => {
    try {
        let completeField = { ...fields };
        const criteria = Object.fromEntries(Object.entries({
            ...completeField,
        }));
        const errorMessage = `${model} already exists`;
        const existingRowerror = await (0, helpers_1.checkExistingRow)({
            model,
            criteria,
            res,
            errorMessage,
        });
        if (existingRowerror)
            return existingRowerror;
        if (completeField.password) {
            const hashedPassword = bcrypt_1.default.hashSync(completeField.password, 10);
            completeField.password = hashedPassword;
        }
        const data = await prismaClient_1.default[model].create({
            data: completeField,
        });
        return res.status(201).json({ success: true, message: successMessage });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.createItem = createItem;
const updateItem = async ({ model, fieldName, fields, id, res, req, successMessage, }) => {
    try {
        const currentRecord = await prismaClient_1.default[model].findUnique({
            where: {
                [fieldName]: id,
            },
        });
        if (!currentRecord) {
            return res.status(404).json({
                success: false,
                message: `${model} doesn't exist`,
            });
        }
        const completeField = { ...fields };
        const criteria = Object.fromEntries(Object.entries({
            ...completeField,
        }));
        const isChanged = Object.keys(fields).some((key) => fields[key] !== currentRecord[key]);
        if (isChanged) {
            let existingRecods;
            if (completeField.username || completeField.email) {
                existingRecods = await prismaClient_1.default[model].findFirst({
                    where: {
                        AND: [
                            { [fieldName]: { not: id } },
                            { OR: [{ username: completeField.username }, { email: completeField.email }] }
                        ],
                    },
                });
            }
            else {
                existingRecods = await prismaClient_1.default[model].findFirst({
                    where: {
                        AND: [{ [fieldName]: { not: id } }, criteria],
                    },
                });
            }
            if (existingRecods) {
                return res
                    .status(400)
                    .json({ success: false, message: `${model} already exist` });
            }
        }
        const result = await prismaClient_1.default[model].update({
            where: {
                [fieldName]: id,
            },
            data: {
                ...fields,
            },
        });
        return res.status(200).json({
            success: true,
            message: successMessage,
            data: result,
        });
    }
    catch (error) {
        console.log(error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.updateItem = updateItem;
const deleteItem = async ({ model, fieldName, id, res, req, successMessage, }) => {
    try {
        const existingRecord = await prismaClient_1.default[model].findUnique({
            where: { [fieldName]: id },
        });
        if (!existingRecord) {
            return res
                .status(400)
                .json({ success: false, message: "No such Record" });
        }
        await prismaClient_1.default[model].delete({
            where: { [fieldName]: id },
        });
        return res.status(200).json({ success: true, message: successMessage });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.deleteItem = deleteItem;
const getItem = async ({ model, params, res, }) => {
    return await (0, helpers_1.findMany)({
        model,
        params,
        res,
        // postProcess: async (data) => await enrichWithAuditLogs(data, model),
    });
};
exports.getItem = getItem;
const createTransactions = async ({ model01, fieldName01, fields01, logging_relationship01, model02, fieldName02, fields02, logging_relationship02, model03, fieldName03, fields03, logging_relationship03, res, req, successMessage, }) => {
    try {
        const { freightadminid, freightuserid } = req;
        const user_role = freightadminid && freightuserid ? "FREIGHT_ADMIN_USER" : "FREIGHT_ADMIN";
        const userId = freightadminid && freightuserid ? freightuserid : freightadminid;
        await prismaClient_1.default.$transaction(async (tx) => {
            const result = await tx[model01].create({
                data: {
                    ...fields01,
                },
            });
            const id = result[fieldName01];
            for (let i = 0; i < fields02.length; i++) {
                await tx[model02].create({
                    data: {
                        ...fields02[i],
                        [fieldName01]: id,
                    },
                });
            }
            return res.status(200).json({
                success: true,
                message: successMessage,
            });
        });
    }
    catch (error) {
        (0, helpers_1.handleError)(res, error);
    }
};
exports.createTransactions = createTransactions;
const updateTransaction = async ({ model01, fieldName01, fields01, logging_relationship01, model02, fieldName02, fields02, logging_relationship02, model03, fieldName03, fields03, logging_relationship03, res, req, successMessage, }) => {
    try {
        const { freightadminid, freightuserid } = req;
        const user_role = freightadminid && freightuserid ? "FREIGHT_ADMIN_USER" : "FREIGHT_ADMIN";
        const userId = freightadminid && freightuserid ? freightuserid : freightadminid;
        await prismaClient_1.default.$transaction(async (tx) => {
            const result = await tx[model01].update({
                data: {
                    ...fields01,
                },
                where: {
                    [fieldName01]: fields01[fieldName01],
                },
            });
            const id = result[fieldName01];
            await (0, helpers_1.DeleteMany)({
                model: model02,
                where_condition: { [fieldName01]: id },
                res,
            });
            for (let i = 0; i < fields02.length; i++) {
                await tx[model02].create({
                    data: {
                        ...fields02[i],
                        [fieldName01]: id,
                    },
                });
            }
            return res.status(200).json({
                success: true,
                message: successMessage,
            });
        });
    }
    catch (error) {
        (0, helpers_1.handleError)(res, error);
    }
};
exports.updateTransaction = updateTransaction;
const createManyItems = async ({ model, fieldsArray, // Array of field objects
res, req, successMessage, }) => {
    try {
        const createdItems = []; // Store created items
        for (const fields of fieldsArray) {
            let completeField = { ...fields };
            const criteria = Object.fromEntries(Object.entries({
                ...completeField,
            }));
            const errorMessage = `${model} already exists`;
            const existingRowError = await (0, helpers_1.checkExistingRow)({
                model,
                criteria,
                res,
                errorMessage,
            });
            if (existingRowError) {
                return existingRowError; // Exit early if an existing row is found
            }
            if (completeField.password) {
                const hashedPassword = bcrypt_1.default.hashSync(completeField.password, 10);
                completeField.password = hashedPassword;
            }
            const data = await prismaClient_1.default[model].create({
                data: completeField,
            });
            createdItems.push(data); // Add created item to the results array
        }
        return res
            .status(200)
            .json({ success: true, message: successMessage, createdItems });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.createManyItems = createManyItems;
const createItemCustomFields = async ({ model, fieldName, fields, res, req, successMessage, }) => {
    try {
        let completeField = { ...fields };
        // Exclude customFields from criteria to avoid Prisma JSON filter error
        const { customFields, ...criteriaFields } = completeField;
        const criteria = Object.fromEntries(Object.entries({
            ...criteriaFields,
        }));
        const errorMessage = `${model} already exists`;
        const existingRowerror = await (0, helpers_1.checkExistingRow)({
            model,
            criteria,
            res,
            errorMessage,
        });
        if (existingRowerror)
            return existingRowerror;
        if (completeField.password) {
            const hashedPassword = bcrypt_1.default.hashSync(completeField.password, 10);
            completeField.password = hashedPassword;
        }
        const data = await prismaClient_1.default[model].create({
            data: completeField,
        });
        return res.status(201).json({ success: true, message: successMessage });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.createItemCustomFields = createItemCustomFields;
const createManyItemsCustomFields = async ({ model, fieldsArray, // Array of field objects
res, req, successMessage, }) => {
    try {
        const createdItems = []; // Store created items
        for (const fields of fieldsArray) {
            let completeField = { ...fields };
            // Exclude customFields from criteria to avoid Prisma JSON filter error
            const { customFields, ...criteriaFields } = completeField;
            const criteria = Object.fromEntries(Object.entries({
                ...criteriaFields,
            }));
            const errorMessage = `${model} already exists`;
            const existingRowError = await (0, helpers_1.checkExistingRow)({
                model,
                criteria,
                res,
                errorMessage,
            });
            if (existingRowError) {
                return existingRowError; // Exit early if an existing row is found
            }
            if (completeField.password) {
                const hashedPassword = bcrypt_1.default.hashSync(completeField.password, 10);
                completeField.password = hashedPassword;
            }
            const data = await prismaClient_1.default[model].create({
                data: completeField,
            });
            createdItems.push(data); // Add created item to the results array
        }
        return res
            .status(200)
            .json({ success: true, message: successMessage, createdItems });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.createManyItemsCustomFields = createManyItemsCustomFields;
//# sourceMappingURL=operation.js.map