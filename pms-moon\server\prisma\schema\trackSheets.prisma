model TrackSheets {
    id                           Int                            @id @default(autoincrement())
    clientId                     Int?
    client                       Client?                        @relation(fields: [clientId], references: [id], onDelete: Cascade)
    company                      String?
    division                     String?
    masterInvoice                String?                        @db.VarChar()
    invoice                      String?                        @unique @db.VarChar()
    bol                          String?                        @db.VarChar()
    invoiceDate                  DateTime?
    receivedDate                 DateTime?
    shipmentDate                 DateTime?
    carrierId                    Int?
    carrier                      Carrier?                       @relation(fields: [carrierId], references: [id], onDelete: Cascade)
    invoiceStatus                String?
    manualMatching               String?
    invoiceType                  String?
    currency                     String?
    qtyShipped                   Int?
    weightUnitName               String?
    quantityBilledText           String?                        @db.VarChar()
    invoiceTotal                 Int?
    savings                      String?                        @db.VarChar()
    financialNotes               String?                        @db.VarChar()
    ftpFileName                  String?                        @db.VarChar()
    ftpPage                      String?                        @db.VarChar()
    filePath                     String?                        @db.Var<PERSON>har()
    billToClient                 Boolean?                       @default(false)
    docAvailable                 String?
    notes                        String?                        @db.VarChar()
    mistake                      String?                        @db.VarChar()
    createdAt                    DateTime                       @default(now())
    updatedAt                    DateTime                       @default(now()) @updatedAt
    TrackSheetCustomFieldMapping TrackSheetCustomFieldMapping[]

    @@map("track_sheets")
}
