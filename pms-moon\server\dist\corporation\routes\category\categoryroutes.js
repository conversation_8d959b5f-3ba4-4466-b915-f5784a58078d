"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const checkPermission_1 = require("../../../middleware/checkPermission");
const create_1 = require("../../controllers/category/create");
const view_1 = require("../../controllers/category/view");
const update_1 = require("../../controllers/category/update");
const delete_1 = require("../../controllers/category/delete");
const router = (0, express_1.Router)();
router.post("/create-category", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CATEGORY MANAGEMENT", "create-category"), create_1.createCategory);
router.get("/get-all-category", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CATEGORY MANAGEMENT", "view-category"), view_1.viewCategory);
router.put("/update-category/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CATEGORY MANAGEMENT", "update-category"), update_1.updateCategory);
router.delete("/delete-category/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("CATEGORY MANAGEMENT", "delete-category"), delete_1.deleteCategory);
exports.default = router;
//# sourceMappingURL=categoryroutes.js.map