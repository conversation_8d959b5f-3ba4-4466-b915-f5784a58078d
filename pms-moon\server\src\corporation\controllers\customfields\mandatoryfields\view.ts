import { Request, Response } from "express";
import { PrismaClient, FieldType } from "@prisma/client";

const prisma = new PrismaClient();

export interface AuthenticatedRequest extends Request {
  user?: { username: string };
}

// --- GET: Fetch Mandatory Fields ---
export const mandatoryFields = async (req: Request, res: Response) => {
  try {
    const result = await prisma.$queryRawUnsafe<{ column_name: string }[]>(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'track_sheets'
        AND table_schema = 'public'
    `);
    const excludedFields = ["createdAt", "updatedAt", "id"];

    const fields = result
      .map((col) => col.column_name)
      .filter((field) => !excludedFields.includes(field))
      .map((field) =>
        field
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ")
      )
      .sort((a, b) => a.localeCompare(b));

    res.json({ mandatoryFields: fields });
  } catch (err) {
    console.error("Error fetching TrackSheets columns:", err);
    res.status(500).json({ error: "Failed to get mandatory fields" });
  }
};