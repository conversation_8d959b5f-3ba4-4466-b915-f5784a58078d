"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userLogin = exports.corporationLogin = exports.superAdminLogin = exports.login1 = exports.login = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const helpers_1 = require("./helpers");
const bcrypt_1 = __importDefault(require("bcrypt"));
const login = async (model, params, tokenname, password, req, res, JWT, fieldName, fieldName2) => {
    try {
        const userParams = { ...params };
        const existinguser = await prisma[model].findUnique(userParams);
        if (!existinguser) {
            return res
                .status(400)
                .json({ success: false, message: "Invalid credential" });
        }
        const isPasswordValid = await bcrypt_1.default.compare(password, existinguser.password);
        if (!isPasswordValid) {
            return res
                .status(400)
                .json({ success: false, message: "Incorrect Password" });
        }
        // // Clear all sessions and cookies for this user
        // await prisma.session.deleteMany({
        //   where: { user_id: existinguser.user_id },
        // });
        // // Clear the current cookie for this session (this will log out the user on the current device too)
        // res.clearCookie(tokenname, {
        //   httpOnly: true,
        //   // secure: process.env.NODE_ENV === "production",
        //   // sameSite: "strict", // Uncomment if needed for production
        // });
        const token = jsonwebtoken_1.default.sign({
            [fieldName]: existinguser[fieldName],
            username: existinguser.username,
            [fieldName2]: existinguser[fieldName2],
        }, JWT, { expiresIn: "1d" });
        // // Create a new session for the user
        // await prisma.session.create({
        //   data: {
        //     user_id: existinguser.user_id,
        //     session_token: token,
        //   },
        // });
        // // Set the new token in the response cookies for the current session
        return res
            .cookie(tokenname, token, {
            httpOnly: true,
            maxAge: 1 * 24 * 60 * 60 * 1000,
        })
            .status(200)
            .json({ success: true, message: "Login successful", token });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.login = login;
const login1 = async (model, params, tokenname, password, req, res, JWT, fieldName, fieldName2) => {
    try {
        const userParams = { ...params };
        const existinguser = await prisma[model].findUnique(userParams);
        if (!existinguser) {
            return res
                .status(400)
                .json({ success: false, message: "Invalid credential" });
        }
        const isPasswordValid = await bcrypt_1.default.compare(password, existinguser.password);
        if (!isPasswordValid) {
            return res
                .status(400)
                .json({ success: false, message: "Incorrect Password" });
        }
        await prisma.session.deleteMany({
            where: { user_id: existinguser.id },
        });
        const token = jsonwebtoken_1.default.sign({
            [fieldName]: existinguser[fieldName],
            username: existinguser.username,
            [fieldName2]: existinguser[fieldName2],
        }, JWT, { expiresIn: "1d" });
        // Create a new session for the user
        await prisma.session.create({
            data: {
                user_id: existinguser.id,
                session_token: token,
            },
        });
        // Set the new token in the response cookies for the current session
        return res
            .cookie(tokenname, token, {
            httpOnly: true,
            maxAge: 1 * 24 * 60 * 60 * 1000, // 1 day
        })
            .status(200)
            .json({ success: true, message: "Login successful", token });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.login1 = login1;
const superAdminLogin = async (req, res) => {
    const { username } = req.body;
    const params = {
        where: { username: username },
    };
    const password = req.body.password;
    await (0, exports.login)("superAdmin", params, "superadmintoken", password, req, res, process.env.JWT_SECRET, "id");
};
exports.superAdminLogin = superAdminLogin;
const corporationLogin = async (req, res) => {
    const { username } = req.body;
    const params = {
        where: { username: username },
    };
    const password = req.body.password;
    await (0, exports.login)("corporation", params, "corporationtoken", password, req, res, process.env.JWT_SECRET, "corporation_id");
};
exports.corporationLogin = corporationLogin;
const userLogin = async (req, res) => {
    const { username } = req.body;
    const params = {
        where: { username: username },
    };
    const password = req.body.password;
    await (0, exports.login1)("user", params, "token", password, req, res, process.env.JWT_SECRET, "id", "corporation_id");
};
exports.userLogin = userLogin;
//# sourceMappingURL=login.js.map