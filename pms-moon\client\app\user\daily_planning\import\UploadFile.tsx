"use client";
import { Input } from "@/components/ui/input";
import React, { useEffect, useState } from "react";
import Papa from "papaparse";
import { formSubmit } from "@/lib/helpers";
import { daily_planning_details, upload_file } from "@/lib/routePath";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useParams, useRouter } from "next/navigation";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Loader2 } from "lucide-react";

type StatusCount = {
  [status: string]: number;
};

type CarrierData = {
  [carrier: string]: StatusCount;
};

const UploadFile = () => {
  const [file, setFile] = useState<File | null>(null);
  const [data, setData] = useState<CarrierData>({});
  const [error, setError] = useState<any | null>([]);
  const [statuses, setStatuses] = useState<string[]>([]);
  const [fileType, setFileType] = useState<string>("data_file");
  const [fileUploadError, setFileUploadError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const { id } = useParams();
  const router = useRouter();

  useEffect(() => {
    const storedData = localStorage.getItem("csvData");
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      setData(parsedData);
      setStatuses(getUniqueStatuses(parsedData));
    }
  }, []);

  const getUniqueStatuses = (data: CarrierData): string[] => {
    const statusSet = new Set<string>();
    Object.values(data).forEach((carrierStatuses) => {
      Object.keys(carrierStatuses).forEach((status) => statusSet.add(status));
    });
    return Array.from(statusSet);
  };

  const processCSV = async (file: File) => {
    try {
      const form = new FormData();
      file && form.append("file", file);

      if (fileType === "data_file") {
        const response = await fetch(`${upload_file.UPLOAD_FILE}/${id}`, {
          method: "POST",
          body: form,
        });
        const res = await response.json();
        
        if (response.ok) {
          toast.success("File uploaded successfully");
          if (res.error.length > 0) {
            setError(res.error);
          } else {
            router.push(`/user/daily_planning`);
          }
        } else {
          toast.error(res.error);
        }
      } else if (fileType === "210_file") {
        const response = await fetch(
          `${upload_file.UPLOAD_FILE_TWOTEN}/${id}`,
          {
            method: "POST",
            body: form,
          }
        );
        const res = await response.json();
        //  (res.error);

        if (response.ok) {
          toast.success("File uploaded successfully");
          if (res.error.length > 0) {
            setError(res.error);
          } else {
            router.push(`/user/daily_planning`);
          }
        } else {
          toast.error(res.error);
        }
      }
    } catch (error) {
      toast.error("An error occurred while processing the file");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setFileUploadError(null);
    }
  };

  const handleSubmit = async () => {
    if (file) {
      setIsProcessing(true);
      await processCSV(file);
    } else {
      setFileUploadError("Please select a file");
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center py-3">
        <RadioGroup
          defaultValue="data_file"
          className="flex"
          onValueChange={setFileType}
        >
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="data_file" id="r1" />
            <Label htmlFor="r1">Manual Data file</Label>
          </div>
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="210_file" id="r2" />
            <Label htmlFor="r2">210 file</Label>
          </div>
        </RadioGroup>
      </div>
      <Input
        type="file"
        name="file"
        accept=".csv"
        id="file"
        className="w-96 mb-2"
        onChange={handleFileChange}
        disabled={isProcessing}
      />

      <Button onClick={handleSubmit} disabled={isProcessing}>
        {isProcessing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : (
          "Process CSV"
        )}
      </Button>
      {fileUploadError && (
        <div className="text-red-500 text-sm">{fileUploadError}</div>
      )}
      <div className="flex flex-wrap gap-2 mt-1">
        {error &&
          error.map((err, index) => (
            <div
              key={index}
              className="mt-1 bg-red-100 text-red-600 rounded-md p-1 text-xs"
            >
              {err}
            </div>
          ))}
      </div>
    </div>
  );
};

export default UploadFile;
