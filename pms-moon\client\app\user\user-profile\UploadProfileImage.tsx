import DialogHeading from "@/app/_component/DialogHeading";
import FormInput from "@/app/_component/FormInput";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { formSubmit, getAllData, getCookie } from "@/lib/helpers";
import { employee_routes } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import React, { Dispatch, SetStateAction, useState } from "react";
import { useForm } from "react-hook-form";
import { CiEdit } from "react-icons/ci";
import { toast } from "sonner";

const UploadProfileImage = ({
  setProfileImage,
}: {
  setProfileImage: Dispatch<SetStateAction<string | null>>;
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  //   const [profileImage, setProfileImage] = useState<string | null>(null);
  const [image, setImage] = useState<File | null>(null);
  const form = useForm<any>({});
  const handleChange = (file: File | null) => {
    setImage(file);
    //  ("Selected file:", file);
  };

  // const onSubmit = async () => {

  //   if (image) {
  //   //   const reader = new FileReader();
  //   //    (reader);

  //   //   reader.onloadend = () => {
  //   //     setProfileImage(reader.result as string);
  //   //   };
  //   //   reader.readAsDataURL(image);

  //     const formData = new FormData();
  //     image && formData.append("file", image);
  //      (formData.get("file"));
  //     const corporationToken = await  getCookie("corporationtoken");

  //     const userToken = await getCookie("token");
  //      (corporationToken, userToken)
  //     // Choose the cookie based on availability
  //     const cookie = corporationToken ? corporationToken : userToken;
  //     //   formSubmit(
  //     //     `${employee_routes.UPLOAD_USERS_IMAGE}`,
  //     //     "POST",
  //     //     formData
  //     //   ).then((data) => {
  //     //     if (data.success) {
  //     //       toast.success(data.message);
  //     //     }
  //     //   });
  //     //  (cookie)
  //     // const res = await fetch(employee_routes.UPLOAD_USERS_IMAGE, {
  //     //   method: "POST",
  //     //   headers: {

  //     //     cookie: `${cookie}`,
  //     //   },
  //     //   credentials: "include",
  //     //   body: formData,
  //     // });

  //     //  (res);
  //     // const data = await res.json();
  //     //  ;
  //     // if (data.success) {
  //     //   toast.success(data.message);
  //     // }

  //     const res = await fetch(employee_routes.UPLOAD_USERS_IMAGE, {
  //       method: "POST",
  //       headers: {
  //         cookie: `${cookie}`,
  //       },
  //       credentials: "include",
  //       body: formData,
  //     });
  //     const data = await res.json();
  //      ;  // Log the response from the server
  //     if (data.success) {
  //       toast.success(data.message);
  //     } else {
  //       toast.error(data.message);
  //     }
  //   }
  // };


  const onSubmit = async () => {
    if (image) {
      const formData = new FormData();
      formData.append("file", image);
      //  (formData.get("file"));

      const corporationToken = await getCookie("corporationtoken");
      const userToken = await getCookie("token");
      const cookie = corporationToken || userToken;  // Use whichever token exists

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);
      try {
        // Ensure you're awaiting the fetch response correctly
        const res = await fetch(employee_routes.UPLOAD_USERS_IMAGE, {
          method: "POST",
          headers: { cookie: `${cookie}` },
          credentials: "include",
          body: formData,
          signal: controller.signal,
        });

        const data = await res.json();


        if (data.success) {
          toast.success(data.message);
        } else {
          toast.error(data.message);
        }
      } catch (error: any) {
        if (error.name === 'AbortError') {
          toast.error('Request was aborted');
        } else {
          toast.error('Error uploading image.');
        }
      } finally {
        clearTimeout(timeoutId);
      }

    }
  };

  // const onSubmit = async (event: React.FormEvent) => {
  //     event.preventDefault();  // Prevent the form from refreshing

  //     if (image) {
  //       const formData = new FormData();
  //       formData.append("file", image);

  //       const corporationToken = await getCookie("corporationtoken");
  //       const userToken = await getCookie("token");

  //       const cookie = corporationToken || `customer ${userToken}`;

  //       try {
  //         const res = await fetch(employee_routes.UPLOAD_USERS_IMAGE, {
  //           method: "POST",
  //           credentials: "include",
  //           body: formData,
  //           headers: {
  //             cookie: `${cookie}`,
  //           },
  //         });

  //         const data = await res.json();
  //         if (data.success) {
  //           toast.success(data.message);
  //         } else {
  //           toast.error("Image upload failed!");
  //         }
  //       } catch (error) {
  //         toast.error("Error uploading image.");
  //         console.error("Upload error:", error);
  //       }
  //     }
  //   };

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <CiEdit
            // onClick={handleEditIconClick}
            className="cursor-pointer  text-black "
            size={25}
            onClick={() => setIsDialogOpen(true)}
          />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading title="Upload Profile Image" description="" />
          <Form {...form}>
            <form className="space-y-4">
              <div className="grid grid-cols-1 gap-5">
                <FormField
                  control={form.control}
                  name="file"
                  render={({ field }) => {
                    return (
                      <FormItem>
                        <FormLabel></FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={"file"}
                              placeholder={"Profile Image"}
                              {...field}
                              onChange={(event: any) =>
                                handleChange(event.target.files[0])
                              }
                              className="bg-gray-200 dark:bg-gray-700 border-none dark:border-gray-700 placeholder:text-gray-400 dark:placeholder:text-gray-100/50 !outline-main-color "
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>
              <Button
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                onClick={onSubmit}
              >
                Submit
              </Button>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UploadProfileImage;
