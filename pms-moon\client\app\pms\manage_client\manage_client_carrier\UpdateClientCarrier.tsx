"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit } from "@/lib/helpers";
import { setup_routes } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createClientCarrierSchema } from "@/lib/zodSchema";
import React, { useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import TriggerButton from "@/app/_component/TriggerButton";
import { useRouter } from "next/navigation";

const UpdateClientCarrier = ({
  data,
  allclient,
  allcarrier,
}: {
  data: any;
  allclient: any;
  allcarrier: any;
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const router = useRouter();

  const { form } = useDynamicForm(createClientCarrierSchema, {
    carrier_id: data?.carrier_id?.toString(),
    client_id: data?.client_id?.toString(),
    payment_terms: data?.payment_terms,
    client_name: data?.client_name || "",
  });

  async function onSubmit(values: any) {
    //  (values);

    try {
      const formData = {
        carrier_id: values?.carrier_id,
        client_id: values?.client_id,
        payment_terms: values?.payment_terms,
        client_name: values?.client_name || "",
      };

      //  (formData, "formData");

      const res = await formSubmit(
        `${setup_routes.UPDATE_SETUP}/${data.id}`,
        "PUT",
        formData
      );

      if (res.success) {
        toast.success(res.message);
        //  (res.message);
        setIsDialogOpen(false);
        form.reset();
        router.refresh();
      } else {
        toast.error(
          res.error || "An error occurred while updating the client carrier."
        );
      }
    } catch (error) {
      toast.error("An error occurred while updating the client .");
      console.error(error);
    }
  }

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger onClick={() => setIsDialogOpen(true)} title="Update">
          <TriggerButton type="edit" />
        </DialogTrigger>

        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Update Client Carrier"
            description=" Update Client Carrier details.."
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-5">
                <SelectComp
                  form={form}
                  label="Client Name"
                  name="client_id"
                  // value={client_id}
                  placeholder="Select client name"
                  key={"role"}
                  disabled={true}
                  isRequired
                >
                  {allclient.map((role: any) => (
                    <SelectItem value={role?.id?.toString()} key={role?.id}>
                      {role.client_name}
                    </SelectItem>
                  ))}
                </SelectComp>
                <SelectComp
                  form={form}
                  label="Carrier Name"
                  name="carrier_id"
                  placeholder="Select carrier name"
                  key={"role"}
                  isRequired
                >
                  {allcarrier?.map((role: any) => (
                    <SelectItem
                      value={role.id.toString()}
                      key={role.id}
                    >
                      {role.name}
                    </SelectItem>
                  ))}
                </SelectComp>
              </div>

              <div className="grid grid-cols-1 gap-5">
                <FormInput
                  form={form}
                  label="Payment Terms"
                  name="payment_terms"
                  type="number"
                  isRequired
                />
              </div>

              <SubmitBtn className="w-full" text="Submit" />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateClientCarrier;
