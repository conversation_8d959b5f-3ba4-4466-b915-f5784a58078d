import { Router } from "express";
import { viewUserTitle } from "../../controllers/usertitle/view";
import { createuserClients } from "../../controllers/userClients/create";
import { getUserClients } from "../../controllers/userClients/view";
import { updateUserClients } from "../../controllers/userClients/update";

const router = Router();

router.post(
  "/create-userclients",
  createuserClients
);

router.get(
  "/get-all-userclients",
  getUserClients
);

// router.put(
//   "/update-userclients",
//   updateUserClients
// );

export default router;