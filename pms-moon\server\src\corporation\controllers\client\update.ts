import { Request, Response } from "express";
import { updateItem } from "../../../utils/operation";

export const updateClient = async (req, res) => {
    const id= req.params.id;
    const {corporation_id }= req;
  const fields = {
    associateId: req.body.associateId,
    client_name: req.body.client_name,
    ownership_id: Number(req.body.ownership), 
    branch_id: req.body.branch_id,
    corporation_id: Number(corporation_id)
  };

  
  await updateItem({
    model: "client",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "client has been updated",
  });
};