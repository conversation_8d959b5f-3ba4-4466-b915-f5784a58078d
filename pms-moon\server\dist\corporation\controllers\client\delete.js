"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteClient = void 0;
const operation_1 = require("../../../utils/operation");
const deleteClient = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "client",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "client has been deleted",
    });
};
exports.deleteClient = deleteClient;
//# sourceMappingURL=delete.js.map