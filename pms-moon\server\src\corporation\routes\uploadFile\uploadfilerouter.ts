import { Router } from "express";
import multer from "multer";
import { uploadCSV } from "../../controllers/uploadfile/create"; 
import { uploadCSVTwoTen } from "../../controllers/uploadfile/createTwoTen";

const router = Router();
const DIR = "./src/public";

const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, DIR);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + "-" + file.originalname);
  },
});
export const upload = multer({
  storage: storage,
});

router.post("/upload-file/:id", upload.single("file"), uploadCSV);
router.post("/upload-csv-twoten/:id", upload.single("file"), uploadCSVTwoTen);

export default router;
