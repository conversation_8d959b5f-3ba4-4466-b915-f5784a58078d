"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteCategory = void 0;
const operation_1 = require("../../../utils/operation");
const deleteCategory = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "category",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "category has been deleted",
    });
};
exports.deleteCategory = deleteCategory;
//# sourceMappingURL=delete.js.map