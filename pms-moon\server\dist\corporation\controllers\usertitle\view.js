"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewUserTitle = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewUserTitle = async (req, res) => {
    try {
        const data = await prisma.userTitle.findMany({
            orderBy: {
                id: "asc",
            },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewUserTitle = viewUserTitle;
//# sourceMappingURL=view.js.map