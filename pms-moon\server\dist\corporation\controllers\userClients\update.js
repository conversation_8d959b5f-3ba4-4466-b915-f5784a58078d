"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserClients = void 0;
const updateUserClients = async (userId, newClientIds) => {
    try {
        const existingMappings = await prisma.userClients.findMany({
            where: { userId },
            select: { clientId: true },
        });
        const existingClientIds = existingMappings.map(mapping => mapping.clientId);
        const clientsToAdd = newClientIds.filter(id => !existingClientIds.includes(id));
        const clientsToRemove = existingClientIds.filter(id => !newClientIds.includes(id));
        if (clientsToAdd.length > 0) {
            const newMappings = clientsToAdd.map(clientId => ({
                userId,
                clientId,
            }));
            await prisma.userClients.createMany({
                data: newMappings,
                skipDuplicates: true,
            });
        }
        if (clientsToRemove.length > 0) {
            await prisma.userClients.deleteMany({
                where: {
                    userId,
                    clientId: { in: clientsToRemove },
                },
            });
        }
        return { success: true };
    }
    catch (error) {
        console.error("Error updating user clients:", error);
        return { success: false, error: "Failed to update user-client." };
    }
};
exports.updateUserClients = updateUserClients;
//# sourceMappingURL=update.js.map