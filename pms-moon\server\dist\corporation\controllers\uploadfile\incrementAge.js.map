{"version": 3, "file": "incrementAge.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/uploadfile/incrementAge.ts"], "names": [], "mappings": ";;;;;;AAAA,0DAA6B;AAEtB,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;IACrC,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC;IACzD,KAAK,MAAM,EAAE,IAAI,GAAG,EAAE,CAAC;QACrB,MAAM,SAAS,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAkB,EAAE,EAAE;YAClD,IAAI,GAAG,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAC;YAC9B,OAAO,GAAG,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;QACH,MAAM,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC;YACvC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE;YACpB,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE;SACzB,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAZW,QAAA,YAAY,gBAYvB;AAEF,mBAAI,CAAC,QAAQ,CACX,WAAW,EACX,KAAK,IAAI,EAAE;IACT,IAAI,CAAC;QACH,MAAM,IAAA,oBAAY,GAAE,CAAC;IACvB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC3D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,EACD,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,cAAc,EAAE,CAC9C,CAAC"}