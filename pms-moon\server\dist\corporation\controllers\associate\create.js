"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAssociate = void 0;
const operation_1 = require("../../../utils/operation");
const createAssociate = async (req, res) => {
    const { corporation_id } = req;
    const fields = {
        name: req.body.name,
        corporation_id: Number(corporation_id),
    };
    await (0, operation_1.createItem)({
        model: "associate",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "Associate has been created",
    });
};
exports.createAssociate = createAssociate;
//# sourceMappingURL=create.js.map