"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createBranch = void 0;
const operation_1 = require("../../../utils/operation");
const createBranch = async (req, res) => {
    const { corporation_id } = req;
    const fields = {
        branch_name: req.body.name,
        corporation_id: Number(corporation_id),
    };
    await (0, operation_1.createItem)({
        model: "Branch",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "branch has been created",
    });
};
exports.createBranch = createBranch;
//# sourceMappingURL=create.js.map