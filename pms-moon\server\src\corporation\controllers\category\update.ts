import { Request, Response } from "express";
import { updateItem } from "../../../utils/operation";

export const updateCategory = async (req, res) => {
    const id= req.params.id;

    const {corporation_id }= req;
  const fields = {
    category_name: req.body.name,
    corporation_id: Number(corporation_id)
  };
  await updateItem({
    model: "category",
    fieldName: "id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "category has been updated",
  });
};