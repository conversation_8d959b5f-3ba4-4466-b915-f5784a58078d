"use client";

import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit } from "@/lib/helpers";
import { carrier_routes, category_routes, location_api } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createCarrierSchema, createCategorySchema } from "@/lib/zodSchema";
import React, { useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FaPlus } from "react-icons/fa";
import DialogHeading from "@/app/_component/DialogHeading";
import { useRouter } from "next/navigation";
import TriggerButton from "@/app/_component/TriggerButton";

function AddCategory() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const router = useRouter();

  const { form } = useDynamicForm(createCategorySchema, {
    name: "",
  });

  async function onSubmit(values: any) {
    try {
      const formData = {
        name: values.name.toUpperCase(),
      };
      const data = await formSubmit(
        category_routes.CREATE_CATEGORY,
        "POST",
        formData
      );
      if (data.success) {
        toast.success(data.message);
        router.refresh();
        setIsDialogOpen(false);
        form.reset()
      } else {
        toast.error(
          data.error || "An error occurred while adding the category."
        );
      }
    } catch (error) {
      toast.error("An error occurred while adding the category.");
      console.error(error);
    }
  }
  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger>
          <TriggerButton type="add" text="category" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Add Category"
            description="Please enter category details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 gap-5">
                <FormInput
                  form={form}
                  label="Category Name"
                  placeholder="Enter Category Name"
                  name="name"
                  type="text"
                  isRequired
                />
              </div>
              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Submit"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default AddCategory;
