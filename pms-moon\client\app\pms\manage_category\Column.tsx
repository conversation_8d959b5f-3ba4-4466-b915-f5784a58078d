import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";

import DeleteRow from "@/app/_component/DeleteRow";
import { carrier_routes, category_routes } from "@/lib/routePath";
import { PermissionWrapper } from "@/lib/permissionWrapper";
import UpdateCategory from "./UpdateCategory";

export interface Category {
  name: string;
  id: any;
}

export const Column = (permissions: string[]): ColumnDef<Category>[] => [
  {
    accessorKey: "Sr. No.",
    header: "Sr. No.",
    cell: ({ row }) => row.index + 1,
  },
  {
    accessorKey: "category_name",
    header: "Name",
  },
  {
    accessorKey: "action",
    header: "Action",
    id: "action",
    cell: ({ row }) => {
      const category = row?.original;
      return (
        <div className="flex items-center">
          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["update-category"]}
          >
            <UpdateCategory data={category} />
          </PermissionWrapper>

          <PermissionWrapper
            permissions={permissions}
            requiredPermissions={["delete-category"]}
          >
            <DeleteRow
              route={`${category_routes.DELETE_CATEGORY}/${category?.id}`}
            />
          </PermissionWrapper>
        </div>
      );
    },
  },
];
