"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteSuperAdmin = void 0;
const operation_1 = require("../../utils/operation");
const deleteSuperAdmin = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "superAdmin",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "Super Admin has been deleted",
    });
};
exports.deleteSuperAdmin = deleteSuperAdmin;
//# sourceMappingURL=delete.js.map