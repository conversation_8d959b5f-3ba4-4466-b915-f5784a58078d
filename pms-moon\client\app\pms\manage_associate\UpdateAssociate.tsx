"use client";
import DialogHeading from "@/app/_component/DialogHeading";
import FormInput from "@/app/_component/FormInput";
import SubmitBtn from "@/app/_component/SubmitBtn";
import TriggerButton from "@/app/_component/TriggerButton";
import { <PERSON><PERSON>, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { formSubmit } from "@/lib/helpers";
import { associate_routes } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createAssociateSchema } from "@/lib/zodSchema";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

function UpdateAssociate({ data }: any) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const router = useRouter();

  const { form } = useDynamicForm(createAssociateSchema, {
    name: data?.name || "",
  });

  async function onSubmit(values: any) {
    try {
      const formData = {
        name: values.name.toUpperCase().trim(),
      };

      const res = await formSubmit(
        `${associate_routes.UPDATE_ASSOCIATE}/${data.id}`,
        "PUT",
        formData
      );

      if (res.success) {
        toast.success(res.message);
        form.reset();
        router.refresh();
        setIsDialogOpen(false);
      } else {
        toast.error(
          data.error || "An error occurred while updating the associate."
        );
      }
    } catch (error) {
      toast.error("An error occurred while updating the associate.");
      console.error(error);
    }
  }

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger>
          <TriggerButton type="edit" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Update Associate"
            description="Please Enter Associate Details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 gap-5">
                <FormInput
                  form={form}
                  label="Associate Name"
                  placeholder="Enter Associate Name"
                  name="name"
                  type="text"
                  isRequired
                />
              </div>
              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Submit"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default UpdateAssociate;
