"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import KeyboardShortcutsHelp from "./KeyboardShortcutsHelp";

interface GlobalKeyboardShortcutsContextType {
  showShortcuts: () => void;
  hideShortcuts: () => void;
  isShortcutsVisible: boolean;
}

const GlobalKeyboardShortcutsContext = createContext<GlobalKeyboardShortcutsContextType | undefined>(undefined);

export const useGlobalKeyboardShortcuts = () => {
  const context = useContext(GlobalKeyboardShortcutsContext);
  if (!context) {
    throw new Error("useGlobalKeyboardShortcuts must be used within a GlobalKeyboardShortcutsProvider");
  }
  return context;
};

interface GlobalKeyboardShortcutsProviderProps {
  children: React.ReactNode;
}

export const GlobalKeyboardShortcutsProvider: React.FC<GlobalKeyboardShortcutsProviderProps> = ({ children }) => {
  const [isShortcutsVisible, setIsShortcutsVisible] = useState(false);
  const pathname = usePathname();

  const showShortcuts = () => setIsShortcutsVisible(true);
  const hideShortcuts = () => setIsShortcutsVisible(false);

  // Global keyboard event listener
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      // Shift + ? to show keyboard shortcuts
      if (event.shiftKey && event.key === "?") {
        event.preventDefault();
        showShortcuts();
      }
      // Escape to close shortcuts
      else if (event.key === "Escape" && isShortcutsVisible) {
        event.preventDefault();
        hideShortcuts();
      }
    };

    // Add global event listener
    document.addEventListener("keydown", handleGlobalKeyDown);

    // Cleanup
    return () => {
      document.removeEventListener("keydown", handleGlobalKeyDown);
    };
  }, [isShortcutsVisible]);

  // Close shortcuts when navigating to a different page
  useEffect(() => {
    hideShortcuts();
  }, [pathname]);

  const contextValue: GlobalKeyboardShortcutsContextType = {
    showShortcuts,
    hideShortcuts,
    isShortcutsVisible,
  };

  return (
    <GlobalKeyboardShortcutsContext.Provider value={contextValue}>
      {children}
      
      {/* Global Keyboard Shortcuts Help Dialog */}
      <KeyboardShortcutsHelp
        isOpen={isShortcutsVisible}
        onClose={hideShortcuts}
      />
    </GlobalKeyboardShortcutsContext.Provider>
  );
};

export default GlobalKeyboardShortcutsProvider;
