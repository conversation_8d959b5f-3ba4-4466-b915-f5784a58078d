import React from "react";
import { BiSolidTime } from "react-icons/bi";
import { FaUser } from "react-icons/fa";
import { SiTask } from "react-icons/si";

const ActivityLogEntry = ({ log }: { log: any }) => {
  return (
    <>
      <div className="flex justify-between ">
        <div className=" flex items-center px-2 py-1.5 border-r-2  border-main-color">
          <FaUser className="mr-2" />
          <span className=" ">{log.username}</span>
        </div>

        <div className=" flex items-center  border-r-2 px-2  border-main-color">
          <BiSolidTime className="mr-2" />
          <span className="  ">{new Date(log.timestamp).toLocaleString()}</span>
        </div>
        <div className=" flex items-center pr-1 pl-2  ">
          <SiTask className="mr-0.5" />
          <span className="  ">{log.action}</span>
        </div>
      </div>
    </>
  );
};

export default ActivityLogEntry;
