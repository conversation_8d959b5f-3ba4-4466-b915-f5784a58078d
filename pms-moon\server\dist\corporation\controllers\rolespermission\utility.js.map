{"version": 3, "file": "utility.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/rolespermission/utility.ts"], "names": [], "mappings": ";;;AACA,oDAA0D;AAEnD,MAAM,iBAAiB,GAAG,KAAK,EACpC,KAAa,EACb,SAAiB,EACjB,MAA2B,EAC3B,GAAa,EACb,GAAG,EACH,oBAA4B,EAC5B,cAAsB,EACtB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;QACxC,MAAM,SAAS,GACb,cAAc,IAAI,OAAO,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,eAAe,CAAC;QACrE,MAAM,MAAM,GAAG,cAAc,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC;QAEpE,2BAA2B;QAC3B,iEAAiE;QACjE,+CAA+C;QAE/C,0BAA0B;QAC1B,MAAM,QAAQ,GAAG;YACf,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;YAC/B,cAAc,EAAE,cAAc;SAC/B,CAAC;QAEF,oDAAoD;QACpD,WAAW;QACX,cAAc;QACd,SAAS;QACT,yCAAyC;QACzC,MAAM;QACN,iDAAiD;QAEjD,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;YACzD,kBAAkB;YAClB,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;gBACrC,IAAI,EAAE;oBACJ,aAAa;oBACb,cAAc,EAAE,MAAM,CAAC,cAAc;oBAErC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;iBAChC;aACF,CAAC,CAAC;YAEH,0BAA0B;YAC1B,MAAM,OAAO,CAAC,GAAG,CACf,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,aAAqB,EAAE,EAAE;gBAC/C,OAAQ,EAAU,CAAC,cAAc,CAAC,MAAM,CAAC;oBACvC,IAAI,EAAE;wBACJ,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC;wBACpC,OAAO,EAAE,OAAO,CAAC,EAAE;qBACpB;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CACH,CAAC;YAEF,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,cAAc;YACvB,WAAW,EAAE,WAAW;SACzB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,0BAA0B;IAClD,CAAC;AACH,CAAC,CAAC;AAnEW,QAAA,iBAAiB,qBAmE5B;AAEK,MAAM,iBAAiB,GAAG,KAAK,EAAE,EACtC,KAAK,EACL,SAAS,EACT,EAAE,EACF,GAAG,EACH,cAAc,GAOf,EAAE,EAAE;IACH,IAAI,CAAC;QACH,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;YACrC,MAAO,EAAU,CAAC,cAAc,CAAC,UAAU,CAAC;gBAC1C,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE;aAC3B,CAAC,CAAC;YACH,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;gBACrB,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE;aAC3B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,cAAc;SACxB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACnB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,2DAA2D;SACrE,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAlCW,QAAA,iBAAiB,qBAkC5B;AAEK,MAAM,iBAAiB,GAAG,KAAK,EAAE,EACtC,KAAK,EACL,SAAS,EACT,EAAE,EACF,IAAI,EACJ,GAAG,EACH,GAAG,EACH,oBAAoB,EACpB,cAAc,GAUf,EAAE,EAAE;IACH,IAAI,CAAC;QACH,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;QACxC,MAAM,SAAS,GACb,cAAc,IAAI,OAAO,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,eAAe,CAAC;QACrE,MAAM,MAAM,GAAG,cAAc,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,cAAc,CAAC;QAEpE,0DAA0D;QAC1D,MAAM,WAAW,GAAG,MAAM,UAAU,CAClC,KAAK,EACL,EAAE,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,EAC9B,GAAG,CACJ,CAAC;QAEF,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gBAAgB;aAC1B,CAAC,CAAC;QACL,CAAC;QAED,gCAAgC;QAChC,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,EAAE,CAAC;YAChD,MAAM,QAAQ,GAAG;gBACf,cAAc,EAAE,cAAc;gBAC9B,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC;YACF,MAAM,YAAY,GAAG,GAAG,KAAK,iBAAiB,CAAC;YAC/C,MAAM,gBAAgB,GAAG,MAAM,IAAA,0BAAgB,EAAC;gBAC9C,KAAK;gBACL,QAAQ;gBACR,GAAG;gBACH,YAAY;aACb,CAAC,CAAC;YACH,IAAI,gBAAgB;gBAAE,OAAO,gBAAgB,CAAC;QAChD,CAAC;QAED,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;YACrC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;gBACrB,KAAK,EAAE;oBACL,CAAC,SAAS,CAAC,EAAE,EAAE;iBAChB;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;iBAC1C;aACF,CAAC,CAAC;YAEH,MAAO,EAAU,CAAC,cAAc,CAAC,UAAU,CAAC;gBAC1C,KAAK,EAAE,EAAE,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE;aAC/B,CAAC,CAAC;YAEH,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE,UAAkB,EAAE,EAAE;gBAC/C,MAAO,EAAU,CAAC,cAAc,CAAC,MAAM,CAAC;oBACtC,IAAI,EAAE;wBACJ,aAAa,EAAE,MAAM,CAAC,UAAU,CAAC;wBACjC,OAAO,EAAE,MAAM,CAAC,EAAE,CAAC;qBACpB;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CACH,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,cAAc;SACxB,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,sBAAsB;SAChC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AA7FW,QAAA,iBAAiB,qBA6F5B;AAEF,MAAM,UAAU,GAAG,KAAK,EAAE,KAAa,EAAE,QAAa,EAAE,GAAa,EAAE,EAAE;IACvE,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAW;IACb,CAAC;AACH,CAAC,CAAC;AAEK,MAAM,OAAO,GAAG,KAAK,EAAE,KAAa,EAAE,MAAW,EAAE,EAAE;IAC1D,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAPW,QAAA,OAAO,WAOlB"}