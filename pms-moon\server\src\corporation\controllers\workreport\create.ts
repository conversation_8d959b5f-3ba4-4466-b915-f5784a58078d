import { createItem } from "../../../utils/operation";
import { WorkStatus } from "./helper";
import winston from "winston";

// Set up Winston logger
const logger = winston.createLogger({
  level: "info",
  format: winston.format.combine(
    winston.format.colorize(),
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => {
      return `${timestamp} ${level}: ${message}`;
    })
  ),
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: "workreport.log" }),
  ],
});

export const createWorkreport = async (req, res) => {
  //  (req, "req")
  const userId = req.user_id;
  // Log the start of the function execution
  logger.info(`Start creating work report for user ${userId}`);

  const startTime = new Date(req.body.start_time);
  const endTime = new Date(req.body.end_time);

  // Calculate time spent in minutes
  const timeSpent = Math.round(
    (endTime.getTime() - startTime.getTime()) / (1000 * 60) // Convert milliseconds to minutes
  );

  try {
    // HOTFIX: PM-055
    const existingWorkReport = await prisma.workReport.findFirst({
      where: {
        user_id: userId,
        date: new Date(req.body.date),
        OR: [
          {
            AND: [
              { start_time: { lt: endTime } }, // Existing start_time is before new endTime
              { finish_time: { gt: startTime } }, // Existing finish_time is after new startTime 
            ],
          },
        ],
      },
    });

    if (existingWorkReport) {
      logger.warn(`Overlapping work report exists for user ${userId}`);
      return res.status(400).json({
        success: false,
        message: "You already have an entry for this time.",
      });
    }
    const workReport = await prisma.workReport.create({
      data: {
        date: new Date(req.body.date),
        user_id: userId,
        client_id: Number(req.body.client_id),
        carrier_id:
          req.body.carrier_id === "N/A"
            ? null
            : req.body.carrier_id
            ? Number(req.body.carrier_id)
            : null,
        work_type_id: Number(req.body.work_type_id),
        category_id: Number(req.body.category),
        work_status: "FINISHED", // Ensure this matches Prisma enum if applicable
        actual_number: req.body.actual_number || null,
        start_time: startTime,
        finish_time: endTime,
        time_spent: timeSpent,
        task_type: req.body.task_type || null,
        notes: req.body.notes,
        switch_type: req.body.switch_type || null,
      },
    });
    

    // Log success
    logger.info(`Work report created successfully for user ${userId}`);
    return res
      .status(200)
      .json({ success: true, message: "Work Reported created succefully" });
  } catch (error) {
    // Log the error
    logger.error(
      `Error creating work report for user ${userId}: ${error.message}`
    );
    res
      .status(500)
      .json({ message: "Failed to create work report", error: error.message });
  }
};
