"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.excelClientCarrier = exports.createClientCarrier = void 0;
const operation_1 = require("../../../utils/operation");
const xlsx_1 = __importDefault(require("xlsx"));
const fs_1 = __importDefault(require("fs"));
const createClientCarrier = async (req, res) => {
    const { corporation_id } = req;
    const fields = {
        carrier_id: Number(req.body.carrier_id),
        client_id: Number(req.body.client_id),
        corporation_id: corporation_id,
        payment_terms: req.body.payment_terms
    };
    //  (fields);
    const existingsetup = await prisma.clientCarrier.findFirst({
        where: {
            carrier_id: Number(req.body.carrier_id),
            client_id: Number(req.body.client_id),
            // payment_terms: req.body.payment_terms
        }
    });
    if (existingsetup) {
        return res.status(200).json({ success: false, message: 'Data already exist' });
    }
    await (0, operation_1.createItem)({
        model: "clientCarrier",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "Client carrier setup has been created",
    });
};
exports.createClientCarrier = createClientCarrier;
const excelClientCarrier = async (req, res) => {
    try {
        // Check if a file was uploaded
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }
        // Validate file extension
        const allowedExtensions = ['.xlsx', '.xls'];
        const fileExtension = req.file.originalname.split('.').pop();
        if (!allowedExtensions.includes(`.${fileExtension}`)) {
            return res.status(400).json({ error: 'Invalid file type. Only Excel files are allowed.' });
        }
        // Read the uploaded Excel file
        const workbook = xlsx_1.default.readFile(req.file.path);
        const worksheet = workbook.Sheets[workbook.SheetNames[0]];
        const importData = xlsx_1.default.utils.sheet_to_json(worksheet, { header: 1 });
        // Define expected headers
        const header = ['Client_name', 'Carrier_name', 'Payment Terms'];
        let errors = [];
        // Check if the file has the correct headers
        if (!importData[0].includes(header[0]) || !importData[0].includes(header[1]) || !importData[0].includes(header[2])) {
            errors.push('Invalid file format. Please ensure the file has the correct headers.');
            return res.status(400).json({ message: "Invalid file format. Please ensure the file has the correct headers.", errors });
        }
        // Process the data
        const userData = await Promise.all(importData.slice(1).map(async (row) => {
            const client = await prisma.client.findFirst({
                where: { client_name: row[0] },
                select: { id: true },
            });
            const carrier = await prisma.carrier.findFirst({
                where: { name: row[1] },
                select: { id: true },
            });
            const corporation = await prisma.corporation.findFirst({
                where: { corporation_id: row[3] },
                select: { corporation_id: true },
            });
            if (!carrier) {
                return { error: `Client Name ${row[0]} for Carrier Name ${row[1]} not found.` };
            }
            // Check if the combination of client and carrier already exists in the clientCarrier table
            const existingClientCarrier = await prisma.clientCarrier.findFirst({
                where: {
                    OR: [
                        // client_id: client.client_id,
                        { carrier_id: carrier.id, }
                    ]
                },
            });
            if (existingClientCarrier) {
                return { error: `Carrier name ${row[1]} already exists.` };
            }
            return {
                corporation_id: Number(corporation.corporation_id),
                client_id: client.id || null,
                carrier_id: carrier.id || null,
                payment_terms: row[2],
            };
        }));
        // Filter out the rows that contain errors
        const errorMessages = userData.filter((data) => data.error).map((data) => data.error);
        // Filter out valid client-carrier data
        const validUserData = userData.filter((data) => !data.error && data.carrier_id && data.client_id && data.payment_terms);
        // Save the valid user data to the database
        // if (validUserData.length > 0) {
        await prisma.clientCarrier.createMany({
            data: validUserData,
        });
        // }
        // Return the response
        return res.status(200).json({
            message: 'Client carrier data imported successfully',
            errors: errorMessages,
            successCount: validUserData.length,
        });
    }
    catch (error) {
        console.error(error);
        return res.status(500).json({ error: 'Internal Server Error' });
    }
    finally {
        fs_1.default.unlinkSync(req.file.path); // Clean up the uploaded file
    }
};
exports.excelClientCarrier = excelClientCarrier;
//# sourceMappingURL=create.js.map