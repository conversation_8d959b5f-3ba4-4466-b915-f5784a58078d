"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteWorktype = void 0;
const operation_1 = require("../../../utils/operation");
const deleteWorktype = async (req, res) => {
    const id = req.params.id;
    //  ('id',id);
    await (0, operation_1.deleteItem)({
        model: "workType",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "Work Type has been deleted",
    });
};
exports.deleteWorktype = deleteWorktype;
//# sourceMappingURL=delete.js.map