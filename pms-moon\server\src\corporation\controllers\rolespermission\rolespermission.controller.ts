// import { Response } from "express";
// import { nanoid } from "nanoid";
// import {
//   checkExistingRow,
//   validateFields,
// } from "../../../utilities/helpers.js";
// import logAuditTrail from "../../../utilities/log.js";

// export const createRolesPermissions = async (req, res: Response) => {
//   try {
//     const { name, permission } = req.body;
//     const { freightadminid, freight_user_id } = req;
//     const user_role = freightadminid ? "FREIGHT_ADMIN" : "FREIGHT_USER";
//     const userId = freightadminid ? freightadminid : freight_user_id;
//     const corporation_id = req.freightadminid;

//     // Validate required fields
//     const validationError = validateFields(res, ["name"], req.body);
//     if (validationError) return validationError;

//     // Check for existing role
//     const criteria = {
//       name: name.toUpperCase(),
//       corporation_id,
//     };
//     const errorMessage = "Role already exists";
//     const existingRowError = await checkExistingRow(
//       "roles",
//       criteria,
//       res,
//       errorMessage
//     );
//     if (existingRowError) return existingRowError;

//     // Create new role
//     const role_id = nanoid(6);
//     await prisma.$transaction(async (tx) => {
//       await tx.roles.create({
//         data: {
//           role_id,
//           name: name.toUpperCase(),
//           corporation_id,
//         },
//       });
//       if (permission.length > 0) {
//         await Promise.all(
//           permission.map(async (item: string) => {
//             await tx.rolePermission.create({
//               data: {
//                 role_permission_id: nanoid(6),
//                 permission_id: item,
//                 role_id,
//               },
//             });
//           })
//         );
//       }
//     });

//     logAuditTrail(
//       "RoleCreation",
//       "Created",
//       userId,
//       user_role,
//       role_id,
//       "rolePermissionRole_permission_id"
//     );
//     return res.status(200).json({ success: true, message: "Role Created" });
//   } catch (error) {
//     console.log(error);
//   }
// };

// export const createPermission = async (req, res: Response) => {
//   // try {
//   //   await prisma.permissions.create({
//   //     data: {
//   //       permission_id: "1",
//   //       module: "delete-freight-user",
//   //     },
//   //   });
//   //   await prisma.permissions.create({
//   //     data: {
//   //       permission_id: "2",
//   //       module: "add-freight-user",
//   //     },
//   //   });
//   //   return res.status(200).json({
//   //     success: true,
//   //     message: "Permissions Created",
//   //   });
//   // } catch (error) { 
//   //   return res.status(500).json({
//   //     success: false,
//   //     message: error.message || "Something went wrong",
//   //   });
//   // }
// };
