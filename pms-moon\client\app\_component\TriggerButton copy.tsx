"use client"
import { cn } from "@/lib/utils";
import { Plus } from "lucide-react";
import { MdEditDocument } from "react-icons/md";
import { FaEdit } from "react-icons/fa";


type TriggerProp = {
  text?: string;
  type: "add" | "edit" | undefined;
  className?: string;
};

function TriggerButton({ text, type,className }: TriggerProp) {
  return (
    <>
      {type === "add" ? (
        <p className={cn("py-1.5 gap-2 bg-primary  px-4 justify-center  text-white uppercase font-semibold rounded-md flex items-center",className)}>
          <Plus className="w-6 h-6  font-extrabold" />
          {text}
        </p>
      ) : (
        <FaEdit 
 className="text-gray-600/85  w-6 h-6" />
      )}
    </>
  );
}

export default TriggerButton;
