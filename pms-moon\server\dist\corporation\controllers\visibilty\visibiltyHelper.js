"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getVisibility = getVisibility;
async function getVisibility(userId, tableName) {
    const sql = `
  WITH RECURSIVE
  table_params AS (
    SELECT '${tableName}'::TEXT AS table_name
  ),
  base_info AS (
    SELECT
        u.id,
        u.parent_id,
        u.level,
        vr.self,
        vr.ancestors,
        vr.descendants,
        vr.siblings
    FROM "User" u
    JOIN visibility_rules vr
      ON vr.level = u.level
     AND vr.table_name = (SELECT table_name FROM table_params)
    WHERE u.id = $1
  ),
  visible_ancestors AS (
    SELECT
        u.id,
        u.parent_id,
        u.level,
        0 AS distance,
        (SELECT ancestors FROM visibility_rules
         WHERE level = u.level
           AND table_name = (SELECT table_name FROM table_params)) AS allowed
    FROM "User" u
    WHERE u.id = (SELECT id FROM base_info)

    UNION ALL

    SELECT
        p.id,
        p.parent_id,
        p.level,
        va.distance + 1 AS distance,
        GREATEST(
            va.allowed,
            (va.distance + 1) + (
                SELECT ancestors
                FROM visibility_rules
                WHERE level = p.level
                  AND table_name = (SELECT table_name FROM table_params)
            )
        ) AS allowed
    FROM visible_ancestors va
    JOIN "User" p ON p.id = va.parent_id
    WHERE (va.distance + 1) <= va.allowed
  ),
  visible_descendants AS (
    SELECT
        u.id,
        0 AS distance,
        (SELECT descendants FROM visibility_rules
         WHERE level = u.level
           AND table_name = (SELECT table_name FROM table_params)) AS allowed
    FROM "User" u
    WHERE u.id = (SELECT id FROM base_info)

    UNION ALL

    SELECT
        c.id,
        vd.distance + 1 AS distance,
        GREATEST(
            vd.allowed,
            (vd.distance + 1) + (
                SELECT descendants
                FROM visibility_rules
                WHERE level = c.level
                  AND table_name = (SELECT table_name FROM table_params)
            )
        ) AS allowed
    FROM visible_descendants vd
    JOIN "User" c ON c.parent_id = vd.id
    WHERE (vd.distance + 1) <= vd.allowed
  ),
  extended_siblings AS (
    SELECT
        s.id,
        s.parent_id,
        s.level,
        1 AS distance,
        (1 + (
            SELECT descendants
            FROM visibility_rules
            WHERE level = s.level
              AND table_name = (SELECT table_name FROM table_params)
        )) AS allowed
    FROM (
        SELECT id, parent_id, level FROM base_info
        UNION
        SELECT va.id, va.parent_id, va.level FROM visible_ancestors AS va
    ) AS base_nodes
    JOIN "User" s ON s.parent_id = base_nodes.parent_id
                 AND s.id <> base_nodes.id
    JOIN visibility_rules vr ON vr.level = base_nodes.level
         AND vr.table_name = (SELECT table_name FROM table_params)
    WHERE vr.siblings = true

    UNION ALL

    SELECT
        c.id,
        c.parent_id,
        c.level,
        es.distance + 1 AS distance,
        GREATEST(
            es.allowed,
            (es.distance + 1) + (
                SELECT descendants
                FROM visibility_rules
                WHERE level = c.level
                  AND table_name = (SELECT table_name FROM table_params)
            )
        ) AS allowed
    FROM extended_siblings es
    JOIN "User" c ON c.parent_id = es.id
    WHERE (es.distance + 1) <= es.allowed
  ),
  all_visible_users AS (
    SELECT id FROM base_info WHERE self = true
    UNION
    SELECT id FROM visible_ancestors WHERE distance > 0
    UNION
    SELECT id FROM visible_descendants WHERE distance > 0
    UNION
    SELECT id FROM extended_siblings
  )
  SELECT t.user_id
  FROM "${tableName}" t
  WHERE t.user_id IN (SELECT id FROM all_visible_users)
  ORDER BY user_id
`;
    try {
        const result = await prisma.$queryRawUnsafe(sql, userId);
        return result;
    }
    catch (error) {
        console.error("Error executing query:", error);
        throw error;
    }
}
//# sourceMappingURL=visibiltyHelper.js.map