import React from "react";
import Login from "./Login";
import LoginIcon from "./LoginIcon";
import Footer from "@/app/_component/Footer";

const page = () => {
  return (
    <>
      <main className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex flex-col">
        {/* Logo for mobile view */}
        <div className="md:hidden pt-6 px-6">
          <h1 className="text-3xl font-bold text-slate-800">
            <span className="bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
              Oi
            </span>
            <span className="text-slate-700">360</span>
          </h1>
        </div>

        {/* Background elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-24 -right-24 w-96 h-96 bg-blue-100 rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute top-1/3 -left-24 w-72 h-72 bg-indigo-100 rounded-full opacity-20 blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 py-4 flex-1 flex flex-col md:flex-row items-center md:items-stretch gap-8 md:gap-12 lg:gap-20 relative z-10">
          {/* Hide Image component on mobile, show on md and up */}
          <div className="hidden md:flex md:w-1/2 flex-col justify-center">
            <LoginIcon />
          </div>

          <div className="w-full md:w-1/2 flex justify-center items-center pt-4 md:pt-9">
            <Login />
          </div>
        </div>

        <Footer />
      </main>
    </>
  );
};

export default page;
