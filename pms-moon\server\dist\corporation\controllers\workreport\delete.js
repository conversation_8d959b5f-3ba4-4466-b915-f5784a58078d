"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteWorkreport = void 0;
const operation_1 = require("../../../utils/operation");
const deleteWorkreport = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "workReport",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "workReport has been deleted",
    });
};
exports.deleteWorkreport = deleteWorkreport;
//# sourceMappingURL=delete.js.map