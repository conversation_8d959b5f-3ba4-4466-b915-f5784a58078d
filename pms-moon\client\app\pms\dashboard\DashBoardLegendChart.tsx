"use client";
import { TrendingUp, TrendingDown } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, XAxis } from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartConfig,
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

const chartData = [
  { month: "January", completed: 145, pending: 32 },
  { month: "February", completed: 168, pending: 28 },
  { month: "March", completed: 157, pending: 45 },
  { month: "April", completed: 189, pending: 22 },
  { month: "May", completed: 176, pending: 35 },
  { month: "June", completed: 195, pending: 18 },
];

const chartConfig = {
  completed: {
    label: "Completed Tasks",
    color: "#BCBCBD",
 
  },
  pending: {
    label: "Pending Tasks",
    color: "#7A7A7C",
  },
} satisfies ChartConfig;

export function DashBoardlegendChart() {
  // Calculate percentage change from previous month
  const currentMonth = chartData[chartData.length - 1];
  const previousMonth = chartData[chartData.length - 2];
  const completionRate =
    (currentMonth.completed / (currentMonth.completed + currentMonth.pending)) *
    100;
  const previousRate =
    (previousMonth.completed /
      (previousMonth.completed + previousMonth.pending)) *
    100;
  const percentageChange: any = (completionRate - previousRate).toFixed(1);
  const isImproving = percentageChange > 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>CSA Workload Analysis</CardTitle>
        <CardDescription>
          Task Completion Status: January - June 2024
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig}>
          <BarChart accessibilityLayer data={chartData}>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="month"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip content={<ChartTooltipContent hideLabel />} />
            <ChartLegend content={<ChartLegendContent />} />
            <Bar
              dataKey="completed"
              stackId="a"
              fill="var(--color-completed)"
              radius={[0, 0, 4, 4]}
            />
            <Bar
              dataKey="pending"
              stackId="a"
              fill="var(--color-pending)"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col items-start gap-2 text-sm">
        <div className="flex gap-2 font-medium leading-none">
          {isImproving ? (
            <>
              Completion rate up by {percentageChange}% this month{" "}
              <TrendingUp className="h-4 w-4 text-green-500" />
            </>
          ) : (
            <>
              Completion rate down by {Math.abs(percentageChange)}% this month{" "}
              <TrendingDown className="h-4 w-4 text-red-500" />
            </>
          )}
        </div>
        <div className="leading-none text-muted-foreground">
          Current completion rate: {completionRate.toFixed(1)}%
        </div>
      </CardFooter>
    </Card>
  );
}
