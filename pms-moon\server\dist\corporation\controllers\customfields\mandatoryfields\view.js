"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mandatoryFields = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// --- GET: Fetch Mandatory Fields ---
const mandatoryFields = async (req, res) => {
    try {
        const result = await prisma.$queryRawUnsafe(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'track_sheets'
        AND table_schema = 'public'
    `);
        const excludedFields = ["createdAt", "updatedAt", "id"];
        const fields = result
            .map((col) => col.column_name)
            .filter((field) => !excludedFields.includes(field))
            .map((field) => field
            .split("_")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" "))
            .sort((a, b) => a.localeCompare(b));
        res.json({ mandatoryFields: fields });
    }
    catch (err) {
        console.error("Error fetching TrackSheets columns:", err);
        res.status(500).json({ error: "Failed to get mandatory fields" });
    }
};
exports.mandatoryFields = mandatoryFields;
//# sourceMappingURL=view.js.map