import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";

import { useRef, useState, useMemo, useEffect, useCallback } from "react";
import { getCookie } from "@/lib/helpers";

interface FormProps {
  form: any;
  name: string;
  label: string;
  placeholder: string;
  className?: string;
  type: string;
  disabledStr?: string;
  disable?: boolean;
  isRequired?: boolean;
  route: string;
  searchQuery: any;
  fieldName: any;
  fieldValue: any;
  isEntryPage?: boolean;
  i?: number;
}

const NotesDropDown = ({
  form,
  name,
  label,
  placeholder,
  className,
  type,
  isRequired,
  disable,
  route,
  searchQuery,
  fieldName,
  fieldValue,
  isEntryPage,
  i,
}: FormProps) => {
  // const { debouncedValue } = useDebounce({
  //   searchquery: searchQuery,
  //   route,
  //   delayValue: 0,
  // });
  let noteType = form.getValues(`notes.${i}.note_type_id`);

  const [debouncedValue, setNotesData] = useState([]);
  const fetchData = useCallback(async () => {
    const cookie = await getCookie("freightadmintoken");

    const user_cookie = await getCookie("freightusertoken");
    let final_cookie = cookie
      ? `freight_admin=${cookie}`
      : `freight_user=${user_cookie}`;
    const res = await fetch(`${route}`, {
      next: {
        revalidate: 0,
        tags: ["add-user"],
      },
      method: "GET",
      headers: {
        "Content-type": "application/json",
        Authorization: `Bearer ${final_cookie}`,
      },
    });
    const data = await res.json();

    if (noteType) {
      setNotesData(data.filter((item: any) => item.note_type == noteType));
    }
  }, [noteType, route]);

  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const suggestionsListRef = useRef<HTMLDivElement>(null);

  const handleClick = (item: any) => {
    if (form.formState?.errors[name]?.message) {
      form.clearErrors(name);
    }
    form.setValue(name, item[fieldValue]);
    setIsDropdownOpen(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (debouncedValue.length === 0) return;

    if (e.key === "ArrowDown") {
      setHighlightedIndex((prevIndex) =>
        prevIndex === debouncedValue.length - 1 ? 0 : prevIndex + 1
      );
    } else if (e.key === "ArrowUp") {
      setHighlightedIndex((prevIndex) =>
        prevIndex <= 0 ? debouncedValue.length - 1 : prevIndex - 1
      );
    } else if (e.key === "Enter") {
      e.preventDefault();
      if (highlightedIndex >= 0) {
        handleClick(debouncedValue[highlightedIndex]);
      }
    }

    // Scroll to the highlighted suggestion
    if (suggestionsListRef.current && highlightedIndex >= 0) {
      const suggestionElement = suggestionsListRef.current.children[
        highlightedIndex
      ] as HTMLDivElement;
      suggestionElement?.scrollIntoView({
        behavior: "smooth",
        block: "nearest",
      });
    }
  };

  const inputValue = useMemo(() => form.getValues(name), [form, name]);
  useEffect(() => {
    fetchData();
  }, [fetchData]);
  const handleFocus = useCallback(() => {
    if (debouncedValue.length > 0) {
      setIsDropdownOpen(true);
    }
  }, [debouncedValue.length]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    form.setValue(name, e.target.value);
    setIsDropdownOpen(true);
    if (e.target.value === "") {
      setIsDropdownOpen(false);
    } else {
      setHighlightedIndex(-1); // Reset the highlighted index when the input changes
    }
  };

  return (
    <div>
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem className={cn(className, isEntryPage ? "mb-1 space-y-0.5" : "md:mb-3 space-y-0.5")}>
            <FormLabel
              className={`${
                isEntryPage ? "md:text-sm" : "md:text-base"
              }  text-gray-800 dark:text-gray-300`}
            >
              {label}
              {isRequired && <span className="text-red-500">*</span>}
            </FormLabel>
            <FormControl>
              <Input
                {...field}
                type={type}
                placeholder={placeholder}
                disabled={disable}
                className={`bg-gray-200 dark:bg-gray-700 border-none dark:border-gray-700 placeholder:text-gray-400 dark:placeholder:text-gray-100/50 outline-none focus:!outline-rose-400 ${
                  isEntryPage ? "text-xs h-7" : ""
                }`}
                onChange={handleInputChange}
                onFocus={handleFocus}
                onBlur={() => setTimeout(() => setIsDropdownOpen(false), 200)}
                onKeyDown={handleKeyDown}
                autoComplete="off"
              />
            </FormControl>
            <FormMessage
              className={`${
                isEntryPage ? "text-xs tracking-wider" : " tracking-wider"
              }`}
            />
          </FormItem>
        )}
      />
      <div className="relative">
        {isDropdownOpen && debouncedValue.length > 0 && (
          <div
            className="absolute max-h-[300px] cbar overflow-auto left-0 mt-1 z-50 w-full bg-white shadow-lg"
            ref={suggestionsListRef}
          >
            {debouncedValue.map((item: any, i) => (
              <div
                key={i}
                className={cn(
                  "cursor-pointer border-b p-2 border-gray-200 text-sm hover:bg-gray-200",
                  i === highlightedIndex && "bg-gray-300"
                )}
                onClick={() => handleClick(item)}
              >
                {item[fieldName]}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default NotesDropDown;
