"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";

import { formSubmit } from "@/lib/helpers";
import { carrier_routes, location_api } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createCarrierSchema } from "@/lib/zodSchema";
import React, { useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import TriggerButton from "@/app/_component/TriggerButton";
import { useRouter } from "next/navigation";

const UpdateCarrier = ({ data }: any) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const router = useRouter();

  const { form } = useDynamicForm(createCarrierSchema, {
    name: data?.name || "",
    carrier_2nd_name: data?.carrier_2nd_name || "",
    carrier_code: data?.carrier_code || "",
  });
 
  async function onSubmit(values: any) {
    try {
      const formData = {
        name: values.name.toUpperCase().trim(),
        carrier_2nd_name: values.carrier_2nd_name.toUpperCase().trim(),
        carrier_code: values.carrier_code.toUpperCase(),
      };
      //  (formData, "form");
      const res = await formSubmit(
        `${carrier_routes.UPDATE_CARRIER}/${data.id}`,
        "PUT",
        formData
      );
      if (res.success) {
        toast.success(res.message);
        form.reset();
        router.refresh();
        setIsDialogOpen(false);
        form.reset();
      } else {
        toast.error(res.error || "An error occurred while adding the carrier.");
      }
    } catch (error) {
      toast.error("An error occurred while adding the carrier.");
      console.error(error);
    }
  }
  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger onClick={() => setIsDialogOpen(true)} title="Update">
          <TriggerButton type="edit" />
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[40rem]">
          <DialogHeading
            title="Update Carrier"
            description="Update Carrier Details"
          />
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid grid-cols-2 gap-5 ">
                <FormInput
                  form={form}
                  label="Carrier Name"
                  name="name"
                  type="text"
                  isRequired
                />

                <FormInput
                  form={form}
                  label="Vap ID"
                  name="carrier_code"
                  type="text"
                  isRequired
                />
                </div>
                <div className="grid grid-cols-2 gap-5 ">

                {/* <SelectComp
                  form={form}
                  label="Country"
                  name="country"
                  placeholder="Select Country"
                  isRequired
                >
                  {countries.map((item) => (
                    <SelectItem value={item.name} key={item.id}>
                      {item.name}
                    </SelectItem>
                  ))}
                </SelectComp> */}
                 <FormInput
                  form={form}
                  label="Carrier Name - 2"
                  name="carrier_2nd_name"
                  type="text"
                  isRequired
                />
              </div>

              <SubmitBtn
                className="w-full bg-primary text-secondary hover:bg-primary/90"
                text="Update"
              />
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateCarrier;
