"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCustomField = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// --- Helper function to map field type ---
function mapFieldType(type) {
    switch (type.toUpperCase()) {
        case "DATE":
            return "DATE";
        case "NUMBER":
            return "NUMBER";
        case "TEXT":
            return "TEXT";
        case "AUTO":
            return "AUTO";
        default:
            return "TEXT";
    }
}
// --- POST: Add Custom Fields ---
const createCustomField = async (req, res) => {
    try {
        const { fields } = req.body;
        if (!Array.isArray(fields) || fields.length === 0) {
            return res.status(400).json({ error: "No custom fields provided." });
        }
        const validFields = fields.filter((f) => f.name && f.type);
        if (validFields.length === 0) {
            return res.status(400).json({ error: "No valid custom fields found." });
        }
        const createdFields = [];
        const connectedFieldIds = [];
        let skipped = 0;
        for (const field of validFields) {
            const exists = await prisma.customField.findFirst({
                where: {
                    name: {
                        equals: field.name,
                        mode: "insensitive",
                    },
                },
            });
            if (!exists) {
                const mappedType = mapFieldType(field.type);
                const createData = {
                    name: field.name,
                    type: mappedType,
                    createdBy: field.created_by || "system",
                    updatedBy: field.updated_by || "system",
                };
                if (mappedType === "AUTO") {
                    createData.autoOption = field.autoOption || null;
                }
                const created = await prisma.customField.create({
                    data: createData,
                });
                createdFields.push(created);
                connectedFieldIds.push(created.id);
            }
            else {
                skipped++;
            }
        }
        // Note: Custom fields are now managed through client_custom_field_arrangements table
        // No automatic linking to clients when creating new fields
        return res.status(201).json({ created: createdFields, skipped });
    }
    catch (error) {
        console.error("Error creating custom fields:", error);
        return res.status(500).json({ error: "Server error" });
    }
};
exports.createCustomField = createCustomField;
//# sourceMappingURL=create.js.map