import React from "react";
import { getAllData } from "@/lib/helpers";
import { category_routes, employee_routes } from "@/lib/routePath";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import ExportReportForm from "./ExportReportForm";

const ReportsPage = async () => {
  const categories = await getAllData(category_routes.GETALL_CATEGORY);
  const users = await getAllData(employee_routes.GETALL_USERS);

  return (
    <div className="w-full p-2 pl-4">
      <div className="h-9 flex items-center">
        <AdminNavBar link={"/pms/reports"} name={"Reports"} />
      </div>
      <div className="space-y-2 mb-6">
        <h1 className="text-2xl">Work Reports</h1>
        <p className="text-sm text-gray-700">
          Export work reports by filtering users, categories and date range
        </p>
      </div>
      <ExportReportForm
        categories={categories || []}
        users={users || []}
        onExport={() => {}}
      />
    </div>
  );
};

export default ReportsPage; 