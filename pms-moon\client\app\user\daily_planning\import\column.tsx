import React from "react";
import { ColumnDef } from "@tanstack/react-table";
import { Button } from "@/components/ui/button";
// import UpdateWorkType from "./UpdateWorkType";
import DeleteRow from "@/app/_component/DeleteRow";
import { worktype_routes } from "@/lib/routePath";

export interface WorkType {
  work_type: string;
  category: string;
  id: string;
}

export const column: ColumnDef<WorkType>[] = [
  {
    accessorKey: "work_type",
    header: "Work Type",
  },
  {
    accessorKey: "category",
    header: "Category",
  },
  {
    accessorKey: "is_work_carrier_specific",
    header: "Carrier Specific",
    cell: ({ row }) => {
      const value = row.getValue("is_work_carrier_specific");
      return value ? "Yes" : "No";
    },
  },
  {
    accessorKey: "does_it_require_planning_number",
    header: "Requires Planning No",
    cell: ({ row }) => {
      const value = row.getValue("does_it_require_planning_number");
      return value ? "Yes" : "No";
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    id: "action",
    cell: ({ row }) => {
      const data = row?.original;
      return (
        <div className="flex items-center">
          {/* <UpdateWorkType data={data} /> */}
          {/* <DeleteRow
            route={`${worktype_routes.DELETE_WORKTYPE}/${data?.id}`}
          /> */}
        </div>
      );
    },
  },
];
