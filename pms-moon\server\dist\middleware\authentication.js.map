{"version": 3, "file": "authentication.js", "sourceRoot": "", "sources": ["../../src/middleware/authentication.ts"], "names": [], "mappings": ";;;;;;AACA,8CAA+C;AAC/C,gEAA+B;AAExB,MAAM,YAAY,GAAG,KAAK,EAC/B,GAAQ,EACR,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,oDAAoD;QACpD,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC;QAElC,qCAAqC;QAErC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,GAAG;iBACP,MAAM,CAAC,GAAG,CAAC;iBACX,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC,CAAC;QAC1D,CAAC;QACD,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QAEzC,IAAI,OAAO,CAAC;QAEZ,IAAI,IAAI,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YAChC,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAEpD,MAAM,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;YAEvB,GAAG,CAAC,OAAO,GAAG,EAAE,CAAC;YAEjB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;gBAChB,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;oBACtD,KAAK,EAAE;wBACL,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB;iBACF,CAAC,CAAC;gBAEH,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,8BAA8B;qBACxC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAErD,MAAM,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;YAEvB,GAAG,CAAC,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;gBAChB,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;oBACtD,KAAK,EAAE;wBACL,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB;iBACF,CAAC,CAAC;gBAEH,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAC1B,OAAO,EAAE,KAAK;wBACd,OAAO,EAAE,8BAA8B;qBACxC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QACD,MAAM,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;QACnC,4BAA4B;QAE5B,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC;QACpC,8CAA8C;QAC9C,gCAAgC;QAChC,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAxEW,QAAA,YAAY,gBAwEvB;AAEK,MAAM,sBAAsB,GAAG,KAAK,EACzC,GAAG,EACH,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACxD,sBAAsB;QACtB,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,GAAG;iBACP,MAAM,CAAC,GAAG,CAAC;iBACX,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC,CAAC;QACzD,CAAC;QACD,MAAM,OAAO,GAAQ,sBAAG,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChE,MAAM,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;QAEnC,GAAG,CAAC,cAAc,GAAG,cAAc,CAAC;QACpC,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AArBW,QAAA,sBAAsB,0BAqBjC"}