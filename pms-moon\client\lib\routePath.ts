
export const location_api_prefix = process.env.NEXT_PUBLIC_LOCATION_API_PREFIX;

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

export const corporation_routes = {
  CREATE_CORPORATION: `${BASE_URL}/api/corporation/create-corporation`,
  LOGIN_CORPORATION: `${BASE_URL}/api/corporation/login`,
  GETALL_CORPORATION: `${BASE_URL}/api/corporation/get-all-corporation`,
  UPDATE_CORPORATION: `${BASE_URL}/api/corporation/update-corporation`,
  DELETE_CORPORATION: `${BASE_URL}/api/corporation/delete-corporation`,
  LOGOUT_CORPORATION: `${BASE_URL}/api/corporation/logout`,
};

export const superadmin_routes = {
  LOGIN_SUPERADMIN: `${BASE_URL}/api/superAdmin/login`,
  CREATE_SUPERADMIN: `${BASE_URL}/api/superAdmin/create-superadmin`,
  GETALL_SUPERADMIN: `${BASE_URL}/api/superAdmin/get-all-superadmin`,
  UPDATE_SUPERADMIN: `${BASE_URL}/api/superAdmin/update-superadmin`,
  DELETE_SUPERADMIN: `${BASE_URL}/api/superAdmin/delete-superadmin`,
  LOGOUT_SUPERADMIN: `${BASE_URL}/api/superAdmin/logout`,
};

export const carrier_routes = {
  CREATE_CARRIER: `${BASE_URL}/api/carrier/create-carrier`,
  GETALL_CARRIER: `${BASE_URL}/api/carrier/get-all-carrier`,
  UPDATE_CARRIER: `${BASE_URL}/api/carrier/update-carrier`,
  DELETE_CARRIER: `${BASE_URL}/api/carrier/delete-carrier`,
  GET_CARRIER_BY_CLIENT: `${BASE_URL}/api/carrier/get-carrier-by-client`,
  UPLOAD_CARRIER: `${BASE_URL}/api/carrier/excelCarrier`,
  EXCEL_CARRIER: `${BASE_URL}/api/carrier/export-carrier`,
  GET_CARRIER: `${BASE_URL}/api/carrier/get-carrier`,
};
export const client_routes = {
  CREATE_CLIENT: `${BASE_URL}/api/clients/create-client`,
  GETALL_CLIENT: `${BASE_URL}/api/clients/get-all-client`,
  UPDATE_CLIENT: `${BASE_URL}/api/clients/update-client`,
  DELETE_CLIENT: `${BASE_URL}/api/clients/delete-client`,
  UPLOAD_CLIENT: `${BASE_URL}/api/clients/excelClient`,
  EXCEL_CLIENT: `${BASE_URL}/api/clients/export-client`,
};
export const associate_routes = {
  CREATE_ASSOCIATE: `${BASE_URL}/api/associate/create-associate`,
  GETALL_ASSOCIATE: `${BASE_URL}/api/associate/get-all-associate`,
  UPDATE_ASSOCIATE: `${BASE_URL}/api/associate/update-associate`,
  DELETE_ASSOCIATE: `${BASE_URL}/api/associate/delete-associate`,
};

export const worktype_routes = {
  CREATE_WORKTYPE: `${BASE_URL}/api/worktype/create-worktype`,
  GETALL_WORKTYPE: `${BASE_URL}/api/worktype/get-all-worktype`,
  UPDATE_WORKTYPE: `${BASE_URL}/api/worktype/update-worktype`,
  DELETE_WORKTYPE: `${BASE_URL}/api/worktype/delete-worktype`,
};

export const category_routes = {
  CREATE_CATEGORY: `${BASE_URL}/api/category/create-category`,
  GETALL_CATEGORY: `${BASE_URL}/api/category/get-all-category`,
  UPDATE_CATEGORY: `${BASE_URL}/api/category/update-category`,
  DELETE_CATEGORY: `${BASE_URL}/api/category/delete-category`,
};

export const branch_routes = {
  CREATE_BRANCH: `${BASE_URL}/api/branch/create-branch`,
  GETALL_BRANCH: `${BASE_URL}/api/branch/get-all-branch`,
  UPDATE_BRANCH: `${BASE_URL}/api/branch/update-branch`,
  DELETE_BRANCH: `${BASE_URL}/api/branch/delete-branch`,
};

export const employee_routes = {
  LOGIN_USERS: `${BASE_URL}/api/users/login`,
  LOGOUT_USERS: `${BASE_URL}/api/users/logout`,
  LOGOUT_SESSION_USERS: `${BASE_URL}/api/users/sessionlogout`,
  CREATE_USER: `${BASE_URL}/api/users/create-user`,
  GETALL_USERS: `${BASE_URL}/api/users/get-all-user`,
  GETALL_SESSION: `${BASE_URL}/api/users//get-all-session`,
  GETCURRENT_USER: `${BASE_URL}/api/users/get-current-user`,
  UPDATE_USERS: `${BASE_URL}/api/users/update-user`,
  DELETE_USERS: `${BASE_URL}/api/users/delete-user`,
  UPLOAD_USERS_IMAGE: `${BASE_URL}/api/users/upload-profile-image`,
  UPLOAD_USERS_FILE: `${BASE_URL}/api/users/excel`,
};

export const usertitle_routes = {
  CREATE_USERTITLE: `${BASE_URL}/api/usertitle//get-all-usertitle`,
  GETALL_USERTITLE: `${BASE_URL}/api/usertitle/get-all-usertitle`,
};
export const setup_routes = {
  CREATE_SETUP: `${BASE_URL}/api/client-carrier/create-setup`,
  GETALL_SETUP: `${BASE_URL}/api/client-carrier/get-all-setup`,
  GETALL_SETUP_BYID: `${BASE_URL}/api/client-carrier/get-all-setupbyId`,
  UPDATE_SETUP: `${BASE_URL}/api/client-carrier/update-setup`,
  DELETE_SETUP: `${BASE_URL}/api/client-carrier/delete-setup`,
  EXCEL_SETUP: `${BASE_URL}/api/client-carrier/excelClientCarrier`,
};
export const location_api = {
  GET_COUNTRY: `${location_api_prefix}/api/location/country`,
  GET_STATE: `${location_api_prefix}/api/location/statename`,
  GET_CITY: `${location_api_prefix}/api/location/citybystate`,
};
export const workreport_routes = {
  CREATE_WORKREPORT: `${BASE_URL}/api/workreport/create-workreport`,
  CREATE_WORKREPORT_MANUALLY: `${BASE_URL}/api/workreport/create-workreport-manually`,
  GETALL_WORKREPORT: `${BASE_URL}/api/workreport/get-all-workreport`,
  GET_USER_WORKREPORT: `${BASE_URL}/api/workreport/get-user-workreport`,
  GET_CURRENT_USER_WORKREPORT: `${BASE_URL}/api/workreport/get-current-user-workreport`,
  UPDATE_WORKREPORT: `${BASE_URL}/api/workreport/update-workreport`,
  DELETE_WORKREPORT: `${BASE_URL}/api/workreport/delete-workreport`,
  UPDATE_WORK_REPORT: `${BASE_URL}/api/workreport/update-workreports`,
  EXCEL_REPORT: `${BASE_URL}/api/workreport/get-workreport`,
  GET_CURRENT_USER_WORKREPORT_STATUS_COUNT: `${BASE_URL}/api/workreport`,
};

export const customizeReport = {
  EXPORT_CUSTOMIZE_REPORT: `${BASE_URL}/api/customizeReport/reports`,
}

export const rolespermission_routes = {
  GETALL_ROLES: `${BASE_URL}/api/rolespermission/get-all-roles`,
  ADD_ROLE: `${BASE_URL}/api/rolespermission/add-roles`,
  GETALL_PERMISSION: `${BASE_URL}/api/rolespermission/get-all-permissions`,
  UPDATE_ROLE: `${BASE_URL}/api/rolespermission/update-roles`,
  DELETE_ROLE: `${BASE_URL}/api/rolespermission/delete-roles`,
};
export const upload_file = {
  UPLOAD_FILE: `${BASE_URL}/api/upload/upload-file`,
  UPLOAD_FILE_TWOTEN: `${BASE_URL}/api/upload/upload-csv-twoten`,
};

export const daily_planning_details = {
  CREATE_DAILY_PLANNING_DETAILS: `${BASE_URL}/api/dailyplanning/create-dailyplanningdetails`,
};
export const daily_planning = {
  CREATE_DAILY_PLANNING: `${BASE_URL}/api/dailyplanning/create-dailyplanning`,
  GETALL_DAILY_PLANNING: `${BASE_URL}/api/dailyplanning/get-all-dailyplanning`,
  GETSPECIFIC_DAILY_PLANNING: `${BASE_URL}/api/dailyplanning/get-specific-dailyplanning`,
  GET_DAILY_PLANNING_BY_ID: `${BASE_URL}/api/dailyplanning/get-dailyplanning-by-id`,
  UPDATE_DAILY_PLANNING: `${BASE_URL}/api/dailyplanning/update-dailyplanning`,
  DELETE_DAILY_PLANNING: `${BASE_URL}/api/dailyplanning/delete-dailyplanning`,
  GET_USER_DAILY_PLANNING_BY_VISIBILITY: `${BASE_URL}/api/dailyplanning/get-user-dailyplanningByVisibility`,
};
export const daily_planning_details_routes = {
  CREATE_DAILY_PLANNING_DETAILS: `${BASE_URL}/api/dailyplanningdetails/create-dailyplanningdetails`,
  EXCEL_DAILY_PLANNING_DETAILS: `${BASE_URL}/api/dailyplanningdetails/excel-dailyplanningdetails`,
  GETALL_DAILY_PLANNING_DETAILS: `${BASE_URL}/api/dailyplanningdetails/get-specific-dailyplanningdetails`,
  GET_DAILY_PLANNING_DETAILS_ID: `${BASE_URL}/api/dailyplanningdetails/get-all-dailyplanningdetails`,
  GET_DAILY_PLANNING_DETAILS_MANUAL: `${BASE_URL}/api/dailyplanningdetails`,
  GET_DAILY_PLANNING_DETAILS_TYPE: `${BASE_URL}/api/dailyplanningdetails`,
  UPDATE_DAILY_PLANNING_DETAILS: `${BASE_URL}/api/dailyplanningdetails/update-dailyplanningdetails`,
  DELETE_DAILY_PLANNING_DETAILS: `${BASE_URL}/api/dailyplanningdetails/delete-dailyplanningdetails`,
  UPDATE_DAILY_PLANNING_DETAILS_STATEMENT:  `${BASE_URL}/api/dailyplanningdetails/update-dailyplanningdetails-statement`,
};

export const search_routes = {
  GET_SEARCH: `${BASE_URL}/api/search`,
};

export const trackSheets_routes = {
  CREATE_TRACK_SHEETS: `${BASE_URL}/api/track-sheets`,
  GETALL_TRACK_SHEETS: `${BASE_URL}/api/track-sheets/clients`,
  UPDATE_TRACK_SHEETS: `${BASE_URL}/api/track-sheets`,
  DELETE_TRACK_SHEETS: `${BASE_URL}/api/track-sheets`,
};

export const clientCustomFields_routes = {
  GET_CLIENT_CUSTOM_FIELDS: `${BASE_URL}/api/client-custom-fields/clients`,
};

export const legrandMapping_routes = {
  GET_LEGRAND_MAPPINGS: `${BASE_URL}/api/legrand-mappings`,
};

export const manualMatchingMapping_routes = {
  GET_MANUAL_MATCHING_MAPPINGS: `${BASE_URL}/api/manual-matching-mappings`,
};

export const customFields_routes = {
  GET_ALL_CUSTOM_FIELDS: `${BASE_URL}/api/custom-fields`,
  GET_CUSTOM_FIELDS_WITH_CLIENTS: `${BASE_URL}/api/custom-fields-with-clients`,
  GET_MANDATORY_FIELDS: `${BASE_URL}/api/mandatory-fields`,
};