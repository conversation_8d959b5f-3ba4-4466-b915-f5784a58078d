"use server";
import { cookies } from "next/headers";
import { revalidatePath } from "next/cache";
import Cookies from "js-cookie";
export const getCookie = (cookieName: string) => {
  const cookie = cookies().get(cookieName);
  if (cookie) {
    return cookie.value;
  }
};

export const removeCookie = (cookieToBeDeleted: string) => {
  const cookie = cookies().get(cookieToBeDeleted);
  if (cookie) {
    cookies().delete(cookieToBeDeleted);
  }
};

// export const formSubmit = async (
//   url: string,
//   request: string,
//   formData: any,
//   revPath?: string
// ) => {
//   const cookie = await getCookie("corporationtoken");
//   const res = await fetch(url, {
//     next: {
//       revalidate: 0,
//       tags: ["add-user"],
//     },
//     method: request,

//     headers: {
//       "Content-type": "application/json",
//       cookie: `${cookie}`,
//     },
//     credentials: "include",
//     body: JSON.stringify(formData),
//   });
//   revPath && revalidatePath(revPath);
//   const data = await res.json();

//   return data;
// };
export const formSubmit = async (
  url: string,
  request: string,
  formData: any,
  revPath?: string
) => {
  // const cookie = await getCookie("corporationtoken");
  // const userCookie = await getCookie("token");
  const corporationToken = await getCookie("corporationtoken");
  const userToken = await getCookie("token");

  // Choose the cookie based on availability
  const cookie = corporationToken || userToken;

  if (!cookie) {
    throw new Error("No valid token found");
  }
  const res = await fetch(url, {
    next: {
      revalidate: 0,
      tags: ["add-user"],
    },
    method: request,

    headers: {
      "Content-type": "application/json",
      cookie: `${cookie}`,
    },
    credentials: "include",
    body: JSON.stringify(formData),
  });
  revPath && revalidatePath(revPath);
  const data = await res.json();

  return data;
};

export const formSubmitOne = async ({
  url,
  request,
  formData,
  revPath,
}: {
  url: string;
  request: string;
  formData: any;
  revPath?: string;
}) => {
  // const cookie = await getCookie("corporationtoken");
  // const userCookie = await getCookie("token");
  const corporationToken = await getCookie("corporationtoken");
  const userToken = await getCookie("token");

  // Choose the cookie based on availability
  const cookie = corporationToken || userToken;

  if (!cookie) {
    throw new Error("No valid token found");
  }
  const res = await fetch(url, {
    next: {
      revalidate: 0,
      tags: ["add-user"],
    },
    method: request,

    headers: {
      "Content-type": "application/json",
      cookie: `${cookie}`,
    },
    credentials: "include",
    body: JSON.stringify(formData),
  });
  revPath && revalidatePath(revPath);
  const data = await res.json();

  return data;
};

export const formSuperAdminSubmit = async (
  url: string,
  request: string,
  formData: any,
  revPath?: string
) => {
  const cookie = await getCookie("superadmintoken");
  const res = await fetch(url, {
    next: {
      revalidate: 0,
      tags: ["add-user"],
    },
    method: request,

    headers: {
      "Content-type": "application/json",
      cookie: `${cookie}`,
    },
    credentials: "include",
    body: JSON.stringify(formData),
  });
  revPath && revalidatePath(revPath);
  const data = await res.json();

  return data;
};

export const getAllData = async (route: string) => {
  console.time("GetAllData | Time taken for" + route);
  const cookie = await getCookie("corporationtoken");
  const cust_cookie = await getCookie("token");
  try {
    let final_cookie = cookie ? `${cookie}` : `customer=${cust_cookie}`;
    const res = await fetch(route, {
      headers: {
        "Content-Type": "application/json",
        cookie: `${final_cookie}`,
        // freightusertoken: freightusertoken || "",
      },
      credentials: "include",
    });
    if (!res.ok) {
      console.error(`Error fetching data: ${res.status} ${res.statusText}`);
      console.timeEnd("GetAllData | Time taken for" + route);
      return [];
    }
    console.timeEnd("GetAllData | Time taken for" + route);
    return res.json();
  } catch (error) {
    console.error("Error:", error);
    console.timeEnd("GetAllData | Time taken for" + route);
    return [];
  }
};

export const deleteRow = async (url: string) => {
  const cookie = await getCookie("corporationtoken");
  const cust_cookie = await getCookie("token");
  let final_cookie = cookie ? `${cookie}` : `customer=${cust_cookie}`;

  const res = await fetch(url, {
    method: "DELETE",
    credentials: "include",
    headers: {
      "Content-type": "application/json",
      cookie: `${final_cookie}`,
    },
  });
  const data = await res.json();  
  // //  ;

  return data;
};

export const getCookieHelper = (cookieName: any) => {
  return Cookies.get(cookieName);
};

export const hasPermission = ({
  permission,
  permission_data,
}: {
  permission: string;
  permission_data: string[];
}) => {

  const hasAllowAll = permission_data?.includes("allow_all");
  const hasSpecificPermission = permission_data?.includes(permission);

  return hasAllowAll || hasSpecificPermission;
};

export const PermissionWrapper = ({
  permissions,
  requiredPermissions,
  children,
}: any) => {
  // Check if the user has any of the required permissions
  //  (permissions);
  const hasPermission =
    permissions?.includes("allow_all") || // Unconditional access if "allow_all" is present
    requiredPermissions?.some((perm: any) => permissions?.includes(perm));
  //  (hasPermission);
  return hasPermission ? children : null;
};
