{"version": 3, "file": "view.js", "sourceRoot": "", "sources": ["../../../../src/corporation/controllers/clientCarrier/view.ts"], "names": [], "mappings": ";;;AAAA,oDAAqD;AAE9C,MAAM,iBAAiB,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAClD,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC/C,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,IAAI;aACd;YACD,OAAO,EAAE;gBACP,EAAE,EAAC,MAAM;aACV;SACF,CACA,CAAC;QACF,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAnBW,QAAA,iBAAiB,qBAmB5B;AAEK,MAAM,qBAAqB,GAAG,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACtD,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,IAAI,CAAC;QACH,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC;YAC/C,KAAK,EAAE;gBACL,SAAS,EAAG,MAAM,CAAC,EAAE,CAAC;aACvB;YACD,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,IAAI;aACd;YACD,OAAO,EAAC;gBACN,EAAE,EAAC,MAAM;aACV;SACF,CACA,CAAC;QACF,IAAI,IAAI,EAAE,CAAC;YACT,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpC,CAAC;QACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAA,qBAAW,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAvBW,QAAA,qBAAqB,yBAuBhC"}