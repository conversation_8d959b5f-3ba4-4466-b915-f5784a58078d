"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logoutSuperAdmin = void 0;
const helpers_1 = require("../../utils/helpers");
const logoutSuperAdmin = async (req, res) => {
    try {
        const { cookieTobeDeleted } = req.body;
        //  ('cookiedelete superadmin',cookieTobeDeleted);
        if (!cookieTobeDeleted) {
            return res.status(400).json({
                success: false,
                error: "No token provided for logout.",
            });
        }
        return res.clearCookie('superadmintoken', {
            httpOnly: true,
        }).status(200).json({
            success: true,
            message: "Logout successful",
        });
    }
    catch (error) {
        (0, helpers_1.handleError)(res, error);
    }
};
exports.logoutSuperAdmin = logoutSuperAdmin;
//# sourceMappingURL=logout.js.map