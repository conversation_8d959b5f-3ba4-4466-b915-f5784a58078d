"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../controllers/create");
const login_1 = require("../../utils/login");
const update_1 = require("../controllers/update");
const delete_1 = require("../controllers/delete");
const view_1 = require("../controllers/view");
const logout_1 = require("../controllers/logout");
const authentication_1 = require("../../middleware/authentication");
const router = (0, express_1.Router)();
router.post("/create-corporation", create_1.createCorporation);
router.post("/login", login_1.corporationLogin);
router.post("/logout", logout_1.logout);
router.get("/get-all-corporation", authentication_1.authenticate, view_1.viewCorporation);
router.put("/update-corporation/:id", authentication_1.authenticate, update_1.updateCorporation);
router.delete("/delete-corporation/:id", authentication_1.authenticate, delete_1.deleteCorporation);
exports.default = router;
//# sourceMappingURL=corporationroutes.js.map