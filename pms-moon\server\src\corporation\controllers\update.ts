import { Request, Response } from "express";
import { updateItem } from "../../utils/operation";
import { updateUserClients } from "./userClients/update";

export const updateCorporation = async (req, res) => {
  const id = req.params.id;

  const fields = {
    username: req.body.username,
    email: req.body.email,
    country: req.body.country,
    state: req.body.state,
    city: req.body.city,
    address: req.body.address,
  };
  await updateItem({
    model: "corporation",
    fieldName: "corporation_id",
    fields: fields,
    id: Number(id),
    res,
    req,
    successMessage: "corporation has been updated",
  });
};

export const updateUser = async (req, res) => {
  const { corporation_id } = req;
  const id = Number(req.params.id);

  const fields = {
    username: req.body.username,
    firstName: req.body.firstName,
    lastName: req.body.lastName,
    email: req.body.email.toLowerCase(),
    role_id: Number(req.body.role_id),
    level: Number(req.body.level),
    parent_id: Number(req.body.parent_id),
    corporation_id: Number(corporation_id),
    branch_id: req.body.branch_id,
    date_of_joining: new Date(req.body.date_of_joining),
  };

  try {
    await updateItem({
      model: "user",
      fieldName: "id",
      fields,
      id,
      res,
      req,
      successMessage: "User has been updated",
    });

    const updateClientsResult = await updateUserClients(id, req.body.clients);

    if (!updateClientsResult.success) {
      return res
        .status(500)
        .json({ success: false, error: updateClientsResult.error });
    }
  } catch (error) {
    console.error("Error updating user:", error);
    return res
      .status(500)
      .json({
        success: false,
        error: "An error occurred while updating the user",
      });
  }
};