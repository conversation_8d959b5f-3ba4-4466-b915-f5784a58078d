import { Router } from "express";
import { authenticate } from "../../../middleware/authentication";
import { createClientCarrier, excelClientCarrier } from "../../controllers/clientCarrier/create";
import { viewClientCarrier, viewClientCarrierById } from "../../controllers/clientCarrier/view";
import { updateClientCarrier } from "../../controllers/clientCarrier/update";
import { deleteClientCarrier } from "../../controllers/clientCarrier/delete";
import multer from "multer";
const router = Router();

const DIR = "./src/public";

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, DIR);
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + "-" + file.originalname);
    },
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 },
}).single("file");


router.post('/excelClientCarrier', upload, excelClientCarrier); // clientCarrier

router.post("/create-setup", authenticate, createClientCarrier);
router.get("/get-all-setup", authenticate, viewClientCarrier);
router.get("/get-all-setupbyId/:id", authenticate, viewClientCarrierById);
router.put("/update-setup/:id", authenticate, updateClientCarrier);
router.delete("/delete-setup/:id", authenticate, deleteClientCarrier);

export default router;
