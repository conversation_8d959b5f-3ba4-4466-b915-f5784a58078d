"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/branch/create");
const authentication_1 = require("../../../middleware/authentication");
const checkPermission_1 = require("../../../middleware/checkPermission");
const view_1 = require("../../controllers/branch/view");
const update_1 = require("../../controllers/branch/update");
const delete_1 = require("../../controllers/branch/delete");
const router = (0, express_1.Router)();
router.post("/create-branch", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("BRANCH MANAGEMENT", "create-branch"), create_1.createBranch);
router.get("/get-all-branch", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("BRANCH MANAGEMENT", "view-branch"), view_1.viewBranch);
router.put("/update-branch/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("BRANCH MANAGEMENT", "update-branch"), update_1.updateBranch);
router.delete("/delete-branch/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("BRANCH MANAGEMENT", "delete-branch"), delete_1.deleteBranch);
exports.default = router;
//# sourceMappingURL=branchroutes.js.map