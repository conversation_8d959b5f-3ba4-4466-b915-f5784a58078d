"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkClientModule = exports.checkUserPermission = exports.hasPermission = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
const hasPermission = async (user_id, module, action, client_id) => {
    const rolePermission = await prisma.user.findFirst({
        where: {
            id: user_id,
        },
        select: {
            role: {
                select: {
                    client_id: true,
                    role_permission: {
                        select: {
                            permission: {
                                select: {
                                    module: true,
                                    action: true,
                                },
                            },
                        },
                    },
                },
            },
        },
    });
    if (rolePermission.role.role_permission.length > 0) {
        const permission = rolePermission.role.role_permission.find((role) => role.permission.module === module);
        return !!permission;
    }
    else {
        return false;
    }
};
exports.hasPermission = hasPermission;
const checkUserPermission = async ({ req, res, action, permissiontype, client_id, }) => {
    const id = req.user_id;
    if (!id) {
        return true;
    }
    const hasPermissionFlag = await (0, exports.hasPermission)(Number(id), permissiontype, action, client_id);
    if (!hasPermissionFlag) {
        res.status(403).json({ success: false, message: "Permission denied" });
        return false;
    }
    return true;
};
exports.checkUserPermission = checkUserPermission;
const checkClientModule = async ({ client_id, user_id, }) => {
    const user = await prisma.user.findFirst({
        where: {
            id: user_id,
            role: {
                client_id: Number(client_id),
            },
        },
        select: {
            role: {
                select: {
                    client_id: true,
                },
            },
        },
    });
    return true;
};
exports.checkClientModule = checkClientModule;
//# sourceMappingURL=permissions.js.map