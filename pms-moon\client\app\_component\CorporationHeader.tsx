import React from 'react'
interface HeaderProps {
    CorporationNamespan1?: string,
    CorporationNamespan2?: string
    FullCorporationName?: string,

}
const CorporationHeader: React.FC<HeaderProps> = ({CorporationNamespan1, CorporationNamespan2}) => {
  return (
    <>
    <div className="bg-[#E9E9E9] p-2 px-5  rounded-t-lg flex items-center  ">
    
   {/*ONLY USE WHEN DATA COMES FROM BACKEND 
   <span className="text-2xl font-bold ">{CorporationNamespan1}</span>
    <span className="text-2xl  font-bold text-[#0191AF]">{CorporationNamespan2}</span>
    <span className="text-2xl font-bold ">{FullCorporationName}</span>USE WHEN COMPANY NAME WITH SAME STYLE
    */}
    <span className="text-2xl font-bold ">XData</span>
    <span className="text-2xl  font-bold text-[#0191AF]">Logix</span>
    </div>
    
    </>
  )
}

export default CorporationHeader

