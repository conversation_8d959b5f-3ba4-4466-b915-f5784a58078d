import { getAllData, getCookie } from "@/lib/helpers";
import {
  daily_planning,
  daily_planning_details_routes,
  employee_routes,
} from "@/lib/routePath";
import { useParams } from "next/navigation";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import { NavBar } from "@/app/_component/NavBar";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import Sidebar from "@/components/sidebar/Sidebar";
import ViewDailyPlanningDetails from "../../ViewDailyPlanningDetails";

const Page = async ({ params }: { params: { id: string } }) => {
  const { id } = params;
  const allDailyPlanningDetails = await getAllData(
    `${daily_planning_details_routes.GET_DAILY_PLANNING_DETAILS_ID}/${id}`
  );
  const allDailyPlanning = await getAllData(
    `${daily_planning.GETSPECIFIC_DAILY_PLANNING}/${id}`
  );
  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");
  //  ("Corporation Cookie:", corporationCookie);

  // If corporationCookie doesn't exist, manually remove 'allow_all' from permissions
  // let permissions = userPermissions;
  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  return (
    <>
      <SidebarProvider>
        <Sidebar {...{ permissions }} profile={userData} />
        <NavBar />
        <main className="pt-1">
          <SidebarTrigger />
        </main>
        <div className="p-14 mt-8 w-full">
          <div className="flex justify-between">
            <div>
              <div className="text-xl font-bold">Daily Planning Date</div>
              {allDailyPlanning &&
                allDailyPlanning.map(
                  (details: any) => details.daily_planning_date.split("T")[0]
                )}
            </div>
            <div>
              <div className="text-xl font-bold">Client</div>
              {allDailyPlanning &&
                allDailyPlanning.map(
                  (details: any) => details.client.client_name
                )}
            </div>
          </div>
          <div className="pt-5 ">
            <ViewDailyPlanningDetails data={allDailyPlanningDetails} />
          </div>
        </div>
      </SidebarProvider>
    </>
  );
};
export default Page;
