"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteUser = exports.deleteCorporation = void 0;
const operation_1 = require("../../utils/operation");
const deleteCorporation = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "corporation",
        fieldName: "corporation_id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "corporation has been deleted",
    });
};
exports.deleteCorporation = deleteCorporation;
const deleteUser = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "user",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "User has been deleted",
    });
};
exports.deleteUser = deleteUser;
//# sourceMappingURL=delete.js.map