"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteCarrier = void 0;
const operation_1 = require("../../../utils/operation");
const deleteCarrier = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "carrier",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "carrier has been deleted",
    });
};
exports.deleteCarrier = deleteCarrier;
//# sourceMappingURL=delete.js.map