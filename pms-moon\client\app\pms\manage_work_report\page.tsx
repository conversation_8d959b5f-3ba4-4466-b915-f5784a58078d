"use server";
import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  carrier_routes,
  category_routes,
  client_routes,
  employee_routes,
  workreport_routes,
  worktype_routes,
} from "@/lib/routePath";
import React from "react";
import ViewWorkReport from "./ViewWorkReport";
import ExportReport from "./ExportReport";
import ManageWorkReport from "./ManageWorkReport";
// import CreateWorkReport from './CreateWorkReport'

const page = async ({
  searchParams,
}: {
  searchParams: {
    pageSize?: string;
    page?: string;
    fDate?: string;
    tDate: string;
    search: string;
    Client: string;
    Carrier: string;
    Username: string;
    Work: string;
    Category: string;
    Type: string;
    // StartTime: string;
    // EndTime: string;
    Workstatus: string;
    // TimeSpent: string;
    ActualNumber: string;
    "Actual No": string;
    Notes: string;
    SwitchType: string;
  };
}) => {
  const {
    page = "1",
    pageSize = "50",
    fDate,
    tDate,
    search,
    Client,
    Carrier,
    Username,
    Work,
    Category,
    Type,
    // StartTime,
    // EndTime,
    Workstatus,
    // TimeSpent,
    "Actual No": ActualNumber,
    Notes,
    SwitchType
  } = searchParams;

  const params = new URLSearchParams();

  if (pageSize) params.append("pageSize", pageSize);
  if (page) params.append("page", page);
  if (Client) params.append("Client", Client);
  if (Carrier) params.append("Carrier", Carrier);
  if (Work) params.append("Work", Work);
  if (Category) params.append("Category", Category);
  if (Type) params.append("Type", Type);
  if (SwitchType) params.append("SwitchType", SwitchType);
  if (Username) params.append("Username", Username);
  // if (StartTime) params.append("StartTime", StartTime);
  // if (EndTime) params.append("EndTime", EndTime);
  if (Workstatus) params.append("Workstatus", Workstatus);
  // if (TimeSpent) params.append("TimeSpent", TimeSpent);
  if (ActualNumber) params.append("ActualNumber", ActualNumber);
  if (Notes) params.append("Notes", Notes);
  if (fDate && tDate) {
    params.append("fDate", fDate);
    params.append("tDate", tDate);
  }
  const apiUrl = `${workreport_routes.GETALL_WORKREPORT}?${params.toString()}`;
  const data = await getAllData(apiUrl);

  const userData = await getAllData(employee_routes.GETCURRENT_USER);
  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.action
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;

  return (
    <>
      <ManageWorkReport data={data} permissions={permissions} params={params} />
    </>
  );
};

export default page;
