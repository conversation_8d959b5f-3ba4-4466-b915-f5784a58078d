"use client";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import SubmitBtn from "@/app/_component/SubmitBtn";
import { Form } from "@/components/ui/form";
import { SelectItem } from "@/components/ui/select";
import { formSubmit } from "@/lib/helpers";
import { carrier_routes, category_routes, location_api } from "@/lib/routePath";
import useDynamicForm from "@/lib/useDynamicForm";
import { createCarrierSchema, createCategorySchema } from "@/lib/zodSchema";
import React, { useCallback, useState } from "react";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import DialogHeading from "@/app/_component/DialogHeading";
import TriggerButton from "@/app/_component/TriggerButton";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { FaHistory } from "react-icons/fa";
import ViewHistory from "./ViewHistory";

const PauseResumeHistory = ({ data }: any) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const router = useRouter();


  const { form } = useDynamicForm(createCategorySchema, {
    name: data?.category_name || "",
  });

  async function onSubmit(values: any) {
    try {
      const formData = {
        name: values.name.toUpperCase(),
      };
      //  (formData, "form");
      const res = await formSubmit(
        `${category_routes.UPDATE_CATEGORY}/${data.id}`,
        "PUT",
        formData
      );
      //  (res, "res");
      if (res.success) {
        toast.success(res.message);
        router.refresh();
        form.reset();
        setIsDialogOpen(false);
      } else {
        toast.error(res.error || "An error occurred while adding the carrier.");
      }
    } catch (error) {
      toast.error("An error occurred while adding the carrier.");
      console.error(error);
    }
  }

  const handleOpenChange = useCallback((open: boolean) => {
    setIsDialogOpen(open);
  }, []);
  
  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={handleOpenChange}>
        <DialogTrigger onClick={() => setIsDialogOpen(true)}>
          <Button variant="outline" className="border-none">
            <FaHistory />
          </Button>
        </DialogTrigger>
        <DialogContent className="md:min-w-[50rem] min-w-[25rem]">
          <DialogHeading
            title="Activity Log"
            description="Pause/Resume History"
          />
          <ViewHistory data={data}/>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PauseResumeHistory;
