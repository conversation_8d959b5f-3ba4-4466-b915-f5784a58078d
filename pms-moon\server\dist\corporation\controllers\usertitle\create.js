"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUserTitle = void 0;
const operation_1 = require("../../../utils/operation");
const createUserTitle = async (req, res) => {
    const fields = {
        title: req.body.title,
        level: req.body.level,
    };
    await (0, operation_1.createItem)({
        model: "userTitle",
        fieldName: "id",
        fields: fields,
        res: res,
        req: req,
        successMessage: "User title has been created",
    });
};
exports.createUserTitle = createUserTitle;
//# sourceMappingURL=create.js.map