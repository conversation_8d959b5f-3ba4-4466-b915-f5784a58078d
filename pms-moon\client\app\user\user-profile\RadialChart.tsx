"use client"
import { TrendingUp } from "lucide-react"
import { Label, PolarGrid, PolarRadiusAxis, RadialBar, RadialBarChart } from "recharts"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { type ChartConfig, ChartContainer } from "@/components/ui/chart"

const RadialChart = () => {
  const totalWork = 500
  const completedWork = 300
  const remainingWork = totalWork - completedWork

  const chartData = [
    { name: "Completed", value: completedWork, fill: "hsl(var(--chart-2))" },
    { name: "Remaining", value: remainingWork, fill: "hsl(var(--chart-5))" },
  ]

  const chartConfig = {
    completed: {
      label: "Completed Work",
      color: "hsl(var(--chart-1))",
    },
    remaining: {
      label: "Remaining Work",
      color: "hsl(var(--chart-2))",
    },
  } satisfies ChartConfig

  return (
    <div className="w-full">
      <Card className="flex w-full flex-col">
        <CardHeader className="items-center pb-0">
          <CardTitle>Work Distribution</CardTitle>
          <CardDescription>Leader to Team Member</CardDescription>
        </CardHeader>
        <CardContent className="flex-1 pb-0">
          <ChartContainer config={chartConfig} className="mx-auto w-full aspect-square max-h-[250px]">
            <RadialBarChart data={chartData} startAngle={0} endAngle={360} innerRadius={80} outerRadius={110}>
              <PolarGrid
                gridType="circle"
                radialLines={false}
                stroke="none"
                className="first:fill-muted last:fill-background"
                polarRadius={[86, 74]}
              />
              <RadialBar dataKey="value" background cornerRadius={10} />
              <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
                <Label
                  content={({ viewBox }) => {
                    if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                      return (
                        <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle" dominantBaseline="middle">
                          <tspan
                            x={viewBox.cx}
                            y={(viewBox.cy || 0) - 20}
                            className="fill-foreground text-4xl font-bold"
                          >
                            {completedWork}
                          </tspan>
                          <tspan x={viewBox.cx} y={(viewBox.cy || 0) + 10} className="fill-muted-foreground text-sm">
                            Completed
                          </tspan>
                          <tspan x={viewBox.cx} y={(viewBox.cy || 0) + 30} className="fill-muted-foreground text-sm">
                            out of {totalWork}
                          </tspan>
                        </text>
                      )
                    }
                  }}
                />
              </PolarRadiusAxis>
            </RadialBarChart>
          </ChartContainer>
        </CardContent>
        <CardFooter className="flex-col gap-2 text-sm">
          <div className="flex items-center gap-2 font-medium leading-none">
            {(completedWork / totalWork) * 100}% of work completed
            <TrendingUp className="h-4 w-4" />
          </div>
          <div className="leading-none text-muted-foreground">{remainingWork} tasks remaining</div>
        </CardFooter>
      </Card>
    </div>
  )
}

export default RadialChart

