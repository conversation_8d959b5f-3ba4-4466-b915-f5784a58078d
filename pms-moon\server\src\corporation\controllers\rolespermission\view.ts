import {
  getAllRoleParams,
  getFreightSetupParams,
  getRoleParams,
  getRolePermissionParams,
} from "./find";
import { Request, Response } from "express";
import { getData } from "./utility";
import { getItem } from "../../../utils/operation";
import { handleError } from "../../../utils/helpers";
import { checkUserPermission } from "../../../utils/permissions";

export const getPermissions = async (req, res: Response) => {
  try {
    const getPermissions = await (prisma as any).permissions.findMany({
      select: {
        id: true,
        module: true,
        action: true,
      },
      orderBy:{
        id:'desc'
      }
    });

    const groupedPermissions = getPermissions.reduce((acc, perm) => {
      const key = `${perm.action}`;
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(perm);
      return acc;
    }, {});

    res.status(200).json(groupedPermissions);
  } catch (error) {
    console.log(error);
    res.status(500).json({
      message: error.message || "something went wrong",
    });
  }
};

export const viewRoles = async (req, res) => {
  try {
    const params = await getAllRoleParams(req.corporation_id);

    // Querying the 'roles' model and including related tables
    const roles = await prisma.roles.findMany({
      where: {
        corporation_id: req.corporation_id, // Assuming you're filtering by 'corporation_id'
      },
      orderBy: {
        created_at: "asc", // Sorting by 'created_at' in ascending order
      },
      include: {
        corporation: true, // Including the 'Corporation' relation
        permissions: true, // Including the 'Permissions' relation
        role_permission: {
          select: {
            permission: {
              select: {
                action: true,
                module: true,
                id: true,
              },
            },
          },
        }, // Including the 'RolePermission' relation
        User: true, // Including the 'User' relation
      },
    });

    // Handling case when no roles are found
    if (!roles || roles.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No roles found",
      });
    }

    // Returning the roles with all related data
    res.status(200).json(roles);
  } catch (error) {
    console.error(error);
    res.status(500).json({
      success: false,
      message: error.message || "Something went wrong",
    });
  }
};

export const viewRolesPermissions = async (
  req,
  res: Response
): Promise<any> => {
  try {
    const params = await getRoleParams(req.body.corporation_id);
    const roles = await getData("roles", params);
    const hasPermission = await checkUserPermission({
      req,
      res: res as Response,
      action: "USER MANAGEMENT",
      permissiontype: "manageRoles",
    });
    if (hasPermission) {
      if (!roles || roles.length === 0) {
        return res.status(404).json({
          success: false,
          message: "No roles found",
        });
      }
      //  (roles, "roles");
      const rolesPermission = await Promise.all(
        roles.map(async (item) => {
          const data = await getRolePermissionParams(item.role_id);
          const permission = data.map((perm) => ({
            module: perm.permission.module,
            permission_id: perm.permission.permission_id,
          }));
          // const client_id = JSON.parse(item.client_id);
          // const client_names = await prisma.client.findMany({
          //   where: {
          //     client_id: { in: client_id },
          //   },
          //   select: {
          //     client_name: true,
          //   },
          // });
          return {
            role: item.name,
            role_id: item.role_id,
            permission,
            // client_id: item.client_id,
            // client_names,
          };
        })
      );
      return res.status(200).json(rolesPermission);
    }
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      success: false,
      message: error.message || "Something went wrong",
    });
  }
};

export const viewSetupPermissions = async (req, res: Response) => {
  try {
    const params = await getFreightSetupParams(req.corporation_id);
    const findPermissions = await findOne("user", params);
    const data =
      findPermissions?.role?.role_permission.map(
        (permission) => permission.permission.module
      ) || [];
    return res.status(200).json(data);
  } catch (error) {
    console.log(error);
    return handleError(res, error);
  }
};

const findOne = async (
  model: string,
  params: any,
  postProcess?: (data: any) => Promise<any>
) => {
  try {
    const data = await prisma[model].findFirst(params);
    const result = postProcess ? await postProcess(data) : data;
    return result;
  } catch (error) {
    throw error;
  }
};
