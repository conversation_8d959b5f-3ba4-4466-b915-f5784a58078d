import Sidebar from "@/components/sidebar/Sidebar";
import { SidebarProvider } from "@/components/ui/sidebar";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  employee_routes,
  usertitle_routes,
  workreport_routes,
} from "@/lib/routePath";
import React from "react";
import UserDetail from "./UserDetail";
import { UserNavBar } from "@/components/userNav/adminNavBar";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import dynamic from "next/dynamic";
import Manage from "./manage";

const Page = async () => {
  // find the user with parent_id = -1
  // user u = find()
  const userData = await getAllData(employee_routes.GETCURRENT_USER);

  const userPermissions =
    userData?.role?.role_permission.map(
      (item: any) => item.permission.module
    ) || [];

  const corporationCookie = await getCookie("corporationtoken");

  const permissions = corporationCookie ? ["allow_all"] : userPermissions;
 console.time("Time taken for: " + workreport_routes.GET_CURRENT_USER_WORKREPORT_STATUS_COUNT);
  const currentUserWorkReportStatusCount = await getAllData(
    workreport_routes.GET_CURRENT_USER_WORKREPORT_STATUS_COUNT
  );
 console.timeEnd("Time taken for: " + workreport_routes.GET_CURRENT_USER_WORKREPORT_STATUS_COUNT);
  let completedWork = 0;
  let resumedWork = 0;
  let totalWork = 0;

  const resumedWorkPercentage =
    totalWork > 0 ? ((resumedWork / totalWork) * 100).toFixed(2) : 0;

  const allEmployee = await getAllData(employee_routes.GETALL_USERS);
  const hrUser = allEmployee.data?.find((user: any) => user.parent_id === -1);
  const userlevel = await getAllData(usertitle_routes.GETALL_USERTITLE);

  return (
    <>
      <Manage
        hrUser={hrUser}
        userData={userData}
        completedWork={completedWork}
        resumedWork={resumedWork}
        resumedWorkPercentage={resumedWorkPercentage}
        userType={currentUserWorkReportStatusCount}
        permissions={permissions}
        allEmployee={allEmployee?.data}
        userlevel={userlevel}
      />
    </>
  );
};

export default Page;
