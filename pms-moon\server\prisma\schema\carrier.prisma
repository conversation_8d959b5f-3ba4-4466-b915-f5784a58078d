model Carrier {
  id                   Int                    @id @default(autoincrement())
  name                 String                 @unique @db.VarChar()
  carrier_code         String?                @unique
  code                 String?                @db.VarChar()
  country              String?                @db.VarChar()
  carrier_2nd_name     String?                @db.VarChar()
  created_at           DateTime               @default(now())
  updated_at           DateTime               @default(now()) @updatedAt
  corporation_id       Int?
  corporation          Corporation?           @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade)
  WorkReport           WorkReport[]
  ClientCarrier        ClientCarrier[]
  DailyPlanningDetails DailyPlanningDetails[]

  TrackSheets TrackSheets[]
}