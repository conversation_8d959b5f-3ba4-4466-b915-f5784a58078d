"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewDailyPlanningDetailsByType = exports.viewDailyPlanningDetailsDetailsManual = exports.viewSpecificDailyPlanningDetails = exports.viewDailyPlanningDetails = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewDailyPlanningDetails = async (req, res) => {
    try {
        // const data = await prisma.dailyPlanningByType.findMany({
        //   include: {
        //     DailyPlanningDetails: {
        //       include: {
        //         carrier: true,
        //       },
        //     },
        //   },
        //   orderBy: { daily_planning_type_id: "desc" },
        // });
        // //  (data, "data");
        // if (data) {
        //   return res.status(200).json(data);
        // }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewDailyPlanningDetails = viewDailyPlanningDetails;
const viewSpecificDailyPlanningDetails = async (req, res) => {
    const { id, type } = req.params;
    //  (id, "id");
    try {
        const data = await prisma.dailyPlanningDetails.findMany({
            where: {
                user_id: req.user_id,
                daily_planning_id: Number(id),
                daily_planning_type: {
                    type: type,
                },
            },
            include: {
                carrier: {
                    select: {
                        name: true,
                        ClientCarrier: {
                            select: {
                                id: true,
                                payment_terms: true,
                            },
                        },
                    },
                },
                daily_planning: true,
            },
        });
        return res.status(200).json(data);
    }
    catch (error) {
        console.log(error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewSpecificDailyPlanningDetails = viewSpecificDailyPlanningDetails;
const viewDailyPlanningDetailsDetailsManual = async (req, res) => {
    try {
        const { id, type } = req.params;
        const data = await prisma.dailyPlanningByType.findMany({
            where: {
                daily_planning_id: Number(id),
                type: type.trim(),
            },
            include: {
                DailyPlanningDetails: { include: { carrier: true } },
                daily_planning: {
                    select: {
                        client_id: true,
                        daily_planning_date: true,
                    },
                },
            },
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        console.log(error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewDailyPlanningDetailsDetailsManual = viewDailyPlanningDetailsDetailsManual;
const viewDailyPlanningDetailsByType = async (req, res) => {
    try {
        const { id, type } = req.params;
        const clientData = await prisma.dailyPlanning.findUnique({
            where: {
                id: Number(id),
            },
            select: {
                client_id: true,
            },
        });
        const clientId = clientData?.client_id;
        const data = await prisma.dailyPlanningByType.findMany({
            where: {
                daily_planning_id: Number(id),
                type: type.trim(),
            },
            include: {
                DailyPlanningDetails: {
                    include: {
                        carrier: {
                            select: {
                                name: true,
                                ClientCarrier: {
                                    where: {
                                        client_id: Number(clientId),
                                    },
                                    select: {
                                        id: true,
                                        payment_terms: true,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        });
        let carrierAgeCounts = (0, helpers_1.buildCarrierAgeCounts)(data);
        Object.keys(carrierAgeCounts).forEach((clientCarrierId) => {
            const entry = carrierAgeCounts[clientCarrierId];
            const paymentTerms = entry.paymentTerms;
            // Use the helper function to assign priorities
            (0, helpers_1.assignPriorityToBuckets)(entry, paymentTerms);
        });
        return res.status(200).json({ data, carrierAgeCounts });
    }
    catch (error) {
        console.log(error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewDailyPlanningDetailsByType = viewDailyPlanningDetailsByType;
//# sourceMappingURL=view.js.map