import { createItem } from "../../../utils/operation";

export const createWorktype = async (req, res) => {
  const fields = {
    work_type: req.body.work_type,
    category_id: Number(req.body.category),
    is_work_carrier_specific: req.body.is_work_carrier_specific,
    does_it_require_planning_number: req.body.does_it_require_planning_number,
    is_backlog_regular_required: req.body.is_backlog_regular_required,
  };
  await createItem({
    model: "workType",
    fieldName: "id",
    fields: fields,
    res: res as Response,
    req: req,
    successMessage: "WorkType has been created",
  });
};
