{"version": 3, "file": "checkPermission.js", "sourceRoot": "", "sources": ["../../src/middleware/checkPermission.ts"], "names": [], "mappings": ";;;AAEO,MAAM,yBAAyB,GAAG,CACvC,MAAc,EACd,MAAc,EACE,EAAE;IAClB,OAAO,KAAK,EAAE,GAAQ,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC1E,2DAA2D;QAC3D,IAAI,CAAC;YACH,IAAI,GAAG,CAAC,cAAc,EAAE,CAAC;gBACvB,OAAO,IAAI,EAAE,CAAC;YAChB,CAAC;YACD,MAAM,EAAE,GAAG,GAAG,CAAC,cAAc,CAAC;YAE9B,IAAI,CAAC,EAAE,EAAE,CAAC;gBACR,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;gBAClE,OAAO,CAAC,oCAAoC;YAC9C,CAAC;YACD,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gBACxC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBACjB,OAAO,EAAE;oBACP,IAAI,EAAE;wBACJ,OAAO,EAAE;4BACP,eAAe,EAAE;gCACf,OAAO,EAAE;oCACP,UAAU,EAAE,IAAI;iCACjB;6BACF;yBACF;qBACF;iBACF;aACF,CAAC,CAAC;YACH,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACxB,GAAG;qBACA,MAAM,CAAC,GAAG,CAAC;qBACX,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,2BAA2B,EAAE,CAAC,CAAC;gBAClE,OAAO;YACT,CAAC;YAED,IAAI,aAAa,GAAG,KAAK,CAAC;YAE1B,KAAK,IAAI,cAAc,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrD,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAC;gBAE7C,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;oBACjE,aAAa,GAAG,IAAI,CAAC;oBACrB,MAAM;gBACR,CAAC;YACH,CAAC;YAED,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,iCAAiC,MAAM,OAAO,MAAM,GAAG;iBACjE,CAAC,CAAC;gBACH,OAAO,CAAC,oCAAoC;YAC9C,CAAC;YACD,sEAAsE;YACtE,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YAE5D,gEAAgE;YAChE,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACrB,GAAG;qBACA,MAAM,CAAC,GAAG,CAAC;qBACX,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,uBAAuB,EAAE,CAAC,CAAC;gBAC9D,OAAO,CAAC,oCAAoC;YAC9C,CAAC;QACH,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AArEW,QAAA,yBAAyB,6BAqEpC"}