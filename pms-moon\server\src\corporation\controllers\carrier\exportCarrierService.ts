import { handleError } from "../../../utils/helpers";
import xlsx from "xlsx";

export const exportCarrierService = async (req, res) => {
  try {
    const { CarrierName, VAPId, CarrierName2 } = req.query;

    const searchConditions: any[] = [];

    if (CarrierName) {
      searchConditions.push({
        name: {
          contains: CarrierName,
          mode: "insensitive",
        },
      });
    }

    if (VAPId) {
      searchConditions.push({
        carrier_code: {
          contains: VAPId,
          mode: "insensitive",
        },
      });
    }

    if (CarrierName2) {
      searchConditions.push({
        carrier_2nd_name: {
          contains: CarrierName2,
          mode: "insensitive",
        },
      });
    }

    const whereClause = {
      AND: [],
    };

    if (searchConditions.length > 0) {
      whereClause.AND.push({
        AND: searchConditions,
      });
    }

    const data = await prisma.carrier.findMany({
      where: whereClause,
      include: {
        WorkReport: true,
      },
      orderBy: {
        id: "desc",
      },
    });

    const datalength = await prisma.carrier.count({
      where: whereClause,
    });

    // Define the headers you want in the Excel sheet
    const headers = ["VAP ID", "Carrier Name", "Carrier Name - 2"];

    // Format the data, ensuring it matches the headers
    const formattedData = data.map((item) => [
      item.carrier_code || "N/A",
      item.name || "N/A",
      item.carrier_2nd_name || "N/A",
    ]);

    // Create the worksheet with headers included
    const ws = xlsx.utils.aoa_to_sheet([headers, ...formattedData]);
    const wb = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(wb, ws, "Carrier");
    const excelBuffer = xlsx.write(wb, { bookType: "xlsx", type: "buffer" });

    if (data.length > 0) {
      res.setHeader("Content-Disposition", "attachment; filename=Carrier.xlsx");
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      return res.send(excelBuffer);
    }

    return res.status(200).json({ data: [], datalength });
  } catch (error) {
    return handleError(res, error);
  }
};
