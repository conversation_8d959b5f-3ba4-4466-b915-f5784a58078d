"use client";

import * as React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend,
} from "recharts";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

// Dummy data generator
const generateDummyData = (timeFrame: string) => {
  return {
    statusData: [
      { name: "Created", value: 40 },
      { name: "Completed", value: 20 },
    ],
    workloadData: [
      { name: "High", value: 25 },
      { name: "Low", value: 40 },
    ],
  };
};

const COLORS = [
  "#BCBCBD",
  "#7A7A7C",

];

export function DashBoardPieChart() {
  const [dataView, setDataView] = React.useState("status");
  const [timeFrame, setTimeFrame] = React.useState("month");
  const [chartData, setChartData] = React.useState(
    generateDummyData(timeFrame)
  );

  React.useEffect(() => {
    setChartData(generateDummyData(timeFrame));
  }, [timeFrame]);

  const data =
    dataView === "status" ? chartData.statusData : chartData.workloadData;
  const total = data.reduce((sum, entry) => sum + entry.value, 0);

  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle>Daily Planning Work Chart</CardTitle>
        <CardDescription>
          View work status or workload by different time periods
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex space-x-4 mb-4">
          <Select value={dataView} onValueChange={setDataView}>
            <SelectTrigger className="w-[180px]">
              <SelectValue>
                {dataView === "status" ? "Work Created Status" : "Work Load"}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="status">Work Created Status</SelectItem>
              <SelectItem value="workload">Work Load</SelectItem>
            </SelectContent>
          </Select>
          <Select value={timeFrame} onValueChange={setTimeFrame}>
            <SelectTrigger className="w-[180px]">
              <SelectValue>
                {timeFrame.charAt(0).toUpperCase() + timeFrame.slice(1)}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="year">Year</SelectItem>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="week">Week</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <ResponsiveContainer width="100%" height={400}>
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={150}
              fill="#8884d8"
              dataKey="value"
              label={({ name, percent }) =>
                `${name} ${(percent * 100).toFixed(0)}%`
              }
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
        <div className="text-center mt-4">
          <p className="text-2xl font-bold">{total}</p>
          <p className="text-sm text-muted-foreground">
            Total {dataView === "status" ? "Tasks" : "Workload"}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
