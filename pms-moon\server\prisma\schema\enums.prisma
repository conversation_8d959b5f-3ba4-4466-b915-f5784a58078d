enum UserType {
  HR
  TL
  CSA
  MEMBER
}

enum WorkStatus {
  STARTED
  PAUSED
  RESUMED
  FINISHED
}

enum TaskType {
  BACKLOG
  REGULAR
}

enum DailyPlanningType {
  INVOICE_ENTRY_STATUS
  PF_STATUS
  REVIEW_STATUS
  BATCH_ERROR_STATUS
  TWO_TEN_ERROR
  TWO_TEN_SUCCESS
  TWO_TEN_M_F
  TWO_TEN_HOLD
  TWO_TEN_IMPORT_ADDITIONAL
  TWO_TEN_MANUAL_MATCH
  HOLD_STATUS
  STATEMENT_TABLE
  CORRECT
  ENTRY
}

enum Bucket {
  ZERO_TO_SEVEN
  EIGHT_TO_FIFTEEN
  SIXTEEN_TO_THIRTY
  THIRTY_ONE_TO_SIXTY
  SIXTY_ONE_TO_NINETY
  NINETY_ONE_TO_HUNDRED_AND_TWENTY
  HUNDRED_AND_TWENTY_PLUS
}

enum PriorityStatus {
  HIGH
  MEDIUM
  LOW
}

enum FieldType {
  TEXT
  NUMBER
  DATE
  AUTO
}

enum AutoOption {
  DATE
  USERNAME
}

enum Switchtype {
  INT
  EXT
}