import { handleError } from "../../../utils/helpers";

export const viewLegrandMapping = async (req, res) => {
  try {

    const data = await prisma.legrandMapping.findMany({
      orderBy: { id: "desc" },
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(404).json({ message: "Rule not found" });
  } catch (error) {
    return handleError(res, error);
  }
};

export const viewLegrandMappingById = async (req, res) => {
  try {
    const { id } = req.params;
    const data = await prisma.legrandMapping.findUnique({
      where: { id: id },
    });
    if (data) {
      return res.status(200).json(data);
    }
    return res.status(404).json({ message: "Rule not found" });
  } catch (error) {
    return handleError(res, error);
  }
};

