model ClientCustomFieldArrangement {
    id              String @id @default(uuid())
    client_id       Int
    custom_field_id String
    order           Int

    Client      Client      @relation(fields: [client_id], references: [id], onDelete: Cascade)
    CustomField CustomField @relation(fields: [custom_field_id], references: [id], onDelete: Cascade)

    @@map("client_custom_field_arrangements")
}
