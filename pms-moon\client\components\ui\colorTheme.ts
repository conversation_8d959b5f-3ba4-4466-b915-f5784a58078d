// app/(pms)/pms/colorTheme.ts

export const colorThemes: Record<string, string> = {
  "/pms/manage_employee": "bg-blue-100 text-blue-800",
  "/pms/manage_client": "bg-green-100 text-green-800",
  "/pms/manage_carrier": "bg-yellow-100 text-yellow-800",
  "/pms/manage_category": "bg-pink-100 text-pink-800",
  "/pms/manage_branch": "bg-purple-100 text-purple-800",
  "/pms/manage_associate": "bg-teal-100 text-teal-800",
  "/pms/manage-roles": "bg-red-100 text-red-800",
  "/pms/customize_report": "bg-indigo-100 text-indigo-800",
  "/pms/manage_work_report": "bg-orange-100 text-orange-800",
  "/pms/manage_work_type": "bg-cyan-100 text-cyan-800",
  "/pms/addupdate_custom_fields": "bg-zinc-100 text-zinc-800", 
  "/pms/arrange_custom_fields": "bg-amber-100 text-amber-800",
};
