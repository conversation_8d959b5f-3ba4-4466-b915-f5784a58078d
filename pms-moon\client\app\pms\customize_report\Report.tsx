"use client";

import React, { useState } from "react";
import { Form, FormMessage } from "@/components/ui/form";
import FormInput from "@/app/_component/FormInput";
import SelectComp from "@/app/_component/SelectComp";
import { SelectItem } from "@/components/ui/select";
import useDynamicForm from "@/lib/useDynamicForm";
import { z } from "zod";
import { customizeReport } from "@/lib/routePath";
import { Button } from "@/components/ui/button";
import { FancyMultiSelects } from "@/app/_component/FancyMultiSelects";
import { FileDown, Loader2, Smartphone, Filter, AlertCircle, CheckCircle } from "lucide-react";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { FancyMultiSelect } from "@/app/_component/FancyMultiSelect";

const Report = ({ categories, users, permissions, clients }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingScreenshot, setIsGeneratingScreenshot] = useState(false);
  const [buttonsEnabled, setButtonsEnabled] = useState(false);
  const [isGenerateClicked, setIsGenerateClicked] = useState(false);
  const [dataLength, setDataLength] = useState(0);
  const [selectedUsers, setSelectedUsers] = useState<
    { value: string; label: string }[]
  >(
    !Array.isArray(users)
      ? [{ value: users.username, label: users.username }]
      : []
  );
  const [selectedCategories, setSelectedCategories] = useState<any>([]);
  const [selectedClients, setSelectedClients] = useState<any>([]);
  const [selectAllUsers, setSelectAllUsers] = useState(false);
  const [selectAllCategories, setSelectAllCategories] = useState(false);
  const [selectAllClients, setSelectAllClients] = useState(false);
  const [selectedAllUsers, setSelectedAllUsers] = useState<any>([]);
  const [selectedAllCategories, setSelectedAllCategories] = useState<any>([]);
  const [selectedAllClients, setSelectedAllClients] = useState<any>([]);

function formatDate(date) {
  const pad = (n) => n.toString().padStart(2, '0');
  const day = pad(date.getDate());
  const month = pad(date.getMonth() + 1); // Months are 0-indexed
  const year = date.getFullYear();

  let hours = date.getHours();
  const minutes = pad(date.getMinutes());
  const ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12 || 12; // Convert 0 -> 12 and 13..23 -> 1..11
  const formattedHours = pad(hours);

  return `${day}${month}${year}-${formattedHours}${minutes}${ampm}`;
}

  const exportReportSchema = z
    .object({
      username: z.string().refine((val) => val !== "" || selectAllUsers, {
        message: "Username is required",
      }),
      category: z.string().refine((val) => val !== "" || selectAllCategories, {
        message: "Category is required",
      }),
      client: z.string().optional(),
      fromDate: z.string().optional(),
      toDate: z.string().optional(),
    })
    .refine(
      (data) => {
        // If fromDate is provided, toDate must also be provided
        if (data.fromDate && !data.toDate) return false;
        return true;
      },
      {
        path: ["toDate"],
        message: "To Date is required",
      }
    )
    .refine(
      (data) => {
        // If toDate is provided, fromDate must also be provided
        if (data.toDate && !data.fromDate) return false;
        return true;
      },
      {
        path: ["fromDate"],
        message: "From Date is required",
      }
    )
    .refine(
      (data) => {
        // Ensure toDate is not before fromDate
        if (data.fromDate && data.toDate) {
          return new Date(data.toDate) >= new Date(data.fromDate);
        }
        return true;
      },
      {
        path: ["toDate"],
        message: "To Date cannot be earlier than From Date",
      }
    );

  // Transform categories array into format required by FancyMultiSelect
  const categoryOptions =
    categories?.map((category) => ({
      value: category.category_name,
      label: category.category_name,
    })) || [];

  const clientOptions =
    clients?.map((client) => ({
      value: client.client_name,
      label: client.client_name,
    })) || [];

  // Transform users array into format required by FancyMultiSelect
  const userOptions = Array.isArray(users)
    ? users.map((user) => ({ value: user.username, label: user.username }))
    : [{ value: users.username, label: users.username }];

  const handleSelectAllUsers = (checked: boolean) => {
    setSelectAllUsers(checked);
    if (checked) {
      const allUsers = userOptions.map((user) => ({
        value: user.value,
        label: user.label,
      }));
      setSelectedAllUsers([]);
      setSelectedUsers([]);
      form.setValue(
        "username",
        ""
        // userOptions.map((user) => user.value).join(",")
      );
    }
  };

  const handleSelectAllCategories = (checked: boolean) => {
    setSelectAllCategories(checked);

    if (checked) {
      const allCategories = categoryOptions.map((category) => ({
        value: category.value,
        label: category.label,
      }));

      setSelectedAllCategories([]);
      setSelectedCategories([]);
      form.setValue(
        "category",
        ""
        // categoryOptions.map((category) => category.value).join(",")
      );
    }
  };

  const handleSelectAllClients = (checked: boolean) => {
    setSelectAllClients(checked);
    if (checked) {
      const allClients = clientOptions.map((client) => ({
        value: client.value,
        label: client.label,
      }));
      setSelectedAllClients(allClients);
      setSelectedClients([]);
      form.setValue(
        "client",
        clientOptions.map((client) => client.value).join(",")
      );
    }
  };

  const { form } = useDynamicForm(exportReportSchema, {
    username: !Array.isArray(users) ? users.username : "",
    category: "",
    client: "",
    fromDate: "",
    toDate: "",
  });

  // Update form when selected categories change
  React.useMemo(() => {
    if (selectedUsers.length > 0) {
      const usernames = selectedUsers.map((user) => user.value).join(",");
      form.setValue("username", usernames);
    } else if (!selectAllUsers) {
      form.setValue("username", "");
    }

    if (selectedCategories.length > 0) {
      const categoryNames = selectedCategories
        .map((cat) => cat.value)
        .join(",");
      form.setValue("category", categoryNames);
    } else if (!selectAllCategories) {
      form.setValue("category", "");
    }

    if (selectedClients.length > 0) {
      const clientNames = selectedClients
        .map((client) => client.value)
        .join(",");
      form.setValue("client", clientNames);
    } else if (!selectAllClients) {
      form.setValue("client", "");
    }
  }, [
    selectedCategories,
    selectedClients,
    selectedUsers,
    selectAllUsers,
    selectAllCategories,
    selectAllClients,
    form,
  ]);

  const handleExportExcel = async (values: any) => {
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams();

      if (values.username) {
        if (values.username === "all" && Array.isArray(users)) {
          const allUsernames = users.map((user) => user.username).join(",");
          queryParams.append("Username", allUsernames);
        } else {
          queryParams.append("Username", values.username);
        }
      }
      if (values.category) queryParams.append("Category", values.category);
      if (values.client) queryParams.append("Client", values.client);
      if (values.fromDate) queryParams.append("fDate", values.fromDate);
      if (values.toDate) queryParams.append("tDate", values.toDate);

      const queryString = queryParams.toString();
      const url = `${customizeReport.EXPORT_CUSTOMIZE_REPORT}?${queryString}`;

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      });

      if (!response.ok) throw new Error("Export failed");

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;

       // ✅ Extract filename from Content-Disposition
      const contentDisposition = response.headers.get("Content-Disposition");
      const filenameMatch = contentDisposition?.match(/filename="?([^"]+)"?/);

      // ✅ Use backend filename if available, fallback otherwise
       const fallbackFilename = `Work_Report_${formatDate(new Date())}.xlsx`;
       link.download = filenameMatch?.[1] ?? fallbackFilename;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error("Export error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateScreenshot = async (values: any) => {
    setIsGeneratingScreenshot(true);
    try {
      const queryParams = new URLSearchParams();

      if (values.username) {
        if (values.username === "all" && Array.isArray(users)) {
          const allUsernames = users.map((user) => user.username).join(",");
          queryParams.append("Username", allUsernames);
        } else {
          queryParams.append("Username", values.username);
        }
      }
      if (values.category) queryParams.append("Category", values.category);
      if (values.client) queryParams.append("Client", values.client);
      if (values.fromDate) queryParams.append("fDate", values.fromDate);
      if (values.toDate) queryParams.append("tDate", values.toDate);
      queryParams.append("screenshot", "true");

      // First check data length
      const checkParams = new URLSearchParams(queryParams);
      checkParams.append("checkDataLength", "true");

      if (Array.isArray(users)) {
        queryParams.append("isCorporation", "true");
        checkParams.append("isCorporation", "true");
      }

      const checkUrl = `${customizeReport.EXPORT_CUSTOMIZE_REPORT}?${checkParams.toString()}`;

      // Check data length first
      const checkResponse = await fetch(checkUrl, {
        method: "GET",
        headers: {
          Accept: "application/json",
        },
        credentials: "include",
      });

      const checkData = await checkResponse.json();

      if (!checkResponse.ok) {
        throw new Error(checkData.message || "Failed to check data length");
      }

      const dataLength = checkData.dataLength || 0;
      setDataLength(dataLength);

      // If data length is greater than 100, show a warning but continue
      if (dataLength > 100) {
        toast.warning(
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-amber-500" />
            <span>Data will be limited to 100 entries in the screenshot.</span>
          </div>,
          {
            duration: 5000,
            position: "top-center",
            style: {
              background: "#fff",
              border: "1px solid #f59e0b",
              borderRadius: "8px",
              padding: "16px",
            },
          }
        );
      }

      // If no data is found
      if (dataLength === 0) {
        toast.error(
          <div className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-red-500" />
            <span>No data available for the selected filters.</span>
          </div>,
          {
            duration: 5000,
            position: "top-center",
          }
        );
        return;
      }

      // If data length is within limits, proceed with screenshot generation
      const queryString = queryParams.toString();
      const url = `${customizeReport.EXPORT_CUSTOMIZE_REPORT}?${queryString}`;

      const response = await fetch(url, {
        method: "GET",
        headers: {
          Accept: "image/png",
        },
        credentials: "include",
      });

      if (!response.ok) {
        // Try to parse error message if it's JSON
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Screenshot generation failed");
        }
        throw new Error("Screenshot generation failed");
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `Work_Report_Summary_${formatDate(new Date())}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);

      toast.success(
        <div className="flex items-center gap-2">
          <span>Screenshot downloaded successfully.</span>
        </div>,
        {
          duration: 3000,
          position: "top-center",
        }
      );
    } catch (error) {
      console.error("Screenshot error:", error);
      toast.error(
        <div className="flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-red-500" />
          <span>{error.message || "Failed to generate screenshot. Please try again."}</span>
        </div>,
        {
          duration: 5000,
          position: "top-center",
        }
      );
    } finally {
      setIsGeneratingScreenshot(false);
    }
  };

  return (
    <div className="min-h-screen bg-background px-4 py-6 lg:px-8">
      <Card className="mx-auto max-w-5xl">
        <CardHeader className="space-y-1 bg-primary/5 rounded-t-xl">
          <CardTitle className="text-2xl font-bold tracking-tight">
            Custom Report
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            Generate custom reports based on your preferences
          </CardDescription>
        </CardHeader>

        <CardContent className="p-6">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleExportExcel)}
              className="space-y-6 animate-in fade-in-50"
            >
              {/* Username Section */}
              <div className="space-y-4">
                <div className="flex-1">
                  <FancyMultiSelects
                    label="Username"
                    frameworks={userOptions}
                    selected={selectAllUsers ? selectedAllUsers : selectedUsers}
                    setSelected={setSelectedUsers}
                    isAllSelected={selectAllUsers}
                    isCorporation={Array.isArray(users)}
                    adminPlaceholder="All Users"
                    disabled={!Array.isArray(users)}
                    showSelectAll={Array.isArray(users)}
                    onSelectAll={handleSelectAllUsers}
                    placeholder="Select Username..."
                    errorMessage={form.formState.errors?.username?.message}
                  />
                </div>
              </div>

              {/* Date Range */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormInput
                  form={form}
                  label="From Date"
                  name="fromDate"
                  type="date"
                  placeholder="Select from date"
                />
                <FormInput
                  form={form}
                  label="To Date"
                  name="toDate"
                  type="date"
                  placeholder="Select to date"
                />
              </div>

              {/* Client and Category */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FancyMultiSelects
                  label="Client"
                  frameworks={clientOptions}
                  selected={
                    selectAllClients ? selectedAllClients : selectedClients
                  }
                  setSelected={setSelectedClients}
                  isAllSelected={selectAllClients}
                  isCorporation={true}
                  adminPlaceholder="All Clients"
                  showSelectAll={true}
                  onSelectAll={handleSelectAllClients}
                  placeholder="Select Client..."
                />

                <FancyMultiSelects
                  label="Category"
                  frameworks={categoryOptions}
                  selected={
                    selectAllCategories
                      ? selectedAllCategories
                      : selectedCategories
                  }
                  setSelected={setSelectedCategories}
                  isAllSelected={selectAllCategories}
                  isCorporation={true}
                  adminPlaceholder="All Categories"
                  showSelectAll={true}
                  onSelectAll={handleSelectAllCategories}
                  placeholder="Select Category..."
                  errorMessage={form.formState.errors?.category?.message}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-4">
                <div className="flex-1 flex flex-col">
                  <Button
                    type="button"
                    variant="default"
                    className={`gap-2  ${
                      isGenerateClicked
                        ? "bg-green-500 hover:bg-green-600  transition-width duration-300 p-5"
                        : "bg-blue-500 hover:bg-blue-600 p-5 "
                    } text-white font-medium shadow-md rounded-md transition-all duration-200`}
                    onClick={() => {
                      if (isGenerateClicked) {
                        // If already in "Filters Applied" state, toggle back to "Generate" state
                        setIsGenerateClicked(false);
                        setButtonsEnabled(false);
                      } else {
                        // If in "Generate" state, move to "Filters Applied" state
                        setButtonsEnabled(true);
                        setIsGenerateClicked(true);
                      }
                    }}
                  >
                    {isGenerateClicked ? (
                      <>
                        <CheckCircle className="h-5 w-5 " />
                        Generate
                      </>
                    ) : (
                      <>
                        <Filter className="h-5 w-5" />
                        Generate
                      </>
                    )}
                  </Button>
                  {dataLength > 0 && (
                    <div className={`text-xs mt-1 text-center ${dataLength > 100 ? 'text-amber-500' : 'text-green-600'}`} style={{ minHeight: '22px' }}>
                      Entries: {dataLength} {dataLength > 100 ? '(Only 100 will be shown in screenshot)' : ''}
                    </div>
                  )}
                </div>
                <Button
                  type="submit"
                  className={`flex-1 gap-2 p-5 bg-slate-800 hover:bg-slate-900 text-white font-medium rounded-md shadow-md transition-all duration-200 ${
                    !buttonsEnabled || isLoading
                      ? "opacity-60 cursor-not-allowed"
                      : ""
                  }`}
                  disabled={!buttonsEnabled || isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-5 w-5 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <FileDown className="h-5 w-5" />
                      Download Report
                    </>
                  )}
                </Button>
                <div className="flex-1 flex flex-col">
                  <Button
                    type="button"
                    variant="secondary"
                    className={`gap-2 p-5 bg-gray-100 hover:bg-gray-200 text-slate-800 font-medium rounded-md shadow-md transition-all duration-200 ${
                      !buttonsEnabled || isLoading || isGeneratingScreenshot
                        ? "opacity-60 cursor-not-allowed"
                        : ""
                    }`}
                    disabled={!buttonsEnabled || isLoading || isGeneratingScreenshot}
                    onClick={form.handleSubmit(handleGenerateScreenshot)}
                  >
                    {isGeneratingScreenshot ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Smartphone className="h-5 w-5" />
                        Download Screenshot
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default Report;
