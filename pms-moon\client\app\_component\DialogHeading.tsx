import { DialogDescription, DialogTitle } from "@/components/ui/dialog";

type DialogHeaderProps = {
  title: string;
  description: string | React.ReactNode;
};

function DialogHeading({ title, description }: DialogHeaderProps) {
  return (
    <>
      <DialogTitle className="text-3xl text-primary dark:text-primary capitalize " >
        {title}
      </DialogTitle>
      <DialogDescription className=" text-base tracking-wider text-gray-800/70 font-medium dark:text-gray-400">
        {description}
      </DialogDescription>
    </>
  );
}

export default DialogHeading;
