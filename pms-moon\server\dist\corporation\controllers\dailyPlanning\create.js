"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDailyPlanning = void 0;
const createDailyPlanning = async (req, res) => {
    const userId = req.user_id;
    const fields = {
        daily_planning_date: new Date(req.body.daily_planning_date),
        client_id: Number(req.body.client_id),
        user_id: Number(userId),
    };
    try {
        const existingDailyPlanning = await prisma.dailyPlanning.findFirst({
            where: {
                client_id: fields.client_id,
                daily_planning_date: fields.daily_planning_date,
            },
        });
        if (existingDailyPlanning) {
            return res.status(400).json({
                message: "Daily planning template already exists",
            });
        }
        const newDailyPlanning = await prisma.dailyPlanning.create({
            data: fields,
        });
        const previousPlanning = await prisma.dailyPlanning.findFirst({
            where: {
                client_id: fields.client_id,
                daily_planning_date: {
                    lt: fields.daily_planning_date,
                },
            },
            orderBy: {
                daily_planning_date: 'desc',
            },
            include: {
                DailyPlanningByType: true,
            },
        });
        let copiedDetailsCount = 0;
        if (previousPlanning) {
            const statementType = previousPlanning.DailyPlanningByType.find((type) => type.type === "STATEMENT_TABLE");
            if (statementType) {
                const unsentDetails = await prisma.dailyPlanningDetails.findMany({
                    where: {
                        daily_planning_id: previousPlanning.id,
                        daily_planning_type_id: statementType.id,
                        send_date: null,
                    },
                });
                const copiedIds = [];
                const newStatementType = await prisma.dailyPlanningByType.create({
                    data: {
                        daily_planning_id: newDailyPlanning.id,
                        type: "STATEMENT_TABLE",
                        created_at: new Date(),
                        updated_at: new Date(),
                    },
                });
                for (const detail of unsentDetails) {
                    copiedIds.push(detail.id);
                    await prisma.dailyPlanningDetails.create({
                        data: {
                            daily_planning_id: newDailyPlanning.id,
                            daily_planning_type_id: newStatementType.id,
                            carrier_id: detail.carrier_id,
                            invoice_entry_total: detail.invoice_entry_total,
                            receive_by: detail.receive_by,
                            receive_date: detail.receive_date,
                            reconcile_by: detail.reconcile_by,
                            reconcile_date: detail.reconcile_date,
                            review_by: detail.review_by,
                            review_date: detail.review_date,
                            send_by: detail.send_by,
                            send_date: detail.send_date,
                            currency: detail.currency,
                            shipping_type: detail.shipping_type,
                            division: detail.division,
                            notes: detail.notes,
                            ute: detail.ute,
                            old: detail.old,
                            new: detail.new,
                            no_invoices: detail.no_invoices,
                            amount_of_invoice: detail.amount_of_invoice,
                            created_at: new Date(),
                            updated_at: new Date(),
                            source: "brought_forward",
                        },
                    });
                    copiedDetailsCount++;
                }
                const allUnsentIds = unsentDetails.map((d) => d.id).sort().join(',');
                const copiedIdString = copiedIds.sort().join(',');
                if (copiedIdString === allUnsentIds && copiedIds.length > 0) {
                    await prisma.dailyPlanningDetails.updateMany({
                        where: {
                            id: { in: copiedIds },
                        },
                        data: {
                            source: "carried_forward",
                        },
                    });
                }
            }
        }
        const newPlanningWithDetails = await prisma.dailyPlanning.findUnique({
            where: { id: newDailyPlanning.id },
            include: {
                DailyPlanningByType: true,
                DailyPlanningDetails: true,
            },
        });
        return res.status(201).json({
            success: true,
            message: "Daily planning has been created",
            planning: newPlanningWithDetails,
            moved_previous_unsent_records: copiedDetailsCount,
        });
    }
    catch (error) {
        console.error("Error creating daily planning:", error);
        return res.status(500).json({
            error: "An error occurred while creating the daily planning",
        });
    }
};
exports.createDailyPlanning = createDailyPlanning;
//# sourceMappingURL=create.js.map