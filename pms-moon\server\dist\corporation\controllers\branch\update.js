"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateBranch = void 0;
const operation_1 = require("../../../utils/operation");
const updateBranch = async (req, res) => {
    const id = req.params.id;
    const { corporation_id } = req;
    const fields = {
        branch_name: req.body.name,
        corporation_id: Number(corporation_id),
    };
    await (0, operation_1.updateItem)({
        model: "branch",
        fieldName: "id",
        fields: fields,
        id: Number(id),
        res,
        req,
        successMessage: "branch has been updated",
    });
};
exports.updateBranch = updateBranch;
//# sourceMappingURL=update.js.map