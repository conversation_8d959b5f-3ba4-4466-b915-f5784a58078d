"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteClientCarrier = void 0;
const operation_1 = require("../../../utils/operation");
const deleteClientCarrier = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "clientCarrier",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "Client caarier setup has been deleted",
    });
};
exports.deleteClientCarrier = deleteClientCarrier;
//# sourceMappingURL=delete.js.map