import { X } from "lucide-react";

type ClosedDialogProps = {
  form?: any;
  setOpen: (open: boolean) => void;
  handleClick?: () => void;
};

function CloseDialog({ form, setOpen,handleClick }: ClosedDialogProps) {
  const onClose = () => {
    setOpen(false);
    form && form.reset();
    handleClick && handleClick();
  };
  return (
    <div
      className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 cursor-pointer"
      onClick={onClose}
    >
      <X className="h-4 w-4" />
    </div>
  );
}

export default CloseDialog;
