"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/ManageTrackSheet.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/ManageTrackSheet.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _createTrackSheet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./createTrackSheet */ \"(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx\");\n/* harmony import */ var _TrackSheetContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TrackSheetContext */ \"(app-pages-browser)/./app/user/trackSheets/TrackSheetContext.tsx\");\n/* harmony import */ var _components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/sidebar/Sidebar */ \"(app-pages-browser)/./components/sidebar/Sidebar.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./components/ui/sidebar.tsx\");\n/* harmony import */ var _app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/_component/BreadCrumbs */ \"(app-pages-browser)/./app/_component/BreadCrumbs.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst ManageWorkSheet = (param)=>{\n    let { permissions, client, carrier, associate, userData, actions } = param;\n    _s();\n    const [filterdata, setFilterData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [deleteData, setDeletedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeView, setActiveView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TrackSheetContext__WEBPACK_IMPORTED_MODULE_3__.TrackSheetContext.Provider, {\n        value: {\n            filterdata,\n            setFilterData,\n            deleteData,\n            setDeletedData\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex w-full min-h-screen\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar_Sidebar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        permissions,\n                        profile: userData\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 w-full pl-3 overflow-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_BreadCrumbs__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    breadcrumblist: [\n                                        {\n                                            link: \"/user/trackSheets\",\n                                            name: \"TrackSheet\"\n                                        }\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_createTrackSheet__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                client: client,\n                                carrier: carrier,\n                                associate: associate,\n                                userData: userData,\n                                activeView: activeView,\n                                setActiveView: setActiveView,\n                                permissions: actions\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\ManageTrackSheet.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ManageWorkSheet, \"FIRuuSuPd/0VoQiuhLcKikgQ+zE=\");\n_c = ManageWorkSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ManageWorkSheet);\nvar _c;\n$RefreshReg$(_c, \"ManageWorkSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/ManageTrackSheet.tsx\n"));

/***/ })

});