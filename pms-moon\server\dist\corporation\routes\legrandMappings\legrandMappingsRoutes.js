"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/legrandMappings/create");
const view_1 = require("../../controllers/legrandMappings/view");
const update_1 = require("../../controllers/legrandMappings/update");
const delete_1 = require("../../controllers/legrandMappings/delete");
const router = (0, express_1.Router)();
router.post("/", 
// authenticate,
create_1.createLegrandMapping);
router.get("/", 
// authenticate,
view_1.viewLegrandMapping);
router.get("/:id", 
// authenticate,
view_1.viewLegrandMappingById);
router.put("/:id", 
// authenticate,
update_1.updateLegrandMapping);
router.delete("/:id", 
// authenticate,
delete_1.deleteLegrandMapping);
exports.default = router;
//# sourceMappingURL=legrandMappingsRoutes.js.map