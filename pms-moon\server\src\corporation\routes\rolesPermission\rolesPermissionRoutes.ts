import { Router } from "express";
import { authenticate } from "../../../middleware/authentication";
import { createRolePermission } from "../../controllers/rolespermission/create";
import {
  getPermissions,
  viewRoles,
  viewRolesPermissions,
} from "../../controllers/rolespermission/view";
import { updateRolesPermission } from "../../controllers/rolespermission/update";
import { deleteRolesPermission } from "../../controllers/rolespermission/delete";
import { checkPermissionMiddleware } from "../../../middleware/checkPermission";

const router = Router();

router.post(
  "/add-roles",
  authenticate,
  checkPermissionMiddleware("ROLE MANAGEMENT", "create-role"),
  createRolePermission
);
router.get("/get-all-roles", viewRoles);
router.get("/get-all-permissions", getPermissions);
router.get("/get-all-role-permissions", viewRolesPermissions);
router.put("/update-roles/:id", authenticate, updateRolesPermission);
router.delete(
  "/delete-roles/:id",
  authenticate,
  checkPermissionMiddleware("ROLE MANAGEMENT", "delete-role"),
  deleteRolesPermission
);
// router.get("/get-all-role-permissions",      viewRolesPermissions);

export default router;
