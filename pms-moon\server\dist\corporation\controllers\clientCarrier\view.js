"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewClientCarrierById = exports.viewClientCarrier = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewClientCarrier = async (req, res) => {
    try {
        const data = await prisma.clientCarrier.findMany({
            include: {
                client: true,
                carrier: true,
            },
            orderBy: {
                id: 'desc'
            }
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewClientCarrier = viewClientCarrier;
const viewClientCarrierById = async (req, res) => {
    const { id } = req.params;
    try {
        const data = await prisma.clientCarrier.findMany({
            where: {
                client_id: Number(id),
            },
            include: {
                client: true,
                carrier: true,
            },
            orderBy: {
                id: 'desc'
            }
        });
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewClientCarrierById = viewClientCarrierById;
//# sourceMappingURL=view.js.map