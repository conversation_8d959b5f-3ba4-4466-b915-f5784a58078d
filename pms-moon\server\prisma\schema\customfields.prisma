model CustomField {
  id         String    @id @default(uuid())
  type       FieldType

  autoOption AutoOption?
  name       String   @unique

  createdAt  DateTime  @default(now())
  createdBy  String?

  updatedAt  DateTime  @updatedAt
  updatedBy  String?

  TrackSheetCustomFieldMapping TrackSheetCustomFieldMapping[]
  ClientCustomFieldArrangement ClientCustomFieldArrangement[]

  @@map("custom_fields")
}
