"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewSuperAdmin = void 0;
const helpers_1 = require("../../utils/helpers");
const viewSuperAdmin = async (req, res) => {
    try {
        const data = await prisma.superAdmin.findMany();
        if (data) {
            return res.status(200).json(data);
        }
        return res.status(400).json([]);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewSuperAdmin = viewSuperAdmin;
//# sourceMappingURL=view.js.map