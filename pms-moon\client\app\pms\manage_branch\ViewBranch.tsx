"use client";
import DataTable from "@/app/_component/DataTable";
import { Branch, Column } from "./Column";

const ViewBranch = ({ data, permissions }: { data: Branch[]; permissions: string[] }) => {
    return (
        <div className="w-full">
            <DataTable
                data={data}
                columns={Column(permissions)}
                // filter
                // filter_column="name"
                showColDropDowns
                showPageEntries
                // filter2
                // filter_column2="register1"
                className="w-full"
            />
        </div>
    );
};

export default ViewBranch;