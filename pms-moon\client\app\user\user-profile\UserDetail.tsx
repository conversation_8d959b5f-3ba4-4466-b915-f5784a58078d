"use client";
import React, { useState, useRef ,useEffect} from "react";
import <PERSON><PERSON><PERSON><PERSON> from "./RadialChart";
import { StackRadialChart } from "./StackRadialChart";
import { CiEdit } from "react-icons/ci";
import { formSubmit } from "@/lib/helpers";
import { employee_routes } from "@/lib/routePath";
import { toast } from "sonner";
import ComingSoonCard from "@/app/_component/ComingsoonCard";
import UploadProfileImage from "./UploadProfileImage";

import { FaArrowDown } from "react-icons/fa";
import RecursiveTree from "./ChildRecursiveTree";
import UserHierarchy from "./userHierarchy";
import { title } from "process";

const UserDetail = ({
  hrUser,
  userData,
  completedWork,
  resumedWork,
  resumedWorkPercentage,
  userType,
  allEmployee,
  userlevel,
}: any) => {
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [image, setImage] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
//   const [searchTerm, setSearchTerm] = useState("");
// const [searchedUser, setSearchedUser] = useState(null);

const hrUserId = hrUser?.id;
  const {
    loggedInUser,
    parentUser,
    grandParentUser,
    grandGrandParentUser,
    childrenUsers,
    loggedInUserTitle,
    parentUserTitle,
    grandParentUserTitle,
    grandGrandParentUserTitle,
    childrenUsersTitles,
  } = UserHierarchy(hrUserId, allEmployee, userlevel, userData,hrUser);
  // useEffect(() => {
  //   if (searchTerm === "") {
  //     setSearchedUser(null); 
  //   }
  // }, [searchTerm]);

  // const handleSearch = () => {
  //   if (!searchTerm) return;
  
  //   const foundUser = allEmployee.find(
  //     (user) =>{
  //       const fullName = `${user.firstName} ${user.lastName}`.toLowerCase();
  //       return fullName.includes(searchTerm.toLowerCase());
  //     }
  //   );
  
  //   if (foundUser) {
  //     setSearchedUser(foundUser);
  //   } else {
  //     toast.error("User not found");
  //   }
  // };

  
  return (
    <div className="min-h-screen w-full bg-gray-50 p-4 ">
      {/* User Info Section */}
      <div className="flex flex-col md:flex-row items-start bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-200 mb-8 p-2  ">
        <div className="w-full md:1/3  pr-2 ">
          <div className="bg-gradient-to-r from-gray-800 to-gray-900 text-white p-4 rounded-xl flex justify-between items-center mb-6">
            <h1 className="text-lg font-semibold">{userData.role?.name}</h1>
            <h1 className="text-lg font-medium">{userData.username}</h1>
          </div>

          <div className="grid grid-cols-3 md:grid-cols-3 gap-5 bg-gray-50 p-2 rounded-xl m-0 ">
            <div className="space-y-2">
              <div>
                <p className="text-gray-500 text-sm ">First Name</p>
                <p className="text-gray-900 text-base font-medium capitalize">
                  {userData.firstName}
                </p>
              </div>
              <div>
                <p className="text-gray-500 text-sm">Username</p>
                <p className="text-gray-900 text-base font-medium ">
                  {userData.username}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <div>
                <p className="text-gray-500 text-sm">Last Name</p>
                <p className="text-gray-900 text-base font-medium capitalize">
                  {userData.lastName}
                </p>
              </div>{" "}
              <div>
                <p className="text-gray-500 text-sm">Email</p>
                <p className="text-gray-900 text-base font-medium">
                  {userData.email}
                </p>
              </div>
              {/* <div>
                <p className="text-gray-500 text-sm">Working Under</p>
                <p className="text-gray-900 text-base font-medium">
                  Not Specified
                </p>
              </div>
              <div>
                <p className="text-gray-500 text-sm">Department</p>
                <p className="text-gray-900 text-base font-medium">
                  Not Specified
                </p>
              </div> */}
            </div>
            {parentUser && (
              <div className="space-y-2">
                <div className="h-1/2"></div>
                <div>
                  <p className="text-gray-500 text-sm">Managed By:</p>
                  <p className="text-gray-900 text-base font-medium capitalize">
                    {parentUser.firstName.toLowerCase()}{" "}
                    {parentUser.lastName.toLowerCase()}
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Profile Picture Section */}
        <div className=" w-full md:w-1/4 bg-gray-100 flex justify-center items-center p-6 relative rounded-md  ">
          <div className="relative h-full ">
            {/* Display the uploaded image or the default profile image */}
            <img
              src={profileImage || "/profile.png"}
              alt="Profile"
              className="rounded-full w-40 h-40 object-cover ring-4 ring-white shadow-md"
            />
            {/* <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 bg-green-600 px-4 py-1 rounded-full text-white text-xs mt-4">
              Active
            </div> */}

            {/* Edit Icon to trigger file input */}
            <div className="absolute bottom-0 right-5 rounded-full p-1 bg-gray-100">
              {/* <CiEdit
                // onClick={handleEditIconClick}
                className="  text-black cursor-pointer"
                size={25}
              /> */}
              <UploadProfileImage setProfileImage={setProfileImage} />
            </div>

            {/* File input for image upload, hidden */}
            {/* <input
              type="file"
              accept="image/*"
              name="file"
              onChange={handleImageUpload}
              ref={fileInputRef} // Attach the ref to the file input
              className="absolute bottom-0 left-1/2 transform -translate-x-1/2 cursor-pointer opacity-0"
              aria-label="Upload Profile Image"
            /> */}
          </div>
          <div className="absolute top-0 right-2  bg-green-600 px-4 py-1 rounded-full text-white text-xs mt-4">
            Active
          </div>
        </div>
      </div>

      <div className="flex flex-col items-center mb-8">
       
{/* <div className="mb-4 w-full flex items-center gap-4 justify-center">
  <input
    type="text"
    placeholder="Search user by name"
    value={searchTerm}
    onChange={(e) => setSearchTerm(e.target.value)}
    className="border border-gray-300 p-2 rounded-lg w-1/2"
     onKeyDown={(e) => {
    if (e.key === "Enter") handleSearch();
  }}
  />
</div> */}

{/* <div className="mb-6 w-full flex justify-center">
  <div className="relative w-full md:w-1/5">
    <input
      type="text"
      placeholder="Search user by name..."
      value={searchTerm}
      onChange={(e) => setSearchTerm(e.target.value)}
      onKeyDown={(e) => {
        if (e.key === "Enter") handleSearch();
      }}
      className="w-full pl-12 pr-10 py-3 border border-gray-300 rounded-full shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition duration-200"
    />
    <div className="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
      <svg
        className="w-5 h-5 text-gray-400"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M21 21l-4.35-4.35M17 11a6 6 0 11-12 0 6 6 0 0112 0z"
        />
      </svg>
    </div>
    {searchTerm && (
      <button
        onClick={() => setSearchTerm("")}
        className="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-400 hover:text-gray-600"
      >
        ✕
      </button>
    )}
  </div>
</div> */}

        <RecursiveTree
          grandGrandParentUser={
            grandGrandParentUser
              ? { ...grandGrandParentUser, title: grandGrandParentUserTitle }
              : null
          }
          grandParentUser={
            grandParentUser
              ? { ...grandParentUser, title: grandParentUserTitle }
              : null
          }
          parentUser={
            parentUser ? { ...parentUser, title: parentUserTitle } : null
          }
          loggedInUser={
            loggedInUser ? { ...loggedInUser, title: loggedInUserTitle } : null
          }
          childrenUsersTitles={childrenUsersTitles}
          // searchedUser={searchedUser}
        />
      </div>
      {/* User Summary Grid */}
      <div
        className={`grid grid-cols-2 ${
          userType === "MEMBER" ? "md:grid-cols-1" : "md:grid-cols-2"
        } gap-6 mb-8`}
      >
        <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200 ">
          <div className="flex items-center justify-between ">
            <h3 className="text-gray-500 text-sm">Active Task</h3>
            <span className="text-green-600 bg-green-100 px-2 py-1 rounded-full text-xs">
              {resumedWorkPercentage}%
            </span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-2">
            {userType.activeTasks}
          </p>
        </div>

        <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-gray-500 text-sm">Tasks Completed</h3>
            <span className="text-blue-600 bg-blue-100 px-2 py-1 rounded-full text-xs">
              Overall
            </span>
          </div>
          <p className="text-2xl font-bold text-gray-900 mt-2">
            {userType.taskCompleted}
          </p>
        </div>

        {/* {userType !== "MEMBER" && (
          <div className="bg-white p-6 rounded-xl shadow-md border border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-gray-500 text-sm">Team Members</h3>
              <span className="text-purple-600 bg-purple-100 px-2 py-1 rounded-full text-xs">
                Active
              </span>
            </div>
            <p className="text-2xl font-bold text-gray-900 mt-2">8</p>
          </div>
        )} */}
      </div>

      {/* Performance Overview Section */}
      <div className="bg-white p-6 rounded-2xl shadow-lg border border-gray-200">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-semibold text-gray-800">
            Performance Overview
          </h2>
          {/* <select className="px-4 py-2 border rounded-lg text-gray-600 text-sm">
            <option>Last 30 Days</option>
            <option>Last 90 Days</option>
            <option>Last Year</option>
          </select> */}
        </div>

        {/* Charts Container */}
        <div className="grid grid-cols-1 min-w-full  md:flex-row gap-6 ">
          {/* Large Radial Chart (Half Screen) */}
          {/* <div className="w-full  grid grid-cols-1"> */}
          {/* <RadialChart /> */}
          {/* </div> */}

          {/* Two Small Stack Radial Charts in a Column */}
          <div className="grid-cols-1">
            <ComingSoonCard />
          </div>
          {/* <div className="grid-cols-2">
          <ComingSoonCard />
          </div> */}
          {/* <ComingSoonCard /> */}
          <div className="grid grid-cols-2 w-full  ">
            {/* <ComingSoonCard /> */}

            {/* <StackRadialChart /> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetail;
