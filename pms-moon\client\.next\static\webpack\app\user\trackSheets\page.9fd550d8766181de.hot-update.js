"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/trackSheets/page",{

/***/ "(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx":
/*!***************************************************!*\
  !*** ./app/user/trackSheets/createTrackSheet.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./components/ui/form.tsx\");\n/* harmony import */ var _app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/_component/FormInput */ \"(app-pages-browser)/./app/_component/FormInput.tsx\");\n/* harmony import */ var _app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/_component/FormCheckboxGroup */ \"(app-pages-browser)/./app/_component/FormCheckboxGroup.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-minus.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,DollarSign,FileText,Hash,Info,MinusCircle,PlusCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-plus.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/helpers */ \"(app-pages-browser)/./lib/helpers.ts\");\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/lib/index.mjs\");\n/* harmony import */ var _app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/_component/SearchSelect */ \"(app-pages-browser)/./app/_component/SearchSelect.tsx\");\n/* harmony import */ var _app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/_component/PageInput */ \"(app-pages-browser)/./app/_component/PageInput.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./LegrandDetailsComponent */ \"(app-pages-browser)/./app/user/trackSheets/LegrandDetailsComponent.tsx\");\n/* harmony import */ var _ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./ClientSelectPage */ \"(app-pages-browser)/./app/user/trackSheets/ClientSelectPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst validateFtpPageFormat = (value)=>{\n    if (!value || value.trim() === \"\") return false;\n    const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n    const match = value.match(ftpPageRegex);\n    if (!match) return false;\n    const currentPage = parseInt(match[1], 10);\n    const totalPages = parseInt(match[2], 10);\n    return currentPage > 0 && totalPages > 0 && currentPage <= totalPages;\n};\nconst trackSheetSchema = zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n    clientId: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Client is required\"),\n    entries: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n        company: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Company is required\"),\n        division: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice is required\"),\n        masterInvoice: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        bol: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice date is required\"),\n        receivedDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Received date is required\"),\n        shipmentDate: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        carrierName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Carrier name is required\"),\n        invoiceStatus: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice status is required\"),\n        manualMatching: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Manual matching is required\"),\n        invoiceType: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice type is required\"),\n        billToClient: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        currency: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Currency is required\"),\n        qtyShipped: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        weightUnitName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Weight unit is required\"),\n        quantityBilledText: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        invoiceTotal: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"Invoice total is required\"),\n        savings: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        financialNotes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        ftpFileName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP File Name is required\"),\n        ftpPage: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().min(1, \"FTP Page is required\").refine((value)=>validateFtpPageFormat(value), (value)=>{\n            if (!value || value.trim() === \"\") {\n                return {\n                    message: \"FTP Page is required\"\n                };\n            }\n            const ftpPageRegex = /^(\\d+)\\s+of\\s+(\\d+)$/i;\n            const match = value.match(ftpPageRegex);\n            if (!match) {\n                return {\n                    message: \"\"\n                };\n            }\n            const currentPage = parseInt(match[1], 10);\n            const totalPages = parseInt(match[2], 10);\n            if (currentPage <= 0 || totalPages <= 0) {\n                return {\n                    message: \"Page numbers must be positive (greater than 0)\"\n                };\n            }\n            if (currentPage > totalPages) {\n                return {\n                    message: \"Please enter a page number between \".concat(totalPages, \" and \").concat(currentPage, \" \")\n                };\n            }\n            return {\n                message: \"Invalid page format\"\n            };\n        }),\n        docAvailable: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.string()).optional().default([]),\n        otherDocuments: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        notes: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        mistake: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandCompanyName: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        legrandZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        shipperZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        consigneeZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAlias: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoAddress: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        billtoZipcode: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n        customFields: zod__WEBPACK_IMPORTED_MODULE_16__.z.array(zod__WEBPACK_IMPORTED_MODULE_16__.z.object({\n            id: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            name: zod__WEBPACK_IMPORTED_MODULE_16__.z.string(),\n            type: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional(),\n            value: zod__WEBPACK_IMPORTED_MODULE_16__.z.string().optional()\n        })).default([])\n    }))\n});\nconst CreateTrackSheet = (param)=>{\n    let { client, carrier, associate, userData, activeView, setActiveView, permissions } = param;\n    _s();\n    const companyFieldRefs = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const [generatedFilenames, setGeneratedFilenames] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [filenameValidation, setFilenameValidation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [missingFields, setMissingFields] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [legrandData, setLegrandData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [manualMatchingData, setManualMatchingData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customFieldsRefresh, setCustomFieldsRefresh] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showFullForm, setShowFullForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [initialAssociateId, setInitialAssociateId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [initialClientId, setInitialClientId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const selectionForm = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\"\n        }\n    });\n    const associateOptions = associate === null || associate === void 0 ? void 0 : associate.map((a)=>{\n        var _a_id;\n        return {\n            value: (_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString(),\n            label: a.name,\n            name: a.name\n        };\n    });\n    const carrierOptions = carrier === null || carrier === void 0 ? void 0 : carrier.map((c)=>{\n        var _c_id;\n        return {\n            value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n            label: c.name\n        };\n    });\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(trackSheetSchema),\n        defaultValues: {\n            associateId: \"\",\n            clientId: \"\",\n            entries: [\n                {\n                    company: \"\",\n                    division: \"\",\n                    invoice: \"\",\n                    masterInvoice: \"\",\n                    bol: \"\",\n                    invoiceDate: \"\",\n                    receivedDate: \"\",\n                    shipmentDate: \"\",\n                    carrierName: \"\",\n                    invoiceStatus: \"ENTRY\",\n                    manualMatching: \"\",\n                    invoiceType: \"\",\n                    billToClient: \"\",\n                    currency: \"\",\n                    qtyShipped: \"\",\n                    weightUnitName: \"\",\n                    quantityBilledText: \"\",\n                    invoiceTotal: \"\",\n                    savings: \"\",\n                    financialNotes: \"\",\n                    ftpFileName: \"\",\n                    ftpPage: \"\",\n                    docAvailable: [],\n                    otherDocuments: \"\",\n                    notes: \"\",\n                    mistake: \"\",\n                    legrandAlias: \"\",\n                    legrandCompanyName: \"\",\n                    legrandAddress: \"\",\n                    legrandZipcode: \"\",\n                    shipperAlias: \"\",\n                    shipperAddress: \"\",\n                    shipperZipcode: \"\",\n                    consigneeAlias: \"\",\n                    consigneeAddress: \"\",\n                    consigneeZipcode: \"\",\n                    billtoAlias: \"\",\n                    billtoAddress: \"\",\n                    billtoZipcode: \"\",\n                    customFields: []\n                }\n            ]\n        }\n    });\n    const getFilteredClientOptions = ()=>{\n        if (!initialAssociateId) {\n            return (client === null || client === void 0 ? void 0 : client.map((c)=>{\n                var _c_id;\n                return {\n                    value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                    label: c.client_name,\n                    name: c.client_name\n                };\n            })) || [];\n        }\n        const filteredClients = (client === null || client === void 0 ? void 0 : client.filter((c)=>{\n            var _c_associateId;\n            return ((_c_associateId = c.associateId) === null || _c_associateId === void 0 ? void 0 : _c_associateId.toString()) === initialAssociateId;\n        })) || [];\n        return filteredClients.map((c)=>{\n            var _c_id;\n            return {\n                value: (_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString(),\n                label: c.client_name,\n                name: c.client_name\n            };\n        });\n    };\n    const clientOptions = getFilteredClientOptions();\n    const entries = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch)({\n        control: form.control,\n        name: \"entries\"\n    });\n    const validateClientForAssociate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, currentClientId)=>{\n        if (associateId && currentClientId) {\n            var _currentClient_associateId;\n            const currentClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === currentClientId;\n            });\n            if (currentClient && ((_currentClient_associateId = currentClient.associateId) === null || _currentClient_associateId === void 0 ? void 0 : _currentClient_associateId.toString()) !== associateId) {\n                form.setValue(\"clientId\", \"\");\n                setInitialClientId(\"\");\n                return false;\n            }\n        }\n        return true;\n    }, [\n        client,\n        form\n    ]);\n    const clearEntrySpecificClients = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const currentEntries = form.getValues(\"entries\") || [];\n        if (currentEntries.length > 0) {\n            const hasEntrySpecificClients = currentEntries.some((entry)=>entry.clientId);\n            if (hasEntrySpecificClients) {\n                const updatedEntries = currentEntries.map((entry)=>({\n                        ...entry,\n                        clientId: \"\"\n                    }));\n                form.setValue(\"entries\", updatedEntries);\n            }\n        }\n    }, [\n        form\n    ]);\n    const fetchLegrandData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.legrandMapping_routes.GET_LEGRAND_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setLegrandData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching LEGRAND mapping data:\", error);\n        }\n    }, []);\n    const fetchManualMatchingData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            const response = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.manualMatchingMapping_routes.GET_MANUAL_MATCHING_MAPPINGS);\n            if (response && Array.isArray(response)) {\n                setManualMatchingData(response);\n            }\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Error fetching manual matching mapping data:\", error);\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        fetchLegrandData();\n        fetchManualMatchingData();\n    }, [\n        fetchLegrandData,\n        fetchManualMatchingData\n    ]);\n    const handleLegrandDataChange = (entryIndex, businessUnit, divisionCode)=>{\n        form.setValue(\"entries.\".concat(entryIndex, \".company\"), businessUnit);\n        if (divisionCode) {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), divisionCode);\n            handleManualMatchingAutoFill(entryIndex, divisionCode);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".division\"), \"\");\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    };\n    const handleManualMatchingAutoFill = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, division)=>{\n        var _formValues_entries, _clientOptions_find;\n        if (!division || !manualMatchingData.length) {\n            return;\n        }\n        const formValues = form.getValues();\n        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        if (entryClientName !== \"LEGRAND\") {\n            return;\n        }\n        const matchingEntry = manualMatchingData.find((mapping)=>mapping.division === division);\n        if (matchingEntry && matchingEntry.ManualShipment) {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), matchingEntry.ManualShipment);\n        } else {\n            form.setValue(\"entries.\".concat(entryIndex, \".manualMatching\"), \"\");\n        }\n    }, [\n        form,\n        manualMatchingData,\n        clientOptions\n    ]);\n    const fetchCustomFieldsForClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (clientId)=>{\n        if (!clientId) return [];\n        try {\n            const allCustomFieldsResponse = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.getAllData)(\"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.clientCustomFields_routes.GET_CLIENT_CUSTOM_FIELDS, \"/\").concat(clientId));\n            let customFieldsData = [];\n            if (allCustomFieldsResponse && allCustomFieldsResponse.custom_fields && allCustomFieldsResponse.custom_fields.length > 0) {\n                customFieldsData = allCustomFieldsResponse.custom_fields.map((field)=>{\n                    let autoFilledValue = \"\";\n                    if (field.type === \"AUTO\") {\n                        if (field.autoOption === \"DATE\") {\n                            autoFilledValue = new Date().toISOString().split(\"T\")[0];\n                        } else if (field.autoOption === \"USERNAME\") {\n                            autoFilledValue = (userData === null || userData === void 0 ? void 0 : userData.username) || \"\";\n                        }\n                    }\n                    return {\n                        id: field.id,\n                        name: field.name,\n                        type: field.type,\n                        autoOption: field.autoOption,\n                        value: autoFilledValue\n                    };\n                });\n            }\n            return customFieldsData;\n        } catch (error) {\n            return [];\n        }\n    }, [\n        userData\n    ]);\n    const { fields, append, remove } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFieldArray)({\n        control: form.control,\n        name: \"entries\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        companyFieldRefs.current = companyFieldRefs.current.slice(0, fields.length);\n    }, [\n        fields.length\n    ]);\n    const generateFilename = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, formValues)=>{\n        try {\n            const entry = formValues.entries[entryIndex];\n            if (!entry) return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Entry data\"\n                ]\n            };\n            const missing = [];\n            const selectedAssociate = associate === null || associate === void 0 ? void 0 : associate.find((a)=>{\n                var _a_id;\n                return ((_a_id = a.id) === null || _a_id === void 0 ? void 0 : _a_id.toString()) === formValues.associateId;\n            });\n            const associateName = (selectedAssociate === null || selectedAssociate === void 0 ? void 0 : selectedAssociate.name) || \"\";\n            if (!associateName) {\n                missing.push(\"Associate\");\n            }\n            const entryClientId = entry.clientId || formValues.clientId;\n            const selectedClient = client === null || client === void 0 ? void 0 : client.find((c)=>{\n                var _c_id;\n                return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entryClientId;\n            });\n            const clientName = (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.client_name) || \"\";\n            if (!clientName) {\n                missing.push(\"Client\");\n            }\n            let carrierName = \"\";\n            if (entry.carrierName) {\n                const carrierOption = carrier === null || carrier === void 0 ? void 0 : carrier.find((c)=>{\n                    var _c_id;\n                    return ((_c_id = c.id) === null || _c_id === void 0 ? void 0 : _c_id.toString()) === entry.carrierName;\n                });\n                carrierName = (carrierOption === null || carrierOption === void 0 ? void 0 : carrierOption.name) || \"\";\n            }\n            if (!carrierName) {\n                missing.push(\"Carrier\");\n            }\n            const receivedDate = entry.receivedDate;\n            const invoiceDate = entry.invoiceDate;\n            const currentDate = new Date();\n            const year = currentDate.getFullYear().toString();\n            const month = currentDate.toLocaleString(\"default\", {\n                month: \"short\"\n            }).toUpperCase();\n            if (!invoiceDate) {\n                missing.push(\"Invoice Date\");\n            }\n            let receivedDateStr = \"\";\n            if (receivedDate) {\n                const date = new Date(receivedDate);\n                receivedDateStr = date.toISOString().split(\"T\")[0];\n            } else {\n                missing.push(\"Received Date\");\n            }\n            const ftpFileName = entry.ftpFileName || \"\";\n            const baseFilename = ftpFileName ? ftpFileName.endsWith(\".pdf\") ? ftpFileName : \"\".concat(ftpFileName, \".pdf\") : \"\";\n            if (!baseFilename) {\n                missing.push(\"FTP File Name\");\n            }\n            const isValid = missing.length === 0;\n            const filename = isValid ? \"/\".concat(associateName, \"/\").concat(clientName, \"/CARRIERINVOICES/\").concat(carrierName, \"/\").concat(year, \"/\").concat(month, \"/\").concat(receivedDateStr, \"/\").concat(baseFilename) : \"\";\n            return {\n                filename,\n                isValid,\n                missing\n            };\n        } catch (error) {\n            return {\n                filename: \"\",\n                isValid: false,\n                missing: [\n                    \"Error generating filename\"\n                ]\n            };\n        }\n    }, [\n        client,\n        carrier,\n        associate\n    ]);\n    const handleCompanyAutoPopulation = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((entryIndex, entryClientId)=>{\n        var _clientOptions_find;\n        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n        const currentEntry = form.getValues(\"entries.\".concat(entryIndex));\n        if (entryClientName && entryClientName !== \"LEGRAND\") {\n            form.setValue(\"entries.\".concat(entryIndex, \".company\"), entryClientName);\n        } else if (entryClientName === \"LEGRAND\") {\n            const shipperAlias = currentEntry.shipperAlias;\n            const consigneeAlias = currentEntry.consigneeAlias;\n            const billtoAlias = currentEntry.billtoAlias;\n            const hasAnyLegrandData = shipperAlias || consigneeAlias || billtoAlias;\n            if (!hasAnyLegrandData && currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        } else {\n            if (currentEntry.company !== \"\") {\n                form.setValue(\"entries.\".concat(entryIndex, \".company\"), \"\");\n            }\n        }\n    }, [\n        form,\n        clientOptions\n    ]);\n    const handleCustomFieldsFetch = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (entryIndex, entryClientId)=>{\n        var _currentCustomFields_, _currentCustomFields_1;\n        if (!entryClientId) {\n            const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\"));\n            if (currentCustomFields && currentCustomFields.length > 0) {\n                form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), []);\n            }\n            return;\n        }\n        const currentCustomFields = form.getValues(\"entries.\".concat(entryIndex, \".customFields\")) || [];\n        const hasEmptyAutoUsernameFields = currentCustomFields.some((field)=>field.type === \"AUTO\" && field.autoOption === \"USERNAME\" && !field.value && (userData === null || userData === void 0 ? void 0 : userData.username));\n        const shouldFetchCustomFields = currentCustomFields.length === 0 || currentCustomFields.length > 0 && !((_currentCustomFields_ = currentCustomFields[0]) === null || _currentCustomFields_ === void 0 ? void 0 : _currentCustomFields_.clientId) || ((_currentCustomFields_1 = currentCustomFields[0]) === null || _currentCustomFields_1 === void 0 ? void 0 : _currentCustomFields_1.clientId) !== entryClientId || hasEmptyAutoUsernameFields;\n        if (shouldFetchCustomFields) {\n            const customFieldsData = await fetchCustomFieldsForClient(entryClientId);\n            const fieldsWithClientId = customFieldsData.map((field)=>({\n                    ...field,\n                    clientId: entryClientId\n                }));\n            form.setValue(\"entries.\".concat(entryIndex, \".customFields\"), fieldsWithClientId);\n            setTimeout(()=>{\n                fieldsWithClientId.forEach((field, fieldIndex)=>{\n                    const fieldPath = \"entries.\".concat(entryIndex, \".customFields.\").concat(fieldIndex, \".value\");\n                    if (field.value) {\n                        form.setValue(fieldPath, field.value);\n                    }\n                });\n                setCustomFieldsRefresh((prev)=>prev + 1);\n            }, 100);\n        }\n    }, [\n        form,\n        fetchCustomFieldsForClient,\n        userData === null || userData === void 0 ? void 0 : userData.username\n    ]);\n    const updateFilenames = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const formValues = form.getValues();\n        const newFilenames = [];\n        const newValidation = [];\n        const newMissingFields = [];\n        if (formValues.entries && Array.isArray(formValues.entries)) {\n            formValues.entries.forEach((_, index)=>{\n                const { filename, isValid, missing } = generateFilename(index, formValues);\n                newFilenames[index] = filename;\n                newValidation[index] = isValid;\n                newMissingFields[index] = missing || [];\n            });\n        }\n        setGeneratedFilenames(newFilenames);\n        setFilenameValidation(newValidation);\n        setMissingFields(newMissingFields);\n    }, [\n        form,\n        generateFilename\n    ]);\n    const handleInitialSelection = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((associateId, clientId)=>{\n        form.setValue(\"associateId\", associateId);\n        form.setValue(\"clientId\", clientId);\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(0, clientId);\n            handleCustomFieldsFetch(0, clientId);\n            updateFilenames();\n        }, 50);\n        setShowFullForm(true);\n    }, [\n        form,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch,\n        updateFilenames\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        setTimeout(()=>{\n            updateFilenames();\n            const formValues = form.getValues();\n            if (formValues.entries && Array.isArray(formValues.entries)) {\n                formValues.entries.forEach((entry, index)=>{\n                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (index === 0 ? formValues.clientId : \"\");\n                    if (entryClientId) {\n                        handleCustomFieldsFetch(index, entryClientId);\n                    }\n                });\n            }\n        }, 50);\n    }, [\n        updateFilenames,\n        handleCustomFieldsFetch,\n        form\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const subscription = form.watch((_, param)=>{\n            let { name } = param;\n            if (name && (name.includes(\"associateId\") || name.includes(\"clientId\") || name.includes(\"carrierName\") || name.includes(\"invoiceDate\") || name.includes(\"receivedDate\") || name.includes(\"ftpFileName\") || name.includes(\"company\") || name.includes(\"division\"))) {\n                updateFilenames();\n                if (name.includes(\"division\")) {\n                    const entryMatch = name.match(/entries\\.(\\d+)\\.division/);\n                    if (entryMatch) {\n                        var _formValues_entries, _clientOptions_find;\n                        const entryIndex = parseInt(entryMatch[1], 10);\n                        const formValues = form.getValues();\n                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                        if (entryClientName === \"LEGRAND\") {\n                            var _formValues_entries_entryIndex, _formValues_entries1;\n                            const divisionValue = (_formValues_entries1 = formValues.entries) === null || _formValues_entries1 === void 0 ? void 0 : (_formValues_entries_entryIndex = _formValues_entries1[entryIndex]) === null || _formValues_entries_entryIndex === void 0 ? void 0 : _formValues_entries_entryIndex.division;\n                            if (divisionValue) {\n                                handleManualMatchingAutoFill(entryIndex, divisionValue);\n                            }\n                        }\n                    }\n                }\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form,\n        updateFilenames,\n        handleManualMatchingAutoFill,\n        clientOptions\n    ]);\n    const onSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        try {\n            const currentFormValues = form.getValues();\n            const currentValidation = [];\n            const currentMissingFields = [];\n            const currentFilenames = [];\n            if (currentFormValues.entries && Array.isArray(currentFormValues.entries)) {\n                currentFormValues.entries.forEach((_, index)=>{\n                    const { filename, isValid, missing } = generateFilename(index, currentFormValues);\n                    currentValidation[index] = isValid;\n                    currentMissingFields[index] = missing || [];\n                    currentFilenames[index] = filename;\n                });\n            }\n            const allFilenamesValid = currentValidation.every((isValid)=>isValid);\n            if (!allFilenamesValid) {\n                const invalidEntries = currentValidation.map((isValid, index)=>({\n                        index,\n                        isValid,\n                        missing: currentMissingFields[index]\n                    })).filter((entry)=>!entry.isValid);\n                const errorDetails = invalidEntries.map((entry)=>\"Entry \".concat(entry.index + 1, \": \").concat(entry.missing.join(\", \"))).join(\" | \");\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Cannot submit: Missing fields - \".concat(errorDetails));\n                return;\n            }\n            const entries = values.entries.map((entry, index)=>{\n                var _entry_customFields;\n                return {\n                    company: entry.company,\n                    division: entry.division,\n                    invoice: entry.invoice,\n                    masterInvoice: entry.masterInvoice,\n                    bol: entry.bol,\n                    invoiceDate: entry.invoiceDate,\n                    receivedDate: entry.receivedDate,\n                    shipmentDate: entry.shipmentDate,\n                    carrierId: entry.carrierName,\n                    invoiceStatus: entry.invoiceStatus,\n                    manualMatching: entry.manualMatching,\n                    invoiceType: entry.invoiceType,\n                    currency: entry.currency,\n                    qtyShipped: entry.qtyShipped,\n                    weightUnitName: entry.weightUnitName,\n                    quantityBilledText: entry.quantityBilledText,\n                    invoiceTotal: entry.invoiceTotal,\n                    savings: entry.savings,\n                    ftpFileName: entry.ftpFileName,\n                    ftpPage: entry.ftpPage,\n                    docAvailable: entry.docAvailable,\n                    notes: entry.notes,\n                    mistake: entry.mistake,\n                    filePath: generatedFilenames[index],\n                    customFields: (_entry_customFields = entry.customFields) === null || _entry_customFields === void 0 ? void 0 : _entry_customFields.map((cf)=>({\n                            id: cf.id,\n                            value: cf.value\n                        }))\n                };\n            });\n            const formData = {\n                clientId: values.clientId,\n                entries: entries\n            };\n            const result = await (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_7__.formSubmit)(_lib_routePath__WEBPACK_IMPORTED_MODULE_8__.trackSheets_routes.CREATE_TRACK_SHEETS, \"POST\", formData);\n            if (result.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.success(\"All TrackSheets created successfully\");\n                form.reset();\n                setTimeout(()=>{\n                    handleInitialSelection(initialAssociateId, initialClientId);\n                }, 100);\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(result.message || \"Failed to create TrackSheets\");\n            }\n            router.refresh();\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"An error occurred while creating the TrackSheets\");\n        }\n    }, [\n        form,\n        router,\n        generateFilename,\n        initialAssociateId,\n        initialClientId,\n        handleInitialSelection,\n        generatedFilenames\n    ]);\n    const addNewEntry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        const newIndex = fields.length;\n        append({\n            clientId: initialClientId,\n            company: \"\",\n            division: \"\",\n            invoice: \"\",\n            masterInvoice: \"\",\n            bol: \"\",\n            invoiceDate: \"\",\n            receivedDate: \"\",\n            shipmentDate: \"\",\n            carrierName: \"\",\n            invoiceStatus: \"ENTRY\",\n            manualMatching: \"\",\n            invoiceType: \"\",\n            billToClient: \"\",\n            currency: \"\",\n            qtyShipped: \"\",\n            weightUnitName: \"\",\n            quantityBilledText: \"\",\n            invoiceTotal: \"\",\n            savings: \"\",\n            financialNotes: \"\",\n            ftpFileName: \"\",\n            ftpPage: \"\",\n            docAvailable: [],\n            otherDocuments: \"\",\n            notes: \"\",\n            mistake: \"\",\n            legrandAlias: \"\",\n            legrandCompanyName: \"\",\n            legrandAddress: \"\",\n            legrandZipcode: \"\",\n            shipperAlias: \"\",\n            shipperAddress: \"\",\n            shipperZipcode: \"\",\n            consigneeAlias: \"\",\n            consigneeAddress: \"\",\n            consigneeZipcode: \"\",\n            billtoAlias: \"\",\n            billtoAddress: \"\",\n            billtoZipcode: \"\",\n            customFields: []\n        });\n        setTimeout(()=>{\n            handleCompanyAutoPopulation(newIndex, initialClientId);\n            handleCustomFieldsFetch(newIndex, initialClientId);\n            if (companyFieldRefs.current[newIndex]) {\n                var _companyFieldRefs_current_newIndex, _companyFieldRefs_current_newIndex1, _companyFieldRefs_current_newIndex2;\n                const inputElement = ((_companyFieldRefs_current_newIndex = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex === void 0 ? void 0 : _companyFieldRefs_current_newIndex.querySelector(\"input\")) || ((_companyFieldRefs_current_newIndex1 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex1 === void 0 ? void 0 : _companyFieldRefs_current_newIndex1.querySelector(\"button\")) || ((_companyFieldRefs_current_newIndex2 = companyFieldRefs.current[newIndex]) === null || _companyFieldRefs_current_newIndex2 === void 0 ? void 0 : _companyFieldRefs_current_newIndex2.querySelector(\"select\"));\n                if (inputElement) {\n                    inputElement.focus();\n                    try {\n                        inputElement.click();\n                    } catch (e) {}\n                }\n            }\n            updateFilenames();\n        }, 200);\n    }, [\n        append,\n        fields.length,\n        updateFilenames,\n        initialClientId,\n        handleCompanyAutoPopulation,\n        handleCustomFieldsFetch\n    ]);\n    const handleFormKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((e)=>{\n        if (e.ctrlKey && (e.key === \"s\" || e.key === \"S\")) {\n            e.preventDefault();\n            form.handleSubmit(onSubmit)();\n        } else if (e.shiftKey && e.key === \"Enter\") {\n            e.preventDefault();\n            addNewEntry();\n        } else if (e.key === \"Enter\" && !e.ctrlKey && !e.shiftKey && !e.altKey) {\n            const activeElement = document.activeElement;\n            const isSubmitButton = (activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute(\"type\")) === \"submit\";\n            if (isSubmitButton) {\n                e.preventDefault();\n                form.handleSubmit(onSubmit)();\n            }\n        }\n    }, [\n        form,\n        onSubmit,\n        addNewEntry\n    ]);\n    const removeEntry = (index)=>{\n        if (fields.length > 1) {\n            remove(index);\n        } else {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"You must have at least one entry\");\n        }\n    };\n    const getFilteredDivisionOptions = (company, entryIndex)=>{\n        if (!company || !legrandData.length) {\n            return [];\n        }\n        if (entryIndex !== undefined) {\n            var _formValues_entries, _clientOptions_find;\n            const formValues = form.getValues();\n            const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[entryIndex];\n            const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || (entryIndex === 0 ? formValues.clientId : \"\");\n            const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n            if (entryClientName === \"LEGRAND\") {\n                const shipperAlias = form.getValues(\"entries.\".concat(entryIndex, \".shipperAlias\"));\n                const consigneeAlias = form.getValues(\"entries.\".concat(entryIndex, \".consigneeAlias\"));\n                const billtoAlias = form.getValues(\"entries.\".concat(entryIndex, \".billtoAlias\"));\n                const currentAlias = shipperAlias || consigneeAlias || billtoAlias;\n                if (currentAlias) {\n                    const selectedData = legrandData.find((data)=>{\n                        const uniqueKey = \"\".concat(data.customeCode, \"-\").concat(data.aliasShippingNames || data.legalName, \"-\").concat(data.shippingBillingAddress);\n                        return uniqueKey === currentAlias;\n                    });\n                    if (selectedData) {\n                        const baseAliasName = selectedData.aliasShippingNames && selectedData.aliasShippingNames !== \"NONE\" ? selectedData.aliasShippingNames : selectedData.legalName;\n                        const sameAliasEntries = legrandData.filter((data)=>{\n                            const dataAliasName = data.aliasShippingNames && data.aliasShippingNames !== \"NONE\" ? data.aliasShippingNames : data.legalName;\n                            return dataAliasName === baseAliasName;\n                        });\n                        const allDivisions = [];\n                        sameAliasEntries.forEach((entry)=>{\n                            if (entry.customeCode) {\n                                if (entry.customeCode.includes(\"/\")) {\n                                    const splitDivisions = entry.customeCode.split(\"/\").map((d)=>d.trim());\n                                    allDivisions.push(...splitDivisions);\n                                } else {\n                                    allDivisions.push(entry.customeCode);\n                                }\n                            }\n                        });\n                        const uniqueDivisions = Array.from(new Set(allDivisions.filter((code)=>code)));\n                        if (uniqueDivisions.length > 1) {\n                            const contextDivisions = uniqueDivisions.sort().map((code)=>({\n                                    value: code,\n                                    label: code\n                                }));\n                            return contextDivisions;\n                        } else {}\n                    }\n                }\n            }\n        }\n        const allDivisions = [];\n        legrandData.filter((data)=>data.businessUnit === company && data.customeCode).forEach((data)=>{\n            if (data.customeCode.includes(\"/\")) {\n                const splitDivisions = data.customeCode.split(\"/\").map((d)=>d.trim());\n                allDivisions.push(...splitDivisions);\n            } else {\n                allDivisions.push(data.customeCode);\n            }\n        });\n        const divisions = Array.from(new Set(allDivisions.filter((code)=>code))).sort().map((code)=>({\n                value: code,\n                label: code\n            }));\n        return divisions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full px-2 py-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4 pl-3 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"view\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"view\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"View TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1064,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"default\",\n                                onClick: ()=>setActiveView(\"create\"),\n                                className: \"w-40 shadow-md rounded-xl text-base transition-all duration-200 \".concat(activeView === \"create\" ? \"bg-neutral-800 hover:bg-neutral-900 text-white\" : \"bg-white text-neutral-800 border border-neutral-300 hover:bg-neutral-100\"),\n                                children: \"Create TrackSheet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1075,\n                                columnNumber: 13\n                            }, undefined),\n                            activeView === \"create\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                                ...selectionForm,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4 ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-[200px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                form: selectionForm,\n                                                name: \"associateId\",\n                                                label: \"Select Associate\",\n                                                placeholder: \"Search Associate...\",\n                                                isRequired: true,\n                                                options: associateOptions || [],\n                                                onValueChange: (value)=>{\n                                                    setInitialAssociateId(value);\n                                                    if (value && initialClientId) {\n                                                        validateClientForAssociate(value, initialClientId);\n                                                    } else {\n                                                        setInitialClientId(\"\");\n                                                        selectionForm.setValue(\"clientId\", \"\");\n                                                    }\n                                                    setShowFullForm(false);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1092,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1091,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"min-w-[200px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                form: selectionForm,\n                                                name: \"clientId\",\n                                                label: \"Select Client\",\n                                                placeholder: \"Search Client...\",\n                                                isRequired: true,\n                                                disabled: !initialAssociateId,\n                                                options: clientOptions || [],\n                                                onValueChange: (value)=>{\n                                                    setInitialClientId(value);\n                                                    if (showFullForm) {\n                                                        clearEntrySpecificClients();\n                                                    }\n                                                    if (value && initialAssociateId) {\n                                                        setTimeout(()=>{\n                                                            handleInitialSelection(initialAssociateId, value);\n                                                        }, 100);\n                                                    } else {\n                                                        setShowFullForm(false);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1113,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1112,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                lineNumber: 1089,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1063,\n                        columnNumber: 11\n                    }, undefined),\n                    activeView === \"view\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full animate-in fade-in duration-500 rounded-2xl shadow-sm dark:bg-gray-800 p-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ClientSelectPage__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            permissions: permissions,\n                            client: client\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1144,\n                        columnNumber: 13\n                    }, undefined) : /* Form Section - Only show when both associate and client are selected */ showFullForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_3__.Form, {\n                        ...form,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: form.handleSubmit(onSubmit),\n                            onKeyDown: handleFormKeyDown,\n                            className: \"space-y-3\",\n                            children: [\n                                fields.map((field, index)=>{\n                                    var _missingFields_index;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2 bg-gray-100 rounded-md px-3 py-2 border border-gray-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-gray-600 rounded-full flex items-center justify-center text-white font-semibold text-xs\",\n                                                            children: index + 1\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1164,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                            children: [\n                                                                \"Entry #\",\n                                                                index + 1\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1167,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                    lineNumber: 1163,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1162,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-50 rounded-md p-2 mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1180,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: \"Client Information\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1181,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1179,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mb-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"mt-2\",\n                                                                                form: form,\n                                                                                label: \"FTP File Name\",\n                                                                                name: \"entries.\".concat(index, \".ftpFileName\"),\n                                                                                type: \"text\",\n                                                                                isRequired: true\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1188,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1187,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_PageInput__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"mt-2\",\n                                                                            form: form,\n                                                                            label: \"FTP Page\",\n                                                                            name: \"entries.\".concat(index, \".ftpPage\"),\n                                                                            isRequired: true\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1197,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"mt-0\",\n                                                                                form: form,\n                                                                                name: \"entries.\".concat(index, \".carrierName\"),\n                                                                                label: \"Select Carrier\",\n                                                                                placeholder: \"Search Carrier\",\n                                                                                isRequired: true,\n                                                                                options: (carrierOptions === null || carrierOptions === void 0 ? void 0 : carrierOptions.filter((carrier)=>{\n                                                                                    const currentEntries = form.getValues(\"entries\") || [];\n                                                                                    const isSelectedInOtherEntries = currentEntries.some((entry, entryIndex)=>entryIndex !== index && entry.carrierName === carrier.value);\n                                                                                    return !isSelectedInOtherEntries;\n                                                                                })) || [],\n                                                                                onValueChange: ()=>{\n                                                                                    setTimeout(()=>updateFilenames(), 100);\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1205,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1204,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"mt-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                            className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                                                                            children: [\n                                                                                                \"Billed to \",\n                                                                                                entryClientName || \"Client\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1239,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                            className: \"flex items-center space-x-4 h-10\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            name: \"entries.\".concat(index, \".billToClient\"),\n                                                                                                            value: \"yes\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1244,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"Yes\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1250,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1243,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                                    className: \"flex items-center\",\n                                                                                                    children: [\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                                            type: \"radio\",\n                                                                                                            name: \"entries.\".concat(index, \".billToClient\"),\n                                                                                                            value: \"no\",\n                                                                                                            className: \"mr-2\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1253,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined),\n                                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                            className: \"text-sm\",\n                                                                                                            children: \"No\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                            lineNumber: 1259,\n                                                                                                            columnNumber: 41\n                                                                                                        }, undefined)\n                                                                                                    ]\n                                                                                                }, void 0, true, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                    lineNumber: 1252,\n                                                                                                    columnNumber: 39\n                                                                                                }, undefined)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                            lineNumber: 1242,\n                                                                                            columnNumber: 37\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1238,\n                                                                                    columnNumber: 35\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1230,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1186,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                (()=>{\n                                                                    var _formValues_entries, _clientOptions_find;\n                                                                    const formValues = form.getValues();\n                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                    return entryClientName === \"LEGRAND\";\n                                                                })() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mb-3\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-3 mb-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Shipper\",\n                                                                                fieldPrefix: \"shipper\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1282,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Consignee\",\n                                                                                fieldPrefix: \"consignee\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1289,\n                                                                                columnNumber: 33\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_LegrandDetailsComponent__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                form: form,\n                                                                                entryIndex: index,\n                                                                                onLegrandDataChange: handleLegrandDataChange,\n                                                                                blockTitle: \"Bill-to\",\n                                                                                fieldPrefix: \"billto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1296,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1281,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1280,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            ref: (el)=>{\n                                                                                companyFieldRefs.current[index] = el;\n                                                                            },\n                                                                            className: \"flex flex-col mb-1 [&_input]:h-10\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                form: form,\n                                                                                label: \"Company\",\n                                                                                name: \"entries.\".concat(index, \".company\"),\n                                                                                type: \"text\",\n                                                                                disable: (()=>{\n                                                                                    var _formValues_entries, _clientOptions_find;\n                                                                                    const formValues = form.getValues();\n                                                                                    const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                    const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                    const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                    return entryClientName === \"LEGRAND\";\n                                                                                })()\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1315,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1309,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex flex-col\",\n                                                                            children: (()=>{\n                                                                                var _formValues_entries, _clientOptions_find, _entries_index;\n                                                                                const formValues = form.getValues();\n                                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                                const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                                const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                                const isLegrand = entryClientName === \"LEGRAND\";\n                                                                                return isLegrand ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                    form: form,\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    label: \"Division\",\n                                                                                    placeholder: \"Search Division\",\n                                                                                    disabled: false,\n                                                                                    options: getFilteredDivisionOptions((entries === null || entries === void 0 ? void 0 : (_entries_index = entries[index]) === null || _entries_index === void 0 ? void 0 : _entries_index.company) || \"\", index),\n                                                                                    onValueChange: (value)=>{\n                                                                                        handleManualMatchingAutoFill(index, value);\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1352,\n                                                                                    columnNumber: 35\n                                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                    form: form,\n                                                                                    label: \"Division\",\n                                                                                    name: \"entries.\".concat(index, \".division\"),\n                                                                                    type: \"text\",\n                                                                                    placeholder: \"Enter Division\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1367,\n                                                                                    columnNumber: 35\n                                                                                }, undefined);\n                                                                            })()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1337,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1308,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1178,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1176,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3 pb-3 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-orange-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1384,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Document Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1385,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1383,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Master Invoice\",\n                                                                        name: \"entries.\".concat(index, \".masterInvoice\"),\n                                                                        type: \"text\",\n                                                                        onBlur: (e)=>{\n                                                                            const masterInvoiceValue = e.target.value;\n                                                                            if (masterInvoiceValue) {\n                                                                                form.setValue(\"entries.\".concat(index, \".invoice\"), masterInvoiceValue);\n                                                                            }\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1390,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice\",\n                                                                        name: \"entries.\".concat(index, \".invoice\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1405,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"BOL\",\n                                                                        name: \"entries.\".concat(index, \".bol\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1412,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1389,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Received Date\",\n                                                                        name: \"entries.\".concat(index, \".receivedDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1420,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Date\",\n                                                                        name: \"entries.\".concat(index, \".invoiceDate\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1428,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Shipment Date\",\n                                                                        name: \"entries.\".concat(index, \".shipmentDate\"),\n                                                                        type: \"text\",\n                                                                        placeholder: \"DD/MM/YYYY\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1436,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1419,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1382,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-4 pb-4 border-b border-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1449,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Financial & Shipment\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1450,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1448,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Total\",\n                                                                        name: \"entries.\".concat(index, \".invoiceTotal\"),\n                                                                        type: \"number\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1455,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".currency\"),\n                                                                        label: \"Currency\",\n                                                                        placeholder: \"Search currency\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"USD\",\n                                                                                label: \"USD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CAD\",\n                                                                                label: \"CAD\"\n                                                                            },\n                                                                            {\n                                                                                value: \"EUR\",\n                                                                                label: \"EUR\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1462,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Savings\",\n                                                                        name: \"entries.\".concat(index, \".savings\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1474,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes\",\n                                                                        name: \"entries.\".concat(index, \".financialNotes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1480,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1454,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Shipped\",\n                                                                        name: \"entries.\".concat(index, \".qtyShipped\"),\n                                                                        type: \"number\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1488,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Weight Unit\",\n                                                                        name: \"entries.\".concat(index, \".weightUnitName\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1494,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_SearchSelect__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        form: form,\n                                                                        name: \"entries.\".concat(index, \".invoiceType\"),\n                                                                        label: \"Invoice Type\",\n                                                                        placeholder: \"Search Invoice Type\",\n                                                                        isRequired: true,\n                                                                        options: [\n                                                                            {\n                                                                                value: \"FREIGHT\",\n                                                                                label: \"FREIGHT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"ADDITIONAL\",\n                                                                                label: \"ADDITIONAL\"\n                                                                            },\n                                                                            {\n                                                                                value: \"BALANCED DUE\",\n                                                                                label: \"BALANCED DUE\"\n                                                                            },\n                                                                            {\n                                                                                value: \"CREDIT\",\n                                                                                label: \"CREDIT\"\n                                                                            },\n                                                                            {\n                                                                                value: \"REVISED\",\n                                                                                label: \"REVISED\"\n                                                                            }\n                                                                        ]\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1501,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Quantity Billed Text\",\n                                                                        name: \"entries.\".concat(index, \".quantityBilledText\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1515,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1487,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 mt-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Invoice Status\",\n                                                                        name: \"entries.\".concat(index, \".invoiceStatus\"),\n                                                                        type: \"text\",\n                                                                        disable: true\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1523,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1530,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1531,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1532,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1522,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1447,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-gray-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1539,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-sm font-semibold text-gray-900\",\n                                                                        children: \"Additional Information\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1540,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1538,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                children: [\n                                                                    (()=>{\n                                                                        var _formValues_entries, _clientOptions_find;\n                                                                        const formValues = form.getValues();\n                                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                        const entryClientId = (entry === null || entry === void 0 ? void 0 : entry.clientId) || formValues.clientId || \"\";\n                                                                        const entryClientName = (clientOptions === null || clientOptions === void 0 ? void 0 : (_clientOptions_find = clientOptions.find((c)=>c.value === entryClientId)) === null || _clientOptions_find === void 0 ? void 0 : _clientOptions_find.name) || \"\";\n                                                                        const isLegrand = entryClientName === \"LEGRAND\";\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: isLegrand ? \"Manual or Matching (Auto-filled)\" : \"Manual or Matching\",\n                                                                            name: \"entries.\".concat(index, \".manualMatching\"),\n                                                                            type: \"text\",\n                                                                            isRequired: true,\n                                                                            disable: isLegrand,\n                                                                            placeholder: isLegrand ? \"Auto-filled based on division\" : \"Enter manual or matching\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1553,\n                                                                            columnNumber: 31\n                                                                        }, undefined);\n                                                                    })(),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Notes (Remarks)\",\n                                                                        name: \"entries.\".concat(index, \".notes\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1564,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Mistake\",\n                                                                        name: \"entries.\".concat(index, \".mistake\"),\n                                                                        type: \"text\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1570,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormCheckboxGroup__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            form: form,\n                                                                            label: \"Documents Available\",\n                                                                            name: \"entries.\".concat(index, \".docAvailable\"),\n                                                                            options: [\n                                                                                {\n                                                                                    label: \"Invoice\",\n                                                                                    value: \"Invoice\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"BOL\",\n                                                                                    value: \"Bol\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"POD\",\n                                                                                    value: \"Pod\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Packages List\",\n                                                                                    value: \"Packages List\"\n                                                                                },\n                                                                                {\n                                                                                    label: \"Other Documents\",\n                                                                                    value: \"Other Documents\"\n                                                                                }\n                                                                            ],\n                                                                            className: \"flex-row gap-2 text-xs\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1577,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1576,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                lineNumber: 1544,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            (()=>{\n                                                                var _formValues_entries;\n                                                                const formValues = form.getValues();\n                                                                const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                                const docAvailable = (entry === null || entry === void 0 ? void 0 : entry.docAvailable) || [];\n                                                                const hasOtherDocuments = docAvailable.includes(\"Other Documents\");\n                                                                return hasOtherDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                        form: form,\n                                                                        label: \"Specify Other Documents\",\n                                                                        name: \"entries.\".concat(index, \".otherDocuments\"),\n                                                                        type: \"text\",\n                                                                        isRequired: true,\n                                                                        placeholder: \"Enter other document types...\",\n                                                                        className: \"max-w-md\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1607,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1606,\n                                                                    columnNumber: 29\n                                                                }, undefined) : null;\n                                                            })()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1537,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    (()=>{\n                                                        var _formValues_entries;\n                                                        const formValues = form.getValues();\n                                                        const entry = (_formValues_entries = formValues.entries) === null || _formValues_entries === void 0 ? void 0 : _formValues_entries[index];\n                                                        const customFields = (entry === null || entry === void 0 ? void 0 : entry.customFields) || [];\n                                                        return Array.isArray(customFields) && customFields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"pt-3 border-t border-gray-100\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-purple-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1634,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"text-sm font-semibold text-gray-900\",\n                                                                            children: [\n                                                                                \"Custom Fields (\",\n                                                                                customFields.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1635,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1633,\n                                                                    columnNumber: 29\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                                                    children: customFields.map((cf, cfIdx)=>{\n                                                                        const fieldType = cf.type || \"TEXT\";\n                                                                        const isAutoField = fieldType === \"AUTO\";\n                                                                        const autoOption = cf.autoOption;\n                                                                        let inputType = \"text\";\n                                                                        if (fieldType === \"DATE\" || isAutoField && autoOption === \"DATE\") {\n                                                                            inputType = \"date\";\n                                                                        } else if (fieldType === \"NUMBER\") {\n                                                                            inputType = \"number\";\n                                                                        }\n                                                                        const fieldLabel = isAutoField ? \"\".concat(cf.name, \" (Auto - \").concat(autoOption, \")\") : cf.name;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_component_FormInput__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            form: form,\n                                                                            label: fieldLabel,\n                                                                            name: \"entries.\".concat(index, \".customFields.\").concat(cfIdx, \".value\"),\n                                                                            type: inputType,\n                                                                            className: \"w-full\",\n                                                                            disable: isAutoField\n                                                                        }, cf.id, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1660,\n                                                                            columnNumber: 35\n                                                                        }, undefined);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1639,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, \"custom-fields-\".concat(index, \"-\").concat(customFieldsRefresh), true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1629,\n                                                            columnNumber: 27\n                                                        }, undefined) : null;\n                                                    })(),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-3 border-t border-gray-100 mt-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-end space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-5 h-5 rounded-full flex items-center justify-center text-white font-bold text-xs cursor-help transition-colors duration-200 \".concat(filenameValidation[index] ? \"bg-green-500 hover:bg-green-600\" : \"bg-orange-500 hover:bg-orange-600\"),\n                                                                                tabIndex: -1,\n                                                                                role: \"button\",\n                                                                                \"aria-label\": \"Entry \".concat(index + 1, \" filename status\"),\n                                                                                children: \"!\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1682,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1681,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                            side: \"top\",\n                                                                            align: \"center\",\n                                                                            className: \"z-[9999]\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm max-w-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"font-medium mb-1\",\n                                                                                        children: [\n                                                                                            \"Entry #\",\n                                                                                            index + 1,\n                                                                                            \" Filename\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1703,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    filenameValidation[index] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"font-medium text-green-600 mb-2\",\n                                                                                                children: \"Filename Generated\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1708,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs font-mono break-all bg-gray-100 p-2 rounded text-black\",\n                                                                                                children: generatedFilenames[index]\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1711,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1707,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"font-medium text-orange-600 mb-1\",\n                                                                                                children: \"Please fill the form to generate filename\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1717,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-xs text-gray-600 mb-2\",\n                                                                                                children: \"Missing fields:\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1720,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                                                className: \"list-disc list-inside space-y-1\",\n                                                                                                children: (_missingFields_index = missingFields[index]) === null || _missingFields_index === void 0 ? void 0 : _missingFields_index.map((field, fieldIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                                        className: \"text-xs\",\n                                                                                                        children: field\n                                                                                                    }, fieldIndex, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                        lineNumber: 1726,\n                                                                                                        columnNumber: 43\n                                                                                                    }, undefined))\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                                lineNumber: 1723,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                        lineNumber: 1716,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1702,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1697,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1680,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    className: \"h-7 w-7 p-0 hover:bg-red-50 hover:border-red-200\",\n                                                                    onClick: ()=>removeEntry(index),\n                                                                    disabled: fields.length <= 1,\n                                                                    tabIndex: -1,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-red-500\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                        lineNumber: 1750,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1741,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                index === fields.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.Tooltip, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipTrigger, {\n                                                                            asChild: true,\n                                                                            tabIndex: -1,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                                type: \"button\",\n                                                                                variant: \"outline\",\n                                                                                size: \"sm\",\n                                                                                className: \"h-7 w-7 p-0 hover:bg-green-50 hover:border-green-200\",\n                                                                                onClick: addNewEntry,\n                                                                                tabIndex: -1,\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_DollarSign_FileText_Hash_Info_MinusCircle_PlusCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                    lineNumber: 1763,\n                                                                                    columnNumber: 35\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1755,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1754,\n                                                                            columnNumber: 31\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_13__.TooltipContent, {\n                                                                            side: \"top\",\n                                                                            align: \"center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-xs\",\n                                                                                children: \"Add New Entry (Shift+Enter)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                                lineNumber: 1767,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                            lineNumber: 1766,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                                    lineNumber: 1753,\n                                                                    columnNumber: 29\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                            lineNumber: 1678,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                        lineNumber: 1677,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                                lineNumber: 1174,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, field.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1160,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            type: \"submit\",\n                                            className: \"px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-md hover:shadow-lg text-sm\",\n                                            children: \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                            lineNumber: 1782,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                        lineNumber: 1781,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                                    lineNumber: 1780,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                            lineNumber: 1154,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                        lineNumber: 1153,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n                lineNumber: 1061,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n            lineNumber: 1060,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\user\\\\trackSheets\\\\createTrackSheet.tsx\",\n        lineNumber: 1059,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateTrackSheet, \"/0hgUI3kB+RCVJ/+JE4ikcrJex0=\", false, function() {\n    return [\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useForm,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useWatch,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_17__.useFieldArray\n    ];\n});\n_c = CreateTrackSheet;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateTrackSheet);\nvar _c;\n$RefreshReg$(_c, \"CreateTrackSheet\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/trackSheets/createTrackSheet.tsx\n"));

/***/ })

});