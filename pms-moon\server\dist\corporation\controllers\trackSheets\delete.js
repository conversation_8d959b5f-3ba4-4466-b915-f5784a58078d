"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteTrackSheets = void 0;
const operation_1 = require("../../../utils/operation");
const deleteTrackSheets = async (req, res) => {
    const id = req.params.id;
    await (0, operation_1.deleteItem)({
        model: "TrackSheets",
        fieldName: "id",
        id: Number(id),
        res: res,
        req: req,
        successMessage: "TrackSheet deleted successfully",
    });
};
exports.deleteTrackSheets = deleteTrackSheets;
//# sourceMappingURL=delete.js.map