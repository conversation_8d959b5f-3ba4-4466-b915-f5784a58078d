"use client";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  VisibilityState,
  getPaginationRowModel,
  PaginationState,
  SortingState,
  getSortedRowModel,
  ColumnFiltersState,
  getFilteredRowModel,
  RowSelectionState,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useContext, useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ChevronLeft, ChevronRight, CloudHail } from "lucide-react";
import { FaFilter, FaSearch } from "react-icons/fa";
import { cn } from "@/lib/utils";
import DeleteMultipleRows from "./DeleteMultipleRows";
// import { rate_routes } from "@/lib/routePath";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { WorkReportContext } from "../pms/manage_work_report/WorkReportContext";
import { TrackerContext } from "../user/tracker/TrackerContext";
import Pagination from "./Pagination";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  isLoading?: boolean;
  showColDropDowns?: boolean;
  filter?: boolean;
  filter1PlaceHolder?: string;
  filter2?: boolean;
  filter3?: boolean;
  filter3view?: JSX.Element;
  total?: boolean;
  totalview?: JSX.Element;
  filter_column?: string;
  filter_column2?: string;
  filter_column3?: string;
  filter_column4?: string;
  overflow?: boolean;
  showPageEntries?: boolean;
  className?: string;
  showSearchColumn?: boolean;
  pageCount?: number;
  isTimerRunning?: any;
  setIsTimerRunning?: any;
  onFilteredDataChange?: (filteredData: TData[]) => void;
}

const DataTable = <TData, TValue>({
  columns,
  data,
  isLoading,
  showColDropDowns,
  filter,
  filter2,
  filter3 = false,
  filter3view,
  total,
  totalview,
  filter_column,
  filter_column2,
  filter_column3,
  filter_column4,
  showPageEntries,
  className,
  filter1PlaceHolder,
  showSearchColumn = true,
  pageCount,
  isTimerRunning,
  setIsTimerRunning,
  onFilteredDataChange,
}: DataTableProps<TData, TValue>) => {
  const [page, setPage] = useState<number>(pageCount || 50);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);

  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: page,
  });
  const { setFromDate, fromDate, toDate, setToDate } =
    useContext(WorkReportContext);
  const { setFDate, fDate, tDate, setTDate } = useContext(TrackerContext);
  const handlePageChange = (e: any) => {
    const newPageSize = parseInt(e.target.value);
    setPage(newPageSize);
    setPagination((prevState) => ({
      ...prevState,
      pageSize: newPageSize,
    }));
  };
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const entriesPerPage = pagination.pageSize;
  const [ids, setIds] = useState<string[]>([]);
  useEffect(() => {
    const selectedIds = Object.keys(rowSelection).filter(
      (key) => rowSelection[key]
    );
    setIds(selectedIds);
  }, [rowSelection, setIds]);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnData, setColumnData] = useState("");
  const [header, setHeader] = useState("");
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  useEffect(() => {
    if (columnData !== "date") {
      setFromDate(""), setToDate("");
      setFDate(""), setTDate("");
    }
  }, [columnData]);

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),

    getFilteredRowModel: getFilteredRowModel(),
    onRowSelectionChange: setRowSelection, //hoist up the row selection state to your own scope

    getRowId: (row, index) => (row as any).rate_cell_id || index.toString(),
    state: {
      columnVisibility,
      pagination,
      sorting,
      columnFilters,
      rowSelection,
    },
    onColumnFiltersChange: setColumnFilters,
  });
  useEffect(() => {
    if (onFilteredDataChange) {
      const filteredRows = table
        .getFilteredRowModel()
        .rows.map((row) => row.original);
      onFilteredDataChange(filteredRows);
    }
  }, [table.getFilteredRowModel().rows, onFilteredDataChange]);
  if (isLoading || !data) {
    return "Loading..";
  }

  const handleColumnSelection = (columnKey: string) => {
    setSelectedColumns((prevSelectedColumns) => {
      if (prevSelectedColumns.includes(columnKey)) {
        return prevSelectedColumns.filter((col) => col !== columnKey);
      }
     
        return [...prevSelectedColumns, columnKey];
      

    });
  };

  const filterColumns = selectedColumns.length
    ? selectedColumns.map((columnKey) =>
        columns.find((col: any) => col.accessorKey === columnKey)
      )
    : [];

  return (
    <div className="animate-in fade-in duration-1000">
      
      <div className=" flex justify-between w-full  lg:w-full lg:flex lg:justify-end mb-3 animate-in fade-in duration-1000 gap-5  ">
        {showPageEntries && (
          <select
            value={page} 
            onChange={handlePageChange}
            className="border rounded-lg !outline-main-color  bg-white"
          >
            {/* <option value={5}>5</option> */}
            <option value={10}>10</option>
            <option value={15}>15</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
            <option value={250}>250</option>
          </select>
        )}
        {showColDropDowns && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <p className="p-2 border-2 rounded-md flex items-center justify-center px-3 cursor-pointer">
                <FaFilter />
              </p>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white border-none   dark:bg-gray-900 dark:ring-1 dark:ring-gray-800" onSelect={(e) => e.preventDefault()}>
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize cursor-pointer"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value: any) =>
                      column.toggleVisibility(!!value)
                    }
                    onSelect={(e) => e.preventDefault()}
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {showSearchColumn && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <p className="p-2 border-2 rounded-md flex items-center justify-center px-3 cursor-pointer ">
                <FaSearch />
              </p>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800 " onSelect={(e) => e.preventDefault()}>
              <div className="px-3 py-2">
                <p className="font-semibold text-sm">Select Columns</p>
                {columns
                  .filter(
                    (item: any) =>
                      item.accessorKey !== "action" &&
                      item.accessorKey !== "Activity Log" &&
                      item.accessorKey !== "Sr. No." &&
                       item.accessorKey !== "permissions"
                  )
                  .map((item: any, id: any) => {
                    return (
                      <DropdownMenuCheckboxItem
                        key={id}
                        checked={selectedColumns.includes(item.accessorKey)}
                        onCheckedChange={() =>
                          handleColumnSelection(item.accessorKey)
                        }
                        className="capitalize cursor-pointer"
                        onSelect={(e) => e.preventDefault()}
                      >
                        {item.header}
                      </DropdownMenuCheckboxItem>
                    );
                  })}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {filterColumns.length > 0 && (
          <>
            {filterColumns.map((column: any, index: number) => {
              
              return column?.accessorKey !== "date" ? (
                <Input
                  key={index}
                  placeholder={` ${column?.header}`}
                  value={
                    table
                      .getColumn(`${column?.accessorKey}`)
                      ?.getFilterValue() as string
                  }
                  onChange={(event) =>
                    table
                      .getColumn(`${column?.accessorKey}`)
                      ?.setFilterValue(event.target.value)
                  }
                  className="w-[20%] dark:bg-gray-700 !outline-main-color"
                />
              ) : (
                <div>{filter3view}</div>
              );
            })}
          </>
        )}
        {/* {columnData && (columnData !== "date" || !filter3) && (
          <>
            <Input
              placeholder={header ? header : filter_column}
              value={
                table.getColumn(`${columnData}`)?.getFilterValue() as string
              }
              onChange={(event) =>
                table
                  .getColumn(`${columnData}`)
                  ?.setFilterValue(event.target.value)
              }
              className="w-[20%] dark:bg-gray-700  !outline-main-color"
            />
          </>
        )} */}

        {filter && (
          <>
            <Input
              placeholder={
                filter1PlaceHolder ? filter1PlaceHolder : filter_column
              }
              value={
                table.getColumn(`${filter_column}`)?.getFilterValue() as string
              }
              onChange={(event) =>
                table
                  .getColumn(`${filter_column}`)
                  ?.setFilterValue(event.target.value)
              }
              className="w-[20%] dark:bg-gray-700  !outline-main-color"
            />
          </>
        )}
        {filter2 && (
          <Input
            placeholder={`${filter_column2}`}
            value={
              table.getColumn(`${filter_column2}`)?.getFilterValue() as string
            }
            onChange={(event) =>
              table
                .getColumn(`${filter_column2}`)
                ?.setFilterValue(event.target.value)
            }
            className="w-[20%] dark:bg-gray-700 !outline-main-color"
          />
        )}
        {/* {selectedColumns.includes("date") && filter3 && (
          <div>{filter3view}</div>
        )} */}
        {/* {ids.length > 0 && (
          <>
            <DeleteMultipleRows
              selectedIds={ids}
              path={rate_routes.DELETE_MULTIPLE_RATES}
              revPath="/"
              setIds={setIds}
              setRowSelection={setRowSelection}
            />
          </>
        )} */}
      </div>
      {total && (
        <div>
          {/*   className="mt-4 bg-gray-100 border border-gray-300 rounded-lg text-center w-60 py-2 flex flex-col justify-center items-center" */}
          {totalview}
        </div>
      )}
      <div className={cn("relative  sm:w-full  cbar ", className)}>
        <Table>
          <TableHeader className="">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className=" text-sm p-2">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="">
            {table.getRowModel()?.rows?.length ? (
              table.getRowModel().rows.map((row, index) => {
                const hasEmailField = row.getVisibleCells().some(cell => cell.column.columnDef.cell=== 'email');
                return (
                  <TableRow
                    key={row.id}
                    data-state={row.getIsSelected() && "selected"}
                    className={`${hasEmailField} ? "" :"capitalize" rounded-xl` }
                  >
                    {row.getVisibleCells().map((cell) => {
                      return (
                        <TableCell key={cell.id} className="text-xs ">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center   dark:bg-gray-700"
                >
                  No Data Found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {/* <div className="flex items-center justify-end space-x-2 p-0.5 ">
        <p className="text-sm">
          Page {table?.getState().pagination.pageIndex + 1} of{" "}
          {table?.getPageCount()}
        </p>
        <Button
          size="sm"
          onClick={() => table.previousPage()}
          disabled={!table.getCanPreviousPage()}
          className="tracking-wider text-gray-800 bg-white ring-gray-300 hover:bg-gray-50 dark:bg-gray-700 dark:ring-gray-600 dark:text-white !p-1"
        >
          <ChevronLeft />
        </Button>
        <Button
          size="sm"
          onClick={() => table.nextPage()}
          disabled={!table.getCanNextPage()}
          className="tracking-wider text-gray-800 bg-white ring-gray-300 hover:bg-gray-50 dark:bg-gray-700 dark:ring-gray-600 dark:text-white !p-1"
        >
          <ChevronRight />
        </Button>
      </div> */}

      <Pagination
        currentPage={table.getState().pagination.pageIndex + 1}
        totalPages={table.getPageCount()}
        onPageChange={(page: any) => table.setPageIndex(page - 1)}
        entriesPerPage={table.getState().pagination.pageSize}
      />
    </div>
  );
};

export default DataTable;